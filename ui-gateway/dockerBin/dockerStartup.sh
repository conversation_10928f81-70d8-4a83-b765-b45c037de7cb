#!/bin/bash

AppName=uig
INST_HOME=${INST_HOME:-"/integral/app/$AppName"}
LOG_HOME=${LOG_HOME:-"/integral/logs/$AppName"}
CRONOLOG_HOME=/usr/sbin
LOGFILE=${LOG_HOME}/integral.out.`date +%Y-%m-%d-%H`

JAVA_HOME=/integral/opt/jdk18

Version=`grep Integral.releaseNumber $INST_HOME/conf/Version.properties | cut -d"=" -f2`
BuildNum=`grep Integral.buildNumber $INST_HOME/conf/Version.properties | cut -d"=" -f2`

$INST_HOME/bin/update_propertie.sh

JAVA_OPTIONS="-server -Xms${MEM}m -Xmx${MEM}m -XX:MaxMetaspaceSize=500m -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-OmitStackTraceInFastThrow -XX:+PrintGCTimeStamps -XX:+PrintAdaptiveSizePolicy -XX:+ParallelRefProcEnabled -XX:+UseG1GC -XX:MaxGCPauseMillis=50 -Djava.net.preferIPv4Stack=true -Dvs=@VSNAME@ -DvsType=@VSTYPE@"
LOGGING_CONFIG="-Djava.util.logging.config.file=/integral/app/uig/conf/local/logging.properties"
LOGGING_MANAGER="-Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager"
JAVA_OPTIONS="$JAVA_OPTIONS $LOGGING_MANAGER $LOGGING_CONFIG"

# VERSION comes from build.gradle 
CLASSPATH=/integral/app/uig/conf/:/integral/app/uig/lib/ui-gateway-@VERSION@.jar

echo =============================================================================== >> $LOGFILE
echo . >> $LOGFILE
echo   Start UIGateway Server - uig-$Version-$BuildNum   >> $LOGFILE
echo . >> $LOGFILE
echo   MEM: $MEM >> $LOGFILE
echo . >> $LOGFILE
echo   JAVA_HOME: $JAVA_HOME >> $LOGFILE
echo . >> $LOGFILE
echo   JAVA_OPTIONS: $JAVA_OPTIONS >> $LOGFILE
echo . >> $LOGFILE
echo   CLASSPATH: $CLASSPATH >> $LOGFILE
echo . >> $LOGFILE
echo =============================================================================== >> $LOGFILE
echo . >> $LOGFILE

cd $LOG_HOME
if [ -L integral.out ]; then
    unlink integral.out
elif [ -f integral.out ]; then
    rm -f integral.out
fi

$JAVA_HOME/bin/java $JAVA_OPTIONS -classpath "$CLASSPATH" -Dloader.main=com.integral.uigateway.UiGatewayApplication org.springframework.boot.loader.PropertiesLauncher  2>&1 | $CRONOLOG_HOME/cronolog integral.out.%Y-%m-%d-%H -S integral.out

