#!/bin/bash

AppName=uig
INST_HOME=${INST_HOME:-"/integral/app/$AppName"}
PROG=UiGatewayApplication

pid=`ps -ef | grep "$INST_HOME/" | grep java | grep -v "grep " | awk -F " " '{print $2}' `
if [ "x$pid" = "x" ]; then
    echo "$INST_HOME is not running"
    exit 0
fi
echo "Take thread dump before shutdown the server"
kill -3 $pid
echo "Shutdown the server, pid=$pid, please wait ... "
kill -15 $pid
j=0
while [[ $j -lt 10 ]]; do
    sleep 2
    pid=`ps -ef | grep java | grep "$INST_HOME/" | grep -v "grep" | awk -F " " '{print $2}'`
    if [ "x$pid" = "x" ]; then
       j=11
    fi
    let j++
done

pid=`ps -ef | grep java | grep "$INST_HOME/" | grep -v "grep" | awk -F " " '{print $2}'`
if [ "x$pid" != "x" ]; then
    echo "Not fully stop, need to force killing the process $pid"
    kill -9 $pid
fi
