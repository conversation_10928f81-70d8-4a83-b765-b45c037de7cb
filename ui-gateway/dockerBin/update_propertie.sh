#!/bin/bash

INST_HOME=/integral/app/uig
File=$INST_HOME/conf/hostname.properties

sed -i s/@ora.db.server@/$DBSERVER/g $File
sed -i s/@ora.db.port@/$DBPORT/g $File
sed -i s/@ora.db.instance@/$DBNAME/g $File
sed -i s/@user.name.prefix@/$DBUSER/g $File
sed -i s/@user.password@/$DBPASSWD/ $File
sed -i s/@virtual.server.name@/$VSNAME/g $File
sed -i s/@virtual.server.group@/$VSGROUP/g $File
sed -i s/@mongo.url@/$MONGO_URL/g $File
sed -i s/@rabbitmq.url@/$RABBITMQ_URL/g $File
sed -i s/@sonic.host.name@/$SONIC_HOST/g $File
sed -i s/@sonic.tcp.port@/$SONIC_PORT/g $File
sed -i s/@sonic.http.port@/$SONIC_PORT/g $File
sed -i s/@user.name@/$DBUSER/g $File
sed -i s/@jms.user.name@/Administrator/g $File
sed -i s/@jms.password@/Administrator/g $File
sed -i s/@jmsproxy.port@/$JMS_PORT/g $File
sed -i s#@rds.url@#$RDS_URL#g $File
sed -i s/@uig.port@/$HTTP_PORT/g $File
#
sed -i s/@uig.port@/$HTTP_PORT/ $INST_HOME/conf/application.properties
#
sed -i s/@consul.ip@/$CONSUL_IP/ $INST_HOME/conf/application.yml
sed -i s/@consul.port@/$CONSUL_PORT/ $INST_HOME/conf/application.yml
sed -i s/\${random.value}/$ENVIRONMENT/ $INST_HOME/conf/application.yml
sed -i s/\${spring.application.name}/$VSNAME/ $INST_HOME/conf/application.yml

