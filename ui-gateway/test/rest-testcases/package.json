{"name": "uig-test", "version": "1.0.0", "description": "Javascript test code for UIG", "main": "app.js", "dependencies": {"axios": "^0.24.0", "chai": "^4.3.4", "describe": "^1.2.0", "lodash": "^4.17.21", "mocha": "^9.1.1", "mocha-junit-reporter": "^2.0.2", "winston": "^3.9.0"}, "scripts": {"test": "mocha --timeout 20000 --reporter mocha-junit-reporter", "test-local": "mocha --timeout 20000"}, "keywords": ["uig", "REST"], "author": "rejeev", "license": "Integral"}