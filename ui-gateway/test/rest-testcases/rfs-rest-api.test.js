const assert = require('chai').assert
const axios = require('axios');
axios.defaults.withCredentials = true;
const _ = require('lodash');
const env = require('../config/properties').env;
const util = require('./util');
const baseUrl = env.uigServer;
const apiEndpoint = baseUrl + '/rfs';
const endpointPrefix = apiEndpoint + '/';
const loginUrl = baseUrl + '/sso/login';
const winston = require('winston');
const {error} = require("winston");
const log_level = process.env.LOG_LEVEL ? process.env.LOG_LEVEL : 'info';
const logger = winston.createLogger({
    level: log_level,
    format: winston.format.simple(),
    transports: [new winston.transports.Console()],
});

describe('Suite1', function () {
    before(async function () {
      try {
        let reqBody = {
          user: env.user, pass: env.pass, org: env.org
        };
        logger.debug(`Login to ${loginUrl} with ${JSON.stringify(reqBody)}`);
        let response = await axios.post(loginUrl, reqBody, {
          headers: {CLIENT_TYPE: 'BROWSER'}
        });
        logger.debug(`Login Response: ${JSON.stringify(response.data)}`);
        let cookies = response.headers["set-cookie"];
        axios.defaults.headers.Cookie = cookies;
      }catch (err){
          logger.error(err);
      }
    })
    
    describe('SubSuite1', function () {
        it('Trade workflow', async function () {
            try {
                let clOrderId = util.randomString(10);
                let subReq = util.getSubscriptionReq(clOrderId);
                logger.debug(`Test Trade workflow: ${apiEndpoint}, req=${JSON.stringify(subReq)}`);
                let res = await axios.post(apiEndpoint, subReq);
                logger.debug(`Test Trade workflow: res=${JSON.stringify(res.data)}`);
                let requestId = res.data.requestId;
                let quoteId = await getQuoteId(requestId, 1000);
                if (!quoteId) {
                    logger.debug(`Test Trade workflow failed - No quote available`);
                    assert.fail("No quote available");
                }
                let trdReq = util.getTradeReq(clOrderId, quoteId);
                logger.debug(`Trade workflow: trade req=${JSON.stringify(trdReq)}`);
                await axios.post(endpointPrefix + requestId, trdReq);
                let tradeId = await getTradeId(requestId, 1000);
                if (!tradeId) {
                    logger.debug(`Test Trade workflow failed. Trade not verified`);
                    assert.fail("No trade available");
                } else {
                    logger.debug(`Test Trade workflow passed.`);
                }
            }catch(err){
                logger.error("exception during test ", err);
                assert.fail("Exception during test");
            }
        });

        it('Expiry flow', async function () {
            try {
                let clOrderId = util.randomString(10);
                let subReq = util.getSubscriptionReq(clOrderId);
                subReq.expiry = 2;
                let res = await axios.post(apiEndpoint, subReq);
                let requestId = res.data.requestId;
                await util.sleep(2000);
                let response = await axios.get(endpointPrefix + requestId);
                let event = _.get(response, 'data.event');
                assert.equal(event, 'REQUEST_EXPIRED');
            }catch (err){
                logger.error("Exception during test", err);
                assert.fail("Exception during test");
            }
        });

        it('Withdraw flow', async function () {
            try {
                let clOrderId = util.randomString(10);
                let subReq = util.getSubscriptionReq(clOrderId);
                let res = await axios.post(apiEndpoint, subReq);
                let requestId = res.data.requestId;
                let quoteId = await getQuoteId(requestId, 1000);
                await axios.delete(endpointPrefix + requestId);
                await util.sleep(100);
                res = await axios.get(endpointPrefix + requestId);
                assert.equal(_.get(res, 'data.event'), 'REQUEST_WITHDRAWN');
            }catch (err){
                logger.error("Exception during test", err);
                assert.fail("Exception during test");
            }
        });

        it('RFS Failed flow', async function () {
            try {
                let clOrderId = util.randomString(10);
                let subReq = util.getSubscriptionReq(clOrderId);
                subReq.symbol = 'EUR/XYZ';
                subReq.dealtCurrency = 'EUR';
                let res = await axios.post(apiEndpoint, subReq);
                let requestId = res.data.requestId;
                await util.sleep(100);
                res = await axios.get(endpointPrefix + requestId);
                let event = _.get(res, 'data.event');
                assert.equal(event, 'REQUEST_DECLINED');
            }catch (err){
                logger.error("Exception during test", err);
                assert.fail("Exception during test");
            }
        });
        
        it('Inactive provider flow', async function () {
            try {
                let clOrderId = util.randomString(10);
                let subReq = util.getSubscriptionReq(clOrderId);
                subReq.providers = ['0001FIXFE'];
                let res = await axios.post(apiEndpoint, subReq);
                let requestId = res.data.requestId;
                await util.sleep(100);
                res = await axios.get(endpointPrefix + requestId);
                let event = _.get(res, 'data.event');
                assert.equal(event, 'REQUEST_EXPIRED');
            }catch (err){
                logger.error("Exception during tst", err);
                assert.fail("Exceptoin during test");
            }
        });
    })
})

async function getTradeId(requestId, timeout){
    let interval = timeout / 10;
    let counter = 0;
    while (counter++ < 10) {
        await util.sleep(interval);
        let response = await axios.get(endpointPrefix + requestId);
        logger.debug(`Trade Res=${JSON.stringify(response.data)}`);
        let tradeId = _.get(response, 'data.trades[0].tradeId');
        if(tradeId) return tradeId;
    }
    return null;
}

async function getQuoteId(requestId, timeout){
    let interval = timeout / 10;
    let counter = 0;
    while (counter++ < 10) {
        await util.sleep(interval);
        let response = await axios.get(endpointPrefix + requestId);
        logger.debug(`Get QuoteId: ${JSON.stringify(response.data)}`);
        let quoteId = _.get(response, 'data.quotes[0].bids[0].quoteId');
        if(quoteId) return quoteId;
    }
    return null;
}


