function getSubscriptionReq(clOrderId) {
    return {
        "symbol": "EUR/USD",
        "amount": 10000.0,
        "dealtCurrency": "EUR",
        "expiry": 30,
        "nearValueDate": "Spot",
        "side": "TWO_WAY",
        "priceType": "Spot",
        "customerAccount": "pfOrg",
        "customerOrg": "pfOrg",
        "clOrderId": clOrderId
    }
}

function getTradeReq(clOrderId, quoteId) {
    return {
        "clOrderId": clOrderId,
        "quoteId": quoteId,
        "side": "BUY",
        "dealtCurrency": "EUR",
        "symbol": "EUR/USD"
    }
}

async function sleep(msec) {
    return new Promise(resolve => setTimeout(resolve, msec));
}

function randomString(length) {
    var result           = '';
    var characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for ( var i = 0; i < length; i++ ) {
      result += characters.charAt(Math.floor(Math.random() * 
 charactersLength));
   }
   return result;
}

module.exports = {
    getSubscriptionReq: getSubscriptionReq,
    getTradeReq: getTradeReq,
    sleep: sleep,
    randomString: randomString,
}