const assert = require('chai').assert
const expect = require('chai').expect
const WebSocket = require('ws')

//const login = require('../login').login
const env = require('../../config/properties').env
const user = require('../../config/properties').user
const mdData = require('../../config/properties').mdData
const orderData = require('../../config/properties').orderData

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId
let tempReqId
let systemReqId
let withdrawResponse
let inactiveQuote
let activeQuote
let subscriptionAck
let withdrawAck
let res
let errors
let Host
let apikey

let wsconnect = function (done, cookies) {
    const websocket_url = 'wss://' + env.hostname +  '/v2/fxstream'
    connection = new WebSocket(websocket_url, [], {
        'headers': {
			'Host': env.apiHost,
			'apikey': env.apikey
        }
    })

    connection.onopen = () => {

        done()
    }

    connection.onerror = (error) => {
        console.log(`WebSocket error: ${error}`)
    }
}

let streamingOutrightOrderTC = function(){

    describe("Streaming Outright Orders", function () {

        before(function (done) {
            tempReqId = ""
            wsconnect(done);
        });

        after(function () {
            connection.close()
        });
        let reqId = Math.floor(Math.random() * 1000)

        describe("SOR Subscription test ", function () {
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Subscription Rate test ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_" + reqId
                console.log("Streaming Outright - Subscription Rate Test -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Rate Test ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Rate Test ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                    }
                }
            });

            it("Subscription test", function () {
                if(rateSubscriptionResponses !== "") {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Rate Test -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal('EUR/USD', rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }
                if (activeQuote !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(activeQuote)

                    assert.equal(mdData.SOR_Symbol_EURUSD, activeQuote.symbol)
                    assert.exists(activeQuote.bid)
                    assert.notEqual(null,activeQuote.bidLimit,"bidlimit is null")
                    assert.exists(activeQuote.offer)
                    assert.exists(activeQuote.bidLimit)
                    assert.exists(activeQuote.offerLimit)
                    assert.exists(activeQuote.time)
                    assert.equal(tempReqId, activeQuote.requestId)
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }

                if(rateUnsubscriptionResponses != "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rateUnsubscriptionResponses : ")
                    console.log(rateUnsubscriptionResponses)

                    assert.equal(mdData.typeVwap, rateUnsubscriptionResponses.request.type)
                    assert.equal(tempReqId, rateUnsubscriptionResponses.request.requestId)
                    assert.equal(true, rateUnsubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateUnsubscriptionResponses.request.depth)
                    assert.equal("success", rateUnsubscriptionResponses.status)
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR BuyBase Order test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order - Buy Base test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_BuyBase_" + reqId
                console.log("Streaming Outright Order Test -> reqId = " + reqId)
//{ "orders": [{ "coId":"SOR_Order15", "type":"Market", "side":"Sell", "symbol":"EUR/USD",
 //"currency":"USD", "size":1000000, "price":1.05511, "timeInForce":"GTC", "tenor":"1W" }]}
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,//["NTFX"],
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order ->  Buy Base test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order - Buy Base test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        }
                    }
                }
            });

            it("SOR BuyBase Order test", function () {
                if (RECEIVED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(RECEIVED)
//{"orderResponses":[{"coId":"StreamingOR_Order_BuyBase_75","type":"Market","timeInForce":"GTT","side":"Buy","currency":"EUR","
//symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":30,"clientOrderTime":"Aug 18, 2023 3:13:05 PM","tradeChannel":"API/WS/ESP",
//"preferredProviders":["NTFX"],"tenor":"1W","userFullName":"user1@pfOrg","action":"place","status":"RECEIVED"}]}

                    assert.exists(RECEIVED.coId)
                    assert.notEqual(null,RECEIVED.coId,"coId is null")
                    assert.equal(orderData.MARKET, RECEIVED.type)
                    assert.equal(orderData.GTT, RECEIVED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, RECEIVED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, RECEIVED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,RECEIVED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, RECEIVED.size,"size is not correct")
                    assert.equal( user.orgname, RECEIVED.org,"org is not correct")
                    assert.equal(orderData.ExpiryTime, RECEIVED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"expiryTime is not present")
                    assert.exists(RECEIVED.clientOrderTime,"clientOrderTime is not present")
                    assert.exists(RECEIVED.tradeChannel,"tradeChannel is not present")
                    assert.exists(RECEIVED.preferredProviders,"preferredProviders is not present")
                    assert.exists(RECEIVED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, RECEIVED.tenor,"tenor is not correct")
                    assert.equal( user.username+"@"+ user.orgname, RECEIVED.userFullName,"userFullName is not correct")
					assert.equal("place", RECEIVED.action,"action is not correct")
					assert.equal("RECEIVED", RECEIVED.status,"status is not correct")
					assert.equal("FORWARD", RECEIVED.settlementType,"status is not correct")
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }


               if (PENDING_NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(PENDING_NEW)
//Streaming Outright Order - Buy Base test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_BuyBase_45","type":"Market","timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"NONE","lastPrice":0,"lastQty":0,"orderId":"NONE","executionType":"PENDING_NEW","leavesQty":1000000,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"PENDING_NEW"}]}

                    assert.exists(PENDING_NEW.coId)
                    assert.notEqual(null,PENDING_NEW.coId,"coId is null")
                    assert.equal(orderData.MARKET, PENDING_NEW.type)
                    assert.equal(orderData.GTT, PENDING_NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, PENDING_NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, PENDING_NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,PENDING_NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
					assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
                    assert.equal( user.orgname, PENDING_NEW.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, PENDING_NEW.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(PENDING_NEW.execFlags,"execFlags doesnt exist")
					assert.exists(PENDING_NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, PENDING_NEW.spotRate,"spotRate is not correct")
					assert.exists(PENDING_NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, PENDING_NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(PENDING_NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, PENDING_NEW.averagePrice,"averagePrice is not correct")
					assert.exists(PENDING_NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, PENDING_NEW.cumQty,"cumQty is not correct")
					assert.exists(PENDING_NEW.execId,"execId doesnt exist")
					assert.exists(PENDING_NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, PENDING_NEW.lastPrice,"lastPrice is not correct")
					assert.exists(PENDING_NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, PENDING_NEW.lastQty,"lastQty is not correct")
					assert.equal("NONE", PENDING_NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, PENDING_NEW.leavesQty,"leavesQty is not correct")
					assert.exists(PENDING_NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, PENDING_NEW.userFullName,"userFullName is not correct")
					assert.equal("place", PENDING_NEW.action,"action is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied PENDING_NEW message")
                    assert.equal(true,false)
                }

               if (NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(NEW)
//Streaming Outright Order - Buy Base test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_BuyBase_45","type":"Market","timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"NONE","lastPrice":0,"lastQty":0,"orderId":"NONE","executionType":"NEW","leavesQty":1000000,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"NEW"}]}

                    assert.exists(NEW.coId)
                    assert.notEqual(null,NEW.coId,"coId is null")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, NEW.size,"size is not correct")
                    assert.equal( user.orgname, NEW.org,"org is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, NEW.expiryTime,"expiryTime is not correct")
					assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(NEW.execFlags,"execFlags doesnt exist")
					assert.exists(NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, NEW.spotRate,"spotRate is not correct")
					assert.exists(NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, NEW.averagePrice,"averagePrice is not correct")
					assert.exists(NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, NEW.cumQty,"cumQty is not correct")
					assert.exists(NEW.execId,"execId doesnt exist")
					assert.exists(NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, NEW.lastPrice,"lastPrice is not correct")
					assert.exists(NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, NEW.lastQty,"lastQty is not correct")
					assert.notEqual(null, NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("NEW", NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, NEW.leavesQty,"leavesQty is not correct")
					assert.exists(NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, NEW.userFullName,"userFullName is not correct")
					assert.equal("place", NEW.action,"action is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied NEW message")
                    assert.equal(true,false)
                }
				   if (FILLED !== "") {
						console.log("Streaming Outright - Subscription Rate Test -> rate : ")
						console.log(FILLED)
	//{"orderResponses":[{"coId":"StreamingOR_Order_BuyBase_45","type":"Market","timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"averagePrice":1.05491,"cumQty":1000000,"execId":"FXI9361341163","lastPrice":1.05491,"lastQty":1000000,"orderId":"**********","valueDate":"********","tradeDate":"********","settlCurrAmt":1054910,"executionType":"TRADE","leavesQty":0,"counterParty":"NTFX","counterPartyAccount":"NTFXle","userFullName":"user1@pfOrg","transactTime":*************,"action":"place","status":"FILLED"}]}


						assert.exists(FILLED.coId)
						assert.notEqual(null,FILLED.coId,"coId is null")
						assert.equal(orderData.MARKET, FILLED.type)
						assert.equal(orderData.GTT, FILLED.timeInForce,"timeInForce is not correct")
						assert.equal(orderData.BUY, FILLED.side,"side is not correct")
						assert.equal(mdData.SOR_baseCurrency_EURUSD, FILLED.currency,"currency is not correct")
						assert.equal(mdData.SOR_Symbol_EURUSD,FILLED.symbol,"symbol is not correct")
						assert.equal(orderData.size, FILLED.size,"size is not correct")
						assert.equal( user.orgname, FILLED.org,"org is not correct")
						// has to be uncommented after fixing expiryTime format
						//assert.equal(orderData.ExpiryTime, FILLED.expiryTime,"expiryTime is not correct")
						assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

						assert.exists(FILLED.execFlags,"execFlags doesnt exist")
						assert.exists(FILLED.spotRate,"spotRate doesnt exist")
						assert.notEqual(0, FILLED.spotRate,"spotRate is zero")
						assert.exists(FILLED.forwardPoints,"forwardPoints doesnt exist")
						assert.notEqual(0, FILLED.forwardPoints,"forwardPoints is zero")
						assert.exists(FILLED.averagePrice,"averagePrice doesnt exist")
						assert.notEqual(0, FILLED.averagePrice,"averagePrice is 0")
						assert.exists(FILLED.cumQty,"cumQty doesnt exist")
						assert.equal(orderData.size, FILLED.cumQty,"cumQty is not correct")
						assert.exists(FILLED.execId,"execId doesnt exist")
						assert.exists(FILLED.lastPrice,"lastPrice doesnt exist")
						assert.notEqual(0, FILLED.lastPrice,"lastPrice is zero")
						assert.exists(FILLED.lastQty,"lastQty doesnt exist")
						assert.equal(orderData.size, FILLED.lastQty,"lastQty is not correct")
						assert.notEqual(null, FILLED.orderId,"orderId is not correct")
						assert.exists(FILLED.valueDate,"valueDate doesnt exist")
						assert.exists(FILLED.tradeDate,"tradeDate doesnt exist")
						assert.exists(FILLED.settlCurrAmt,"settlCurrAmt doesnt exist")
						amt = FILLED.size*FILLED.averagePrice
						assert.equal(FILLED.size*FILLED.averagePrice, FILLED.settlCurrAmt,"settlCurrAmt is not correct")
						assert.equal("TRADE", FILLED.executionType,"executionType is not correct")
						assert.equal(0, FILLED.leavesQty,"leavesQty is not correct")
						assert.exists(FILLED.counterParty,"counterParty doesnt exist")
						assert.exists(FILLED.counterPartyAccount,"counterPartyAccount doesnt exist")
						assert.equal( user.username+"@"+ user.orgname, FILLED.userFullName,"userFullName is not correct")
						assert.exists(FILLED.transactTime,"transactTime doesnt exist")
						assert.equal("place", FILLED.action,"action is not correct")
						assert.equal("FILLED", FILLED.status,"status is not correct")

					} else {
						console.log("Streaming Outright -   Subscription Rate test -> Not receied FILLED message")
						assert.equal(true,false)
					}


           });

        });

        describe("SOR SellBase Order test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order - Buy Base test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_SellBase_" + reqId
                console.log("Streaming Outright Order Test -> reqId = " + reqId)
//{ "orders": [{ "coId":"SOR_Order15", "type":"Market", "side":"Sell", "symbol":"EUR/USD",
 //"currency":"USD", "size":1000000, "price":1.05511, "timeInForce":"GTC", "tenor":"1W" }]}
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.SELL,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order ->  SELL Base test : request : ")
				console.log(wsreq)
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order - SELL Base test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        }
                    }
                }
            });

            it("SOR SellBase Order test", function () {
                if (RECEIVED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(RECEIVED)
//{"orderResponses":[{"coId":"StreamingOR_Order_SellBase_75","type":"Market","timeInForce":"GTT","side":"SELL","currency":"EUR","
//symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":30,"clientOrderTime":"Aug 18, 2023 3:13:05 PM","tradeChannel":"API/WS/ESP",
//"preferredProviders":["NTFX"],"tenor":"1W","userFullName":"user1@pfOrg","action":"place","status":"RECEIVED"}]}

                    assert.exists(RECEIVED.coId)
                    assert.notEqual(null,RECEIVED.coId,"coId is null")
                    assert.equal(orderData.MARKET, RECEIVED.type)
                    assert.equal(orderData.GTT, RECEIVED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, RECEIVED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, RECEIVED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,RECEIVED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, RECEIVED.size,"size is not correct")
                    assert.equal( user.orgname, RECEIVED.org,"org is not correct")
                    assert.equal(orderData.ExpiryTime, RECEIVED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"expiryTime is not present")
                    assert.exists(RECEIVED.clientOrderTime,"clientOrderTime is not present")
                    assert.exists(RECEIVED.tradeChannel,"tradeChannel is not present")
                    assert.exists(RECEIVED.preferredProviders,"preferredProviders is not present")
                    assert.exists(RECEIVED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, RECEIVED.tenor,"org is not correct")
                    assert.equal( user.username+"@"+ user.orgname, RECEIVED.userFullName,"userFullName is not correct")
					assert.equal("place", RECEIVED.action,"action is not correct")
					assert.equal("RECEIVED", RECEIVED.status,"status is not correct")
					assert.equal("FORWARD", RECEIVED.settlementType,"status is not correct")
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }


               if (PENDING_NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(PENDING_NEW)
//Streaming Outright Order - SELL Base test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_SellBase_45","type":"Market","timeInForce":"GTT","side":"SELL","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"NONE","lastPrice":0,"lastQty":0,"orderId":"NONE","executionType":"PENDING_NEW","leavesQty":1000000,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"PENDING_NEW"}]}

                    assert.exists(PENDING_NEW.coId)
                    assert.notEqual(null,PENDING_NEW.coId,"coId is null")
                    assert.equal(orderData.MARKET, PENDING_NEW.type)
                    assert.equal(orderData.GTT, PENDING_NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, PENDING_NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, PENDING_NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,PENDING_NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
                    assert.equal( user.orgname, PENDING_NEW.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, PENDING_NEW.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(PENDING_NEW.execFlags,"execFlags doesnt exist")
					assert.exists(PENDING_NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, PENDING_NEW.spotRate,"spotRate is not correct")
					assert.exists(PENDING_NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, PENDING_NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(PENDING_NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, PENDING_NEW.averagePrice,"averagePrice is not correct")
					assert.exists(PENDING_NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, PENDING_NEW.cumQty,"cumQty is not correct")
					assert.exists(PENDING_NEW.execId,"execId doesnt exist")
					assert.exists(PENDING_NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, PENDING_NEW.lastPrice,"lastPrice is not correct")
					assert.exists(PENDING_NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, PENDING_NEW.lastQty,"lastQty is not correct")
					assert.equal("NONE", PENDING_NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, PENDING_NEW.leavesQty,"leavesQty is not correct")
					assert.exists(PENDING_NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, PENDING_NEW.userFullName,"userFullName is not correct")
					assert.equal("place", PENDING_NEW.action,"action is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied PENDING_NEW message")
                    assert.equal(true,false)
                }

               if (NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(NEW)
//Streaming Outright Order - SELL Base test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_SellBase_45","type":"Market","timeInForce":"GTT","side":"SELL","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"NONE","lastPrice":0,"lastQty":0,"orderId":"NONE","executionType":"NEW","leavesQty":1000000,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"NEW"}]}

                    assert.exists(NEW.coId)
                    assert.notEqual(null,NEW.coId,"coId is null")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, NEW.size,"size is not correct")
                    assert.equal( user.orgname, NEW.org,"org is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, NEW.expiryTime,"expiryTime is not correct")
					assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(NEW.execFlags,"execFlags doesnt exist")
					assert.exists(NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, NEW.spotRate,"spotRate is not correct")
					assert.exists(NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, NEW.averagePrice,"averagePrice is not correct")
					assert.exists(NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, NEW.cumQty,"cumQty is not correct")
					assert.exists(NEW.execId,"execId doesnt exist")
					assert.exists(NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, NEW.lastPrice,"lastPrice is not correct")
					assert.exists(NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, NEW.lastQty,"lastQty is not correct")
					assert.notEqual(null, NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("NEW", NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, NEW.leavesQty,"leavesQty is not correct")
					assert.exists(NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, NEW.userFullName,"userFullName is not correct")
					assert.equal("place", NEW.action,"action is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied NEW message")
                    assert.equal(true,false)
                }
               if (FILLED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(FILLED)
//{"orderResponses":[{"coId":"StreamingOR_Order_SellBase_45","type":"Market","timeInForce":"GTT","side":"SELL","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"averagePrice":1.05491,"cumQty":1000000,"execId":"FXI9361341163","lastPrice":1.05491,"lastQty":1000000,"orderId":"**********","valueDate":"********","tradeDate":"********","settlCurrAmt":1054910,"executionType":"TRADE","leavesQty":0,"counterParty":"NTFX","counterPartyAccount":"NTFXle","userFullName":"user1@pfOrg","transactTime":*************,"action":"place","status":"FILLED"}]}


                    assert.exists(FILLED.coId)
                    assert.notEqual(null,FILLED.coId,"coId is null")
                    assert.equal(orderData.MARKET, FILLED.type)
                    assert.equal(orderData.GTT, FILLED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, FILLED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FILLED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,FILLED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FILLED.size,"size is not correct")
                    assert.equal( user.orgname, FILLED.org,"org is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, FILLED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")
                    assert.exists(FILLED.execFlags,"execFlags doesnt exist")
                    assert.exists(FILLED.spotRate,"spotRate doesnt exist")
                    assert.notEqual(0, FILLED.spotRate,"spotRate is zero")
                    assert.exists(FILLED.forwardPoints,"forwardPoints is zero")
                    assert.notEqual(0, FILLED.forwardPoints,"forwardPoints is not correct")
                    assert.exists(FILLED.averagePrice,"averagePrice doesnt exist")
                    assert.notEqual(0, FILLED.averagePrice,"averagePrice is 0")
                    assert.exists(FILLED.cumQty,"cumQty doesnt exist")
                    assert.equal(orderData.size, FILLED.cumQty,"cumQty is not correct")
                    assert.exists(FILLED.execId,"execId doesnt exist")
                    assert.exists(FILLED.lastPrice,"lastPrice doesnt exist")
                    assert.notEqual(0, FILLED.lastPrice,"lastPrice is zero")
                    assert.exists(FILLED.lastQty,"lastQty doesnt exist")
                    assert.equal(orderData.size, FILLED.lastQty,"lastQty is not correct")
                    assert.notEqual(null, FILLED.orderId,"orderId is not correct")
                    assert.exists(FILLED.valueDate,"valueDate doesnt exist")
                    assert.exists(FILLED.tradeDate,"tradeDate doesnt exist")
                    assert.exists(FILLED.settlCurrAmt,"settlCurrAmt doesnt exist")
                    amt = FILLED.size*FILLED.averagePrice
                    assert.equal(FILLED.size*FILLED.averagePrice, FILLED.settlCurrAmt,"settlCurrAmt is not correct")
                    assert.equal("TRADE", FILLED.executionType,"executionType is not correct")
                    assert.equal(0, FILLED.leavesQty,"leavesQty is not correct")
                    assert.exists(FILLED.counterParty,"counterParty doesnt exist")
                    assert.exists(FILLED.counterPartyAccount,"counterPartyAccount doesnt exist")
                    assert.equal( user.username+"@"+ user.orgname, FILLED.userFullName,"userFullName is not correct")
                    assert.exists(FILLED.transactTime,"transactTime doesnt exist")
                    assert.equal("place", FILLED.action,"action is not correct")
                    assert.equal("FILLED", FILLED.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied FILLED message")
                    assert.equal(true,false)
                }


           });

        });

        describe("SOR SellTerm Order test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order - Sell Term test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_SellTerm_" + reqId
                console.log("Streaming Outright Order Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.SELL,
                     currency : mdData.SOR_termCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order ->  Sell Term test : request : " )
                console.log(wsreq)
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order - Sell Term test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        }
                    }
                }
            });

            it("SOR SellTerm Order test", function () {
                if (RECEIVED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(RECEIVED)

                    assert.exists(RECEIVED.coId)
                    assert.notEqual(null,RECEIVED.coId,"coId is null")
                    assert.equal(orderData.MARKET, RECEIVED.type)
                    assert.equal(orderData.GTT, RECEIVED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, RECEIVED.side,"side is not correct")
                    assert.equal(mdData.SOR_termCurrency_EURUSD, RECEIVED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,RECEIVED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, RECEIVED.size,"size is not correct")
                    assert.equal( user.orgname, RECEIVED.org,"org is not correct")
                    assert.equal(orderData.ExpiryTime, RECEIVED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"expiryTime is not present")
                    assert.exists(RECEIVED.clientOrderTime,"clientOrderTime is not present")
                    assert.exists(RECEIVED.tradeChannel,"tradeChannel is not present")
                    assert.exists(RECEIVED.preferredProviders,"preferredProviders is not present")
                    assert.exists(RECEIVED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, RECEIVED.tenor,"org is not correct")
                    assert.equal( user.username+"@"+ user.orgname, RECEIVED.userFullName,"userFullName is not correct")
					assert.equal("place", RECEIVED.action,"action is not correct")
					assert.equal("RECEIVED", RECEIVED.status,"status is not correct")
					assert.equal("FORWARD", RECEIVED.settlementType,"status is not correct")
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }


               if (PENDING_NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(PENDING_NEW)

                    assert.exists(PENDING_NEW.coId)
                    assert.notEqual(null,PENDING_NEW.coId,"coId is null")
                    assert.equal(orderData.MARKET, PENDING_NEW.type)
                    assert.equal(orderData.GTT, PENDING_NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, PENDING_NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_termCurrency_EURUSD, PENDING_NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,PENDING_NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
					assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
                    assert.equal( user.orgname, PENDING_NEW.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, PENDING_NEW.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(PENDING_NEW.execFlags,"execFlags doesnt exist")
					assert.exists(PENDING_NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, PENDING_NEW.spotRate,"spotRate is not correct")
					assert.exists(PENDING_NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, PENDING_NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(PENDING_NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, PENDING_NEW.averagePrice,"averagePrice is not correct")
					assert.exists(PENDING_NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, PENDING_NEW.cumQty,"cumQty is not correct")
					assert.exists(PENDING_NEW.execId,"execId doesnt exist")
					assert.exists(PENDING_NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, PENDING_NEW.lastPrice,"lastPrice is not correct")
					assert.exists(PENDING_NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, PENDING_NEW.lastQty,"lastQty is not correct")
					assert.equal("NONE", PENDING_NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, PENDING_NEW.leavesQty,"leavesQty is not correct")
					assert.exists(PENDING_NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, PENDING_NEW.userFullName,"userFullName is not correct")
					assert.equal("place", PENDING_NEW.action,"action is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied PENDING_NEW message")
                    assert.equal(true,false)
                }

               if (NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(NEW)

                    assert.exists(NEW.coId)
                    assert.notEqual(null,NEW.coId,"coId is null")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_termCurrency_EURUSD, NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, NEW.size,"size is not correct")
                    assert.equal( user.orgname, NEW.org,"org is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, NEW.expiryTime,"expiryTime is not correct")
					assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(NEW.execFlags,"execFlags doesnt exist")
					assert.exists(NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, NEW.spotRate,"spotRate is not correct")
					assert.exists(NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, NEW.averagePrice,"averagePrice is not correct")
					assert.exists(NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, NEW.cumQty,"cumQty is not correct")
					assert.exists(NEW.execId,"execId doesnt exist")
					assert.exists(NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, NEW.lastPrice,"lastPrice is not correct")
					assert.exists(NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, NEW.lastQty,"lastQty is not correct")
					assert.notEqual(null, NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("NEW", NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, NEW.leavesQty,"leavesQty is not correct")
					assert.exists(NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, NEW.userFullName,"userFullName is not correct")
					assert.equal("place", NEW.action,"action is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied NEW message")
                    assert.equal(true,false)
                }
				   if (FILLED !== "") {
						console.log("Streaming Outright - Subscription Rate Test -> rate : ")
						console.log(FILLED)

						assert.exists(FILLED.coId)
						assert.notEqual(null,FILLED.coId,"coId is null")
						assert.equal(orderData.MARKET, FILLED.type)
						assert.equal(orderData.GTT, FILLED.timeInForce,"timeInForce is not correct")
						assert.equal(orderData.SELL, FILLED.side,"side is not correct")
						assert.equal(mdData.SOR_termCurrency_EURUSD, FILLED.currency,"currency is not correct")
						assert.equal(mdData.SOR_Symbol_EURUSD,FILLED.symbol,"symbol is not correct")
						assert.equal(orderData.size, FILLED.size,"size is not correct")
						assert.equal( user.orgname, FILLED.org,"org is not correct")
						// has to be uncommented after fixing expiryTime format
						//assert.equal(orderData.ExpiryTime, FILLED.expiryTime,"expiryTime is not correct")
						assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

						assert.exists(FILLED.execFlags,"execFlags doesnt exist")
						assert.exists(FILLED.spotRate,"spotRate doesnt exist")
						assert.notEqual(0, FILLED.spotRate,"spotRate is zero")
						assert.exists(FILLED.forwardPoints,"forwardPoints doesnt exist")
						assert.notEqual(0, FILLED.forwardPoints,"forwardPoints is zero")
						assert.exists(FILLED.averagePrice,"averagePrice doesnt exist")
						assert.notEqual(0, FILLED.averagePrice,"averagePrice is 0")
						assert.exists(FILLED.cumQty,"cumQty doesnt exist")
						assert.equal(orderData.size, FILLED.cumQty,"cumQty is not correct")
						assert.exists(FILLED.execId,"execId doesnt exist")
						assert.exists(FILLED.lastPrice,"lastPrice doesnt exist")
						assert.notEqual(0, FILLED.lastPrice,"lastPrice is zero")
						assert.exists(FILLED.lastQty,"lastQty doesnt exist")
						assert.equal(orderData.size, FILLED.lastQty,"lastQty is not correct")
						assert.notEqual(null, FILLED.orderId,"orderId is not correct")
						assert.exists(FILLED.valueDate,"valueDate doesnt exist")
						assert.exists(FILLED.tradeDate,"tradeDate doesnt exist")
						assert.exists(FILLED.settlCurrAmt,"settlCurrAmt doesnt exist")
						amt = FILLED.size/FILLED.averagePrice
						assert.equal(amt.toFixed(2),FILLED.settlCurrAmt,"settlCurrAmt is not correct")
						assert.equal("TRADE", FILLED.executionType,"executionType is not correct")
						assert.equal(0, FILLED.leavesQty,"leavesQty is not correct")
						assert.exists(FILLED.counterParty,"counterParty doesnt exist")
						assert.exists(FILLED.counterPartyAccount,"counterPartyAccount doesnt exist")
						assert.equal( user.username+"@"+ user.orgname, FILLED.userFullName,"userFullName is not correct")
						assert.exists(FILLED.transactTime,"transactTime doesnt exist")
						assert.equal("place", FILLED.action,"action is not correct")
						assert.equal("FILLED", FILLED.status,"status is not correct")

					} else {
						console.log("Streaming Outright -   Subscription Rate test -> Not receied FILLED message")
						assert.equal(true,false)
					}
           });

        });

        describe("SOR BuyTerm Order test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order - Buy Term test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_BuyTerm_" + reqId
                console.log("Streaming Outright Order Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_termCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order ->  SELL Term test : request : ")
				console.log(wsreq)
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order - SELL Term test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        }
                    }
                }
            });

            it("SOR BuyTerm Order test", function () {
                if (RECEIVED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(RECEIVED)

                    assert.exists(RECEIVED.coId)
                    assert.notEqual(null,RECEIVED.coId,"coId is null")
                    assert.equal(orderData.MARKET, RECEIVED.type)
                    assert.equal(orderData.GTT, RECEIVED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, RECEIVED.side,"side is not correct")
                    assert.equal(mdData.SOR_termCurrency_EURUSD, RECEIVED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,RECEIVED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, RECEIVED.size,"size is not correct")
                    assert.equal( user.orgname, RECEIVED.org,"org is not correct")
                    assert.equal(orderData.ExpiryTime, RECEIVED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"expiryTime is not present")
                    assert.exists(RECEIVED.clientOrderTime,"clientOrderTime is not present")
                    assert.exists(RECEIVED.tradeChannel,"tradeChannel is not present")
                    assert.exists(RECEIVED.preferredProviders,"preferredProviders is not present")
                    assert.exists(RECEIVED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, RECEIVED.tenor,"org is not correct")
                    assert.equal( user.username+"@"+ user.orgname, RECEIVED.userFullName,"userFullName is not correct")
					assert.equal("place", RECEIVED.action,"action is not correct")
					assert.equal("RECEIVED", RECEIVED.status,"status is not correct")
					assert.equal("FORWARD", RECEIVED.settlementType,"status is not correct")
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }


               if (PENDING_NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(PENDING_NEW)

                    assert.exists(PENDING_NEW.coId)
                    assert.notEqual(null,PENDING_NEW.coId,"coId is null")
                    assert.equal(orderData.MARKET, PENDING_NEW.type)
                    assert.equal(orderData.GTT, PENDING_NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, PENDING_NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_termCurrency_EURUSD, PENDING_NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,PENDING_NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
                    assert.equal( user.orgname, PENDING_NEW.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, PENDING_NEW.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(PENDING_NEW.execFlags,"execFlags doesnt exist")
					assert.exists(PENDING_NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, PENDING_NEW.spotRate,"spotRate is not correct")
					assert.exists(PENDING_NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, PENDING_NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(PENDING_NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, PENDING_NEW.averagePrice,"averagePrice is not correct")
					assert.exists(PENDING_NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, PENDING_NEW.cumQty,"cumQty is not correct")
					assert.exists(PENDING_NEW.execId,"execId doesnt exist")
					assert.exists(PENDING_NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, PENDING_NEW.lastPrice,"lastPrice is not correct")
					assert.exists(PENDING_NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, PENDING_NEW.lastQty,"lastQty is not correct")
					assert.equal("NONE", PENDING_NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, PENDING_NEW.leavesQty,"leavesQty is not correct")
					assert.exists(PENDING_NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, PENDING_NEW.userFullName,"userFullName is not correct")
					assert.equal("place", PENDING_NEW.action,"action is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied PENDING_NEW message")
                    assert.equal(true,false)
                }

               if (NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(NEW)

                    assert.exists(NEW.coId)
                    assert.notEqual(null,NEW.coId,"coId is null")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_termCurrency_EURUSD, NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, NEW.size,"size is not correct")
                    assert.equal( user.orgname, NEW.org,"org is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, NEW.expiryTime,"expiryTime is not correct")
					assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(NEW.execFlags,"execFlags doesnt exist")
					assert.exists(NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, NEW.spotRate,"spotRate is not correct")
					assert.exists(NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, NEW.averagePrice,"averagePrice is not correct")
					assert.exists(NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, NEW.cumQty,"cumQty is not correct")
					assert.exists(NEW.execId,"execId doesnt exist")
					assert.exists(NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, NEW.lastPrice,"lastPrice is not correct")
					assert.exists(NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, NEW.lastQty,"lastQty is not correct")
					assert.notEqual(null, NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("NEW", NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, NEW.leavesQty,"leavesQty is not correct")
					assert.exists(NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, NEW.userFullName,"userFullName is not correct")
					assert.equal("place", NEW.action,"action is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied NEW message")
                    assert.equal(true,false)
                }
               if (FILLED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(FILLED)

                    assert.exists(FILLED.coId)
                    assert.notEqual(null,FILLED.coId,"coId is null")
                    assert.equal(orderData.MARKET, FILLED.type)
                    assert.equal(orderData.GTT, FILLED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FILLED.side,"side is not correct")
                    assert.equal(mdData.SOR_termCurrency_EURUSD, FILLED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,FILLED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FILLED.size,"size is not correct")
                    assert.equal( user.orgname, FILLED.org,"org is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, FILLED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")
                    assert.exists(FILLED.execFlags,"execFlags doesnt exist")
                    assert.exists(FILLED.spotRate,"spotRate doesnt exist")
                    assert.notEqual(0, FILLED.spotRate,"spotRate is zero")
                    assert.exists(FILLED.forwardPoints,"forwardPoints is zero")
                    assert.notEqual(0, FILLED.forwardPoints,"forwardPoints is not correct")
                    assert.exists(FILLED.averagePrice,"averagePrice doesnt exist")
                    assert.notEqual(0, FILLED.averagePrice,"averagePrice is 0")
                    assert.exists(FILLED.cumQty,"cumQty doesnt exist")
                    assert.equal(orderData.size, FILLED.cumQty,"cumQty is not correct")
                    assert.exists(FILLED.execId,"execId doesnt exist")
                    assert.exists(FILLED.lastPrice,"lastPrice doesnt exist")
                    assert.notEqual(0, FILLED.lastPrice,"lastPrice is zero")
                    assert.exists(FILLED.lastQty,"lastQty doesnt exist")
                    assert.equal(orderData.size, FILLED.lastQty,"lastQty is not correct")
                    assert.notEqual(null, FILLED.orderId,"orderId is not correct")
                    assert.exists(FILLED.valueDate,"valueDate doesnt exist")
                    assert.exists(FILLED.tradeDate,"tradeDate doesnt exist")
                    assert.exists(FILLED.settlCurrAmt,"settlCurrAmt doesnt exist")
                    amt = FILLED.size/FILLED.averagePrice
                    assert.equal(amt.toFixed(2), FILLED.settlCurrAmt,"settlCurrAmt is not correct")
                    assert.equal("TRADE", FILLED.executionType,"executionType is not correct")
                    assert.equal(0, FILLED.leavesQty,"leavesQty is not correct")
                    assert.exists(FILLED.counterParty,"counterParty doesnt exist")
                    assert.exists(FILLED.counterPartyAccount,"counterPartyAccount doesnt exist")
                    assert.equal( user.username+"@"+ user.orgname, FILLED.userFullName,"userFullName is not correct")
                    assert.exists(FILLED.transactTime,"transactTime doesnt exist")
                    assert.equal("place", FILLED.action,"action is not correct")
                    assert.equal("FILLED", FILLED.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied FILLED message")
                    assert.equal(true,false)
                }


           });

        });

        describe("SOR BuyBase Limit Order test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Limit Order - Buy Base test ************************** ' + new Date());
                tempReqId = "StreamingOR_LimitOrder_BuyBase_" + reqId
                console.log("Streaming Outright Limit Order Test -> reqId = " + reqId)
//{ "orders": [{ "coId":"SOR_Order15", "type":"Limit", "side":"Sell", "symbol":"EUR/USD",
 //"currency":"USD", "size":1000000, "price":1.05511, "timeInForce":"GTC", "tenor":"1W" }]}
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.LIMIT,
					 price : "1.5",
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Limit Order ->  Buy Base test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Limit Order - Buy Base test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        }
                    }
                }
            });

            it("SOR BuyBase Limit Order test", function () {
                if (RECEIVED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(RECEIVED)
//{"orderResponses":[{"coId":"StreamingOR_Order_BuyBase_75","type":"Market","timeInForce":"GTT","side":"Buy","currency":"EUR","
//symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":30,"clientOrderTime":"Aug 18, 2023 3:13:05 PM","tradeChannel":"API/WS/ESP",
//"preferredProviders":["NTFX"],"tenor":"1W","userFullName":"user1@pfOrg","action":"place","status":"RECEIVED"}]}

                    assert.exists(RECEIVED.coId)
                    assert.notEqual(null,RECEIVED.coId,"coId is null")
                    assert.equal(orderData.LIMIT, RECEIVED.type)
                    assert.equal(orderData.GTT, RECEIVED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, RECEIVED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, RECEIVED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,RECEIVED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, RECEIVED.size,"size is not correct")
                    assert.equal( user.orgname, RECEIVED.org,"org is not correct")
					assert.equal( "1.5", RECEIVED.price,"price is not correct")
                    assert.equal(orderData.ExpiryTime, RECEIVED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"expiryTime is not present")
                    assert.exists(RECEIVED.clientOrderTime,"clientOrderTime is not present")
                    assert.exists(RECEIVED.tradeChannel,"tradeChannel is not present")
                    assert.exists(RECEIVED.preferredProviders,"preferredProviders is not present")
                    assert.exists(RECEIVED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, RECEIVED.tenor,"tenor is not correct")
                    assert.equal( user.username+"@"+ user.orgname, RECEIVED.userFullName,"userFullName is not correct")
					assert.equal("place", RECEIVED.action,"action is not correct")
					assert.equal("RECEIVED", RECEIVED.status,"status is not correct")
					assert.equal("FORWARD", RECEIVED.settlementType,"status is not correct")
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }


               if (PENDING_NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(PENDING_NEW)
//Streaming Outright Limit Order - Buy Base test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_BuyBase_45","type":"Market","timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"NONE","lastPrice":0,"lastQty":0,"orderId":"NONE","executionType":"PENDING_NEW","leavesQty":1000000,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"PENDING_NEW"}]}

                    assert.exists(PENDING_NEW.coId)
                    assert.notEqual(null,PENDING_NEW.coId,"coId is null")
                    assert.equal(orderData.LIMIT, PENDING_NEW.type)
                    assert.equal(orderData.GTT, PENDING_NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, PENDING_NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, PENDING_NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,PENDING_NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
					assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
                    assert.equal( user.orgname, PENDING_NEW.org,"org is not correct")
					assert.equal( "1.5", RECEIVED.price,"price is not correct")
                    //assert.equal(orderData.ExpiryTime, PENDING_NEW.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(PENDING_NEW.execFlags,"execFlags doesnt exist")
					assert.exists(PENDING_NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, PENDING_NEW.spotRate,"spotRate is not correct")
					assert.exists(PENDING_NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, PENDING_NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(PENDING_NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, PENDING_NEW.averagePrice,"averagePrice is not correct")
					assert.exists(PENDING_NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, PENDING_NEW.cumQty,"cumQty is not correct")
					assert.exists(PENDING_NEW.execId,"execId doesnt exist")
					assert.exists(PENDING_NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, PENDING_NEW.lastPrice,"lastPrice is not correct")
					assert.exists(PENDING_NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, PENDING_NEW.lastQty,"lastQty is not correct")
					assert.equal("NONE", PENDING_NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, PENDING_NEW.leavesQty,"leavesQty is not correct")
					assert.exists(PENDING_NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, PENDING_NEW.userFullName,"userFullName is not correct")
					assert.equal("place", PENDING_NEW.action,"action is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied PENDING_NEW message")
                    assert.equal(true,false)
                }

               if (NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(NEW)
//Streaming Outright Limit Order - Buy Base test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_BuyBase_45","type":"Market","timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"NONE","lastPrice":0,"lastQty":0,"orderId":"NONE","executionType":"NEW","leavesQty":1000000,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"NEW"}]}

                    assert.exists(NEW.coId)
                    assert.notEqual(null,NEW.coId,"coId is null")
                    assert.equal(orderData.LIMIT, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, NEW.size,"size is not correct")
                    assert.equal( user.orgname, NEW.org,"org is not correct")
					assert.equal( "1.5", RECEIVED.price,"price is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, NEW.expiryTime,"expiryTime is not correct")
					assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(NEW.execFlags,"execFlags doesnt exist")
					assert.exists(NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, NEW.spotRate,"spotRate is not correct")
					assert.exists(NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, NEW.averagePrice,"averagePrice is not correct")
					assert.exists(NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, NEW.cumQty,"cumQty is not correct")
					assert.exists(NEW.execId,"execId doesnt exist")
					assert.exists(NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, NEW.lastPrice,"lastPrice is not correct")
					assert.exists(NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, NEW.lastQty,"lastQty is not correct")
					assert.notEqual(null, NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("NEW", NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, NEW.leavesQty,"leavesQty is not correct")
					assert.exists(NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, NEW.userFullName,"userFullName is not correct")
					assert.equal("place", NEW.action,"action is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied NEW message")
                    assert.equal(true,false)
                }
				   if (FILLED !== "") {
						console.log("Streaming Outright - Subscription Rate Test -> rate : ")
						console.log(FILLED)
	//{"orderResponses":[{"coId":"StreamingOR_Order_BuyBase_45","type":"Market","timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"averagePrice":1.05491,"cumQty":1000000,"execId":"FXI9361341163","lastPrice":1.05491,"lastQty":1000000,"orderId":"**********","valueDate":"********","tradeDate":"********","settlCurrAmt":1054910,"executionType":"TRADE","leavesQty":0,"counterParty":"NTFX","counterPartyAccount":"NTFXle","userFullName":"user1@pfOrg","transactTime":*************,"action":"place","status":"FILLED"}]}


						assert.exists(FILLED.coId)
						assert.notEqual(null,FILLED.coId,"coId is null")
						assert.equal(orderData.LIMIT, FILLED.type)
						assert.equal(orderData.GTT, FILLED.timeInForce,"timeInForce is not correct")
						assert.equal(orderData.BUY, FILLED.side,"side is not correct")
						assert.equal(mdData.SOR_baseCurrency_EURUSD, FILLED.currency,"currency is not correct")
						assert.equal(mdData.SOR_Symbol_EURUSD,FILLED.symbol,"symbol is not correct")
						assert.equal(orderData.size, FILLED.size,"size is not correct")
						assert.equal( user.orgname, FILLED.org,"org is not correct")
						assert.equal( "1.5", RECEIVED.price,"price is not correct")
						// has to be uncommented after fixing expiryTime format
						//assert.equal(orderData.ExpiryTime, FILLED.expiryTime,"expiryTime is not correct")
						assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

						assert.exists(FILLED.execFlags,"execFlags doesnt exist")
						assert.exists(FILLED.spotRate,"spotRate doesnt exist")
						assert.notEqual(0, FILLED.spotRate,"spotRate is zero")
						assert.exists(FILLED.forwardPoints,"forwardPoints doesnt exist")
						assert.notEqual(0, FILLED.forwardPoints,"forwardPoints is zero")
						assert.exists(FILLED.averagePrice,"averagePrice doesnt exist")
						assert.notEqual(0, FILLED.averagePrice,"averagePrice is 0")
						assert.exists(FILLED.cumQty,"cumQty doesnt exist")
						assert.equal(orderData.size, FILLED.cumQty,"cumQty is not correct")
						assert.exists(FILLED.execId,"execId doesnt exist")
						assert.exists(FILLED.lastPrice,"lastPrice doesnt exist")
						assert.notEqual(0, FILLED.lastPrice,"lastPrice is zero")
						assert.exists(FILLED.lastQty,"lastQty doesnt exist")
						assert.equal(orderData.size, FILLED.lastQty,"lastQty is not correct")
						assert.notEqual(null, FILLED.orderId,"orderId is not correct")
						assert.exists(FILLED.valueDate,"valueDate doesnt exist")
						assert.exists(FILLED.tradeDate,"tradeDate doesnt exist")
						assert.exists(FILLED.settlCurrAmt,"settlCurrAmt doesnt exist")
						amt = FILLED.size*FILLED.averagePrice
						assert.equal(FILLED.size*FILLED.averagePrice, FILLED.settlCurrAmt,"settlCurrAmt is not correct")
						assert.equal("TRADE", FILLED.executionType,"executionType is not correct")
						assert.equal(0, FILLED.leavesQty,"leavesQty is not correct")
						assert.exists(FILLED.counterParty,"counterParty doesnt exist")
						assert.exists(FILLED.counterPartyAccount,"counterPartyAccount doesnt exist")
						assert.equal( user.username+"@"+ user.orgname, FILLED.userFullName,"userFullName is not correct")
						assert.exists(FILLED.transactTime,"transactTime doesnt exist")
						assert.equal("place", FILLED.action,"action is not correct")
						assert.equal("FILLED", FILLED.status,"status is not correct")

					} else {
						console.log("Streaming Outright -   Subscription Rate test -> Not receied FILLED message")
						assert.equal(true,false)
					}

           });

        });

        describe("SOR SellBase Limit Order test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Limit Order - Sell Base test ************************** ' + new Date());
                tempReqId = "StreamingOR_LimitOrder_SellBase_" + reqId
                console.log("Streaming Outright Limit Order Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.LIMIT,
					 price : "0.8",
                     side : orderData.SELL,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Limit Order ->  SELL Base test : request : ")
				console.log(wsreq)
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Limit Order - SELL Base test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        }
                    }
                }
            });

            it("SOR SellBase Limit Order test", function () {
                if (RECEIVED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(RECEIVED)
//{"orderResponses":[{"coId":"StreamingOR_Order_SellBase_75","type":"Market","timeInForce":"GTT","side":"SELL","currency":"EUR","
//symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":30,"clientOrderTime":"Aug 18, 2023 3:13:05 PM","tradeChannel":"API/WS/ESP",
//"preferredProviders":["NTFX"],"tenor":"1W","userFullName":"user1@pfOrg","action":"place","status":"RECEIVED"}]}

                    assert.exists(RECEIVED.coId)
                    assert.notEqual(null,RECEIVED.coId,"coId is null")
                    assert.equal(orderData.LIMIT, RECEIVED.type)
                    assert.equal(orderData.GTT, RECEIVED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, RECEIVED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, RECEIVED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,RECEIVED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, RECEIVED.size,"size is not correct")
                    assert.equal( user.orgname, RECEIVED.org,"org is not correct")
					assert.equal( "0.8", RECEIVED.price,"price is not correct")
                    assert.equal(orderData.ExpiryTime, RECEIVED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"expiryTime is not present")
                    assert.exists(RECEIVED.clientOrderTime,"clientOrderTime is not present")
                    assert.exists(RECEIVED.tradeChannel,"tradeChannel is not present")
                    assert.exists(RECEIVED.preferredProviders,"preferredProviders is not present")
                    assert.exists(RECEIVED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, RECEIVED.tenor,"org is not correct")
                    assert.equal( user.username+"@"+ user.orgname, RECEIVED.userFullName,"userFullName is not correct")
					assert.equal("place", RECEIVED.action,"action is not correct")
					assert.equal("RECEIVED", RECEIVED.status,"status is not correct")
					assert.equal("FORWARD", RECEIVED.settlementType,"status is not correct")
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }


               if (PENDING_NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(PENDING_NEW)
//Streaming Outright Limit Order - SELL Base test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_SellBase_45","type":"Market","timeInForce":"GTT","side":"SELL","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"NONE","lastPrice":0,"lastQty":0,"orderId":"NONE","executionType":"PENDING_NEW","leavesQty":1000000,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"PENDING_NEW"}]}

                    assert.exists(PENDING_NEW.coId)
                    assert.notEqual(null,PENDING_NEW.coId,"coId is null")
                    assert.equal(orderData.LIMIT, PENDING_NEW.type)
                    assert.equal(orderData.GTT, PENDING_NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, PENDING_NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, PENDING_NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,PENDING_NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, PENDING_NEW.size,"size is not correct")
                    assert.equal( user.orgname, PENDING_NEW.org,"org is not correct")
					assert.equal( "0.8", RECEIVED.price,"price is not correct")
                    //assert.equal(orderData.ExpiryTime, PENDING_NEW.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")

					assert.exists(PENDING_NEW.execFlags,"execFlags doesnt exist")
					assert.exists(PENDING_NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, PENDING_NEW.spotRate,"spotRate is not correct")
					assert.exists(PENDING_NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, PENDING_NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(PENDING_NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, PENDING_NEW.averagePrice,"averagePrice is not correct")
					assert.exists(PENDING_NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, PENDING_NEW.cumQty,"cumQty is not correct")
					assert.exists(PENDING_NEW.execId,"execId doesnt exist")
					assert.exists(PENDING_NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, PENDING_NEW.lastPrice,"lastPrice is not correct")
					assert.exists(PENDING_NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, PENDING_NEW.lastQty,"lastQty is not correct")
					assert.equal("NONE", PENDING_NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, PENDING_NEW.leavesQty,"leavesQty is not correct")
					assert.exists(PENDING_NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, PENDING_NEW.userFullName,"userFullName is not correct")
					assert.equal("place", PENDING_NEW.action,"action is not correct")
					assert.equal("PENDING_NEW", PENDING_NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied PENDING_NEW message")
                    assert.equal(true,false)
                }

               if (NEW !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(NEW)
//Streaming Outright Limit Order - SELL Base test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_SellBase_45","type":"Market","timeInForce":"GTT","side":"SELL","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"NONE","lastPrice":0,"lastQty":0,"orderId":"NONE","executionType":"NEW","leavesQty":1000000,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"NEW"}]}

                    assert.exists(NEW.coId)
                    assert.notEqual(null,NEW.coId,"coId is null")
                    assert.equal(orderData.LIMIT, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, NEW.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, NEW.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,NEW.symbol,"symbol is not correct")
                    assert.equal(orderData.size, NEW.size,"size is not correct")
                    assert.equal( user.orgname, NEW.org,"org is not correct")
					assert.equal( "0.8", RECEIVED.price,"price is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, NEW.expiryTime,"expiryTime is not correct")
					assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")
					assert.exists(NEW.execFlags,"execFlags doesnt exist")
					assert.exists(NEW.spotRate,"spotRate doesnt exist")
					assert.equal(0, NEW.spotRate,"spotRate is not correct")
					assert.exists(NEW.forwardPoints,"forwardPoints doesnt exist")
					assert.equal(0, NEW.forwardPoints,"forwardPoints is not correct")
					assert.exists(NEW.averagePrice,"averagePrice doesnt exist")
					assert.equal(0, NEW.averagePrice,"averagePrice is not correct")
					assert.exists(NEW.cumQty,"cumQty doesnt exist")
					assert.equal(0, NEW.cumQty,"cumQty is not correct")
					assert.exists(NEW.execId,"execId doesnt exist")
					assert.exists(NEW.lastPrice,"lastPrice doesnt exist")
					assert.equal(0, NEW.lastPrice,"lastPrice is not correct")
					assert.exists(NEW.lastQty,"lastQty doesnt exist")
					assert.equal(0, NEW.lastQty,"lastQty is not correct")
					assert.notEqual(null, NEW.orderId,"orderId is not correct")
					assert.equal(mdData.SOR_1W_tenor, PENDING_NEW.valueDate,"valueDate is not correct")
					assert.equal("NEW", NEW.executionType,"executionType is not correct")
					assert.equal(orderData.size, NEW.leavesQty,"leavesQty is not correct")
					assert.exists(NEW.counterParty,"counterParty doesnt exist")
					assert.equal( user.username+"@"+ user.orgname, NEW.userFullName,"userFullName is not correct")
					assert.equal("place", NEW.action,"action is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied NEW message")
                    assert.equal(true,false)
                }
               if (FILLED !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(FILLED)
//{"orderResponses":[{"coId":"StreamingOR_Order_SellBase_45","type":"Market","timeInForce":"GTT","side":"SELL","currency":"EUR","symbol":"EUR/USD","size":1000000,"org":"pfOrg","expiryTime":*************,"execFlags":[],"averagePrice":1.05491,"cumQty":1000000,"execId":"FXI9361341163","lastPrice":1.05491,"lastQty":1000000,"orderId":"**********","valueDate":"********","tradeDate":"********","settlCurrAmt":1054910,"executionType":"TRADE","leavesQty":0,"counterParty":"NTFX","counterPartyAccount":"NTFXle","userFullName":"user1@pfOrg","transactTime":*************,"action":"place","status":"FILLED"}]}

                    assert.exists(FILLED.coId)
                    assert.notEqual(null,FILLED.coId,"coId is null")
                    assert.equal(orderData.LIMIT, FILLED.type)
                    assert.equal(orderData.GTT, FILLED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.SELL, FILLED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FILLED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,FILLED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FILLED.size,"size is not correct")
                    assert.equal(user.orgname, FILLED.org,"org is not correct")
					assert.equal("0.8", RECEIVED.price,"price is not correct")
                    // has to be uncommented after fixing expiryTime format
                    //assert.equal(orderData.ExpiryTime, FILLED.expiryTime,"expiryTime is not correct")
                    assert.exists(RECEIVED.expiryTime,"ExpiryTime is not present")
                    assert.exists(FILLED.execFlags,"execFlags doesnt exist")
                    assert.exists(FILLED.spotRate,"spotRate doesnt exist")
                    assert.notEqual(0, FILLED.spotRate,"spotRate is zero")
                    assert.exists(FILLED.forwardPoints,"forwardPoints is zero")
                    assert.notEqual(0, FILLED.forwardPoints,"forwardPoints is not correct")
                    assert.exists(FILLED.averagePrice,"averagePrice doesnt exist")
                    assert.notEqual(0, FILLED.averagePrice,"averagePrice is 0")
                    assert.exists(FILLED.cumQty,"cumQty doesnt exist")
                    assert.equal(orderData.size, FILLED.cumQty,"cumQty is not correct")
                    assert.exists(FILLED.execId,"execId doesnt exist")
                    assert.exists(FILLED.lastPrice,"lastPrice doesnt exist")
                    assert.notEqual(0, FILLED.lastPrice,"lastPrice is zero")
                    assert.exists(FILLED.lastQty,"lastQty doesnt exist")
                    assert.equal(orderData.size, FILLED.lastQty,"lastQty is not correct")
                    assert.notEqual(null, FILLED.orderId,"orderId is not correct")
                    assert.exists(FILLED.valueDate,"valueDate doesnt exist")
                    assert.exists(FILLED.tradeDate,"tradeDate doesnt exist")
                    assert.exists(FILLED.settlCurrAmt,"settlCurrAmt doesnt exist")
                    amt = FILLED.size*FILLED.averagePrice
                    assert.equal(FILLED.size*FILLED.averagePrice, FILLED.settlCurrAmt,"settlCurrAmt is not correct")
                    assert.equal("TRADE", FILLED.executionType,"executionType is not correct")
                    assert.equal(0, FILLED.leavesQty,"leavesQty is not correct")
                    assert.exists(FILLED.counterParty,"counterParty doesnt exist")
                    assert.exists(FILLED.counterPartyAccount,"counterPartyAccount doesnt exist")
                    assert.equal( user.username+"@"+ user.orgname, FILLED.userFullName,"userFullName is not correct")
                    assert.exists(FILLED.transactTime,"transactTime doesnt exist")
                    assert.equal("place", FILLED.action,"action is not correct")
                    assert.equal("FILLED", FILLED.status,"status is not correct")

                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not receied FILLED message")
                    assert.equal(true,false)
                }
           });

        });

    });
};


let streamingOutrightOrderNegativeTC = function(){

    describe("Streaming Outright Orders Negative", function () {

        before(function (done) {
            tempReqId = ""
            wsconnect(done);
        });

        after(function () {
            connection.close()
        });
        let reqId = Math.floor(Math.random() * 1000)


       describe("SOR Order Neg Invalid CP test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid CP test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidCP_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : "ABC/XYZ", //mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid CP test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid CP test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        }
                    }
                }
            });

            it("SOR Order Neg Invalid CP test", function () {
                if (REJECTED !== "") {
//Streaming Outright Order Neg - Invalid CP test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_Neg_348","type":"Market",
//"timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"ABC/XYZ","size":1000000,"org":"pfOrg","expiryTime":1692748850000,
//"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"0","lastPrice":0,"lastQty":0,"orderId":"0",
//"valueDate":"1W","executionType":"REJECTED","leavesQty":0,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"REJECTED","reason":"RequestValidationError.InvalidCurrencyPair"}]}


                    console.log("Streaming Outright Order Neg - Invalid CP Test -> rate : ")
                    console.log(REJECTED)

                    assert.exists(REJECTED.coId)
                    assert.notEqual(null,REJECTED.coId,"coId is null")
                    assert.equal(orderData.MARKET, REJECTED.type)
                    assert.equal(orderData.GTT, REJECTED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, REJECTED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, REJECTED.currency,"currency is not correct")
                    assert.equal("ABC/XYZ",REJECTED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, REJECTED.size,"size is not correct")
                    assert.equal( user.orgname, REJECTED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, REJECTED.expiryTime,"expiryTime is not correct")
                    assert.exists(REJECTED.expiryTime,"expiryTime is not present")
					assert.exists(REJECTED.execFlags,"execFlags is not present")
					assert.equal(0, REJECTED.spotRate,"spotRate is not correct")
					assert.equal(0, REJECTED.forwardPoints,"forwardPoints is not correct")
					assert.equal(0, REJECTED.averagePrice,"averagePrice is not correct")
					assert.equal(0, REJECTED.cumQty,"cumQty is not correct")
					assert.equal(0, REJECTED.execId,"execId is not correct")
					assert.equal(0, REJECTED.lastPrice,"lastPrice is not correct")
					assert.equal(0, REJECTED.lastQty,"lastQty is not correct")
					assert.equal(0, REJECTED.orderId,"orderId is not correct")
                    //assert.exists(REJECTED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, REJECTED.valueDate,"tenor is not correct")
					assert.equal("REJECTED", REJECTED.executionType,"executionType is not correct")
					assert.equal(0, REJECTED.leavesQty,"leavesQty is not correct")
					assert.exists(REJECTED.counterParty,"counterParty is not present")
					assert.equal( user.username+"@"+ user.orgname, REJECTED.userFullName,"userFullName is not correct")
					assert.equal("place", REJECTED.action,"action is not correct")
					assert.equal("REJECTED", REJECTED.status,"status is not correct")
					assert.equal("RequestValidationError.InvalidCurrencyPair", REJECTED.reason,"reason is not correct")


                } else {
                    console.log("Streaming Outright Order Neg - Invalid CP  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Without tag CP test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Without tag CP test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_WithoutTagCP_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     //symbol : "", //mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Without tag CP test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Without tag CP test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Without tag CP test", function () {
                if (FAILED !== "") {
//Streaming Outright Order Neg - Without tag CP test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_Neg_348","type":"Market",
//"timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"ABC/XYZ","size":1000000,"org":"pfOrg","expiryTime":1692748850000,
//"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"0","lastPrice":0,"lastQty":0,"orderId":"0",
//"valueDate":"1W","executionType":"FAILED","leavesQty":0,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"FAILED","reason":"RequestValidationError.InvalidCurrencyPair"}]}


                    console.log("Streaming Outright Order Neg - Without tag CP Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    //assert.equal(mdData.SOR_Symbol_EURUSD,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("Incomplete Currency Information", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Without tag CP  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

/// below tcs fail, need to be updated after bug fixes
       describe("SOR Order Neg Null CP test ", function () {
       // PLT-4984 - This tc has to be updated once the bug is fixed
       // currently orderResponse shows RECEIVED, it should be rejected
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null CP test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullCP_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : "", //mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null CP test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null CP test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }
                    }
                }
            });

            it("OR Order Neg Null CP test", function () {
                if (FAILED !== "") {
//Streaming Outright Order Neg - Null CP test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_Neg_348","type":"Market",
//"timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"ABC/XYZ","size":1000000,"org":"pfOrg","expiryTime":1692748850000,
//"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"0","lastPrice":0,"lastQty":0,"orderId":"0",
//"valueDate":"1W","executionType":"REJECTED","leavesQty":0,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"REJECTED","reason":"RequestValidationError.InvalidCurrencyPair"}]}


                    console.log("Streaming Outright Order Neg - Null CP Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal("ABC/XYZ",FAILED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, REJECTED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.execFlags,"execFlags is not present")
					assert.equal(0, FAILED.spotRate,"spotRate is not correct")
					assert.equal(0, FAILED.forwardPoints,"forwardPoints is not correct")
					assert.equal(0, FAILED.averagePrice,"averagePrice is not correct")
					assert.equal(0, FAILED.cumQty,"cumQty is not correct")
					assert.equal(0, FAILED.execId,"execId is not correct")
					assert.equal(0, FAILED.lastPrice,"lastPrice is not correct")
					assert.equal(0, FAILED.lastQty,"lastQty is not correct")
					assert.equal(0, FAILED.orderId,"orderId is not correct")
                    //assert.exists(REJECTED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.valueDate,"tenor is not correct")
					assert.equal("REJECTED", FAILED.executionType,"executionType is not correct")
					assert.equal(0, FAILED.leavesQty,"leavesQty is not correct")
					assert.exists(FAILED.counterParty,"counterParty is not present")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("Incomplete Currency Information", FAILED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Null CP  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Invalid Order Type test ", function () {
       // PLT-4982 - This tc has to updated once the bug is fixed.
       // Right now generic error is seen separately, instead UIG should reject the order and reason should be part of orderResponses
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid Type test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidType_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : "abc", //orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid Type test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid Type test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Invalid Order Type test", function () {
                if (FAILED !== "") {
//Streaming Outright Order Neg - Invalid Type test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_Neg_348","type":"Market",
//"timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"ABC/XYZ","size":1000000,"org":"pfOrg","expiryTime":1692748850000,
//"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"0","lastPrice":0,"lastQty":0,"orderId":"0",
//"valueDate":"1W","executionType":"FAILED","leavesQty":0,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"FAILED","reason":"RequestValidationError.InvalidCurrencyPair"}]}


                    console.log("Streaming Outright Order Neg - Invalid Type Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.SymbolNotSpecified", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Invalid Type  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Null Type test ", function () {
       // PLT-4982 - This tc has to updated once the bug is fixed.
       // Right now errors are coming separately, instead UIG should reject the order and reason should be part of orderResponses
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null Type test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullType_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : "", //orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null Type test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null Type test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Null Type test ", function () {
                if (FAILED !== "") {
//Streaming Outright Order Neg - Null Type test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_Neg_348","type":"Market",
//"timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"ABC/XYZ","size":1000000,"org":"pfOrg","expiryTime":1692748850000,
//"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"0","lastPrice":0,"lastQty":0,"orderId":"0",
//"valueDate":"1W","executionType":"FAILED","leavesQty":0,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"FAILED","reason":"RequestValidationError.InvalidCurrencyPair"}]}


                    console.log("Streaming Outright Order Neg - Null Type Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.SymbolNotSpecified", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Null Type  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });
//need to be updated after bug fixes
//

       describe("SOR Order Neg Without Type tag test ", function () {
       // PLT-4982 - This tc has to updated once the bug is fixed.
       // Right now errors are coming separately, instead UIG should reject the order and reason should be part of orderResponses

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Without Type tag test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_WithoutType_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     //type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Without Type tag test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Without Type tag test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Without Type tag test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Without Type tag Test -> rate : ")
                    console.log(FAILED)

                    assert.equal(tempReqId, FAILED.coId)
                    //assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,FAILED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.TypeNotSpecified", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Without Type tag  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Without Side tag test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Without Side tag test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_WithoutSide_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     //side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Without Side tag test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Without Side tag test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Without Side tag test", function () {
                if (FAILED !== "") {
                    console.log("Streaming Outright Order Neg - Without Side tag Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    //assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD, FAILED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.SideNotSpecified", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Without Side tag  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

///* // below tcs fail, need to be updated after bug fixes
       describe("SOR Order Neg Invalid Side test ", function () {
       // PLT-4980 Generic Errors are seen
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid Side test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidSide_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : "ABC", //orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid Side test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid Side test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Invalid Side test", function () {
                if (FAILED !== "") {
//Streaming Outright Order Neg - Invalid Side test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_Neg_348","type":"Market",
//"timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"ABC/XYZ","size":1000000,"org":"pfOrg","expiryTime":1692748850000,
//"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"0","lastPrice":0,"lastQty":0,"orderId":"0",
//"valueDate":"1W","executionType":"FAILED","leavesQty":0,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"FAILED","reason":"RequestValidationError.InvalidCurrencyPair"}]}


                    console.log("Streaming Outright Order Neg - Invalid Side Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal("ABC", FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD, FAILED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.SymbolNotSpecified", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Invalid Side  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Null Side test ", function () {
       // PLT-4980  Generic Errors are seen
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null Side test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullSide_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : "ABC", //orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null Side test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null Side test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Null Side test", function () {
                if (FAILED !== "") {
//Streaming Outright Order Neg - Null Side test ->  res : {"orderResponses":[{"coId":"StreamingOR_Order_Neg_348","type":"Market",
//"timeInForce":"GTT","side":"Buy","currency":"EUR","symbol":"ABC/XYZ","size":1000000,"org":"pfOrg","expiryTime":1692748850000,
//"execFlags":[],"spotRate":0,"forwardPoints":0,"averagePrice":0,"cumQty":0,"execId":"0","lastPrice":0,"lastQty":0,"orderId":"0",
//"valueDate":"1W","executionType":"FAILED","leavesQty":0,"counterParty":"NTFX","userFullName":"user1@pfOrg","action":"place","status":"FAILED","reason":"RequestValidationError.InvalidCurrencyPair"}]}


                    console.log("Streaming Outright Order Neg - Null Side Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal("", FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD, FAILED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.SymbolNotSpecified", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Null Side  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Null Currency test ", function () {
       // PLT-4984 - Reject reason received from OA is not being sent to Client
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null Currency test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullCcy_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : "", //mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null Currency test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null Currency test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Null Currency test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Null Currency Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal("ABC", FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD, FAILED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("Need to be modified after bug fix", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Null Currency  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });
//need to be updated after bug fixes
//

       describe("SOR Order Neg Invalid Currency test ", function () {
       // PLT-4984 - Reject reason received from OA is not being sent to Client
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid Currency test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidCcy_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : "ABC", //mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid Currency test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid Currency test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Invalid Currency test", function () {
                if (REJECTED !== "") {

                    console.log("Streaming Outright Order Neg - Invalid Currency Test -> rate : ")
                    console.log(REJECTED)

                    assert.exists(REJECTED.coId)
                    assert.notEqual(null,REJECTED.coId,"coId is null")
                    assert.equal(orderData.MARKET, REJECTED.type)
                    assert.equal(orderData.GTT, REJECTED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, REJECTED.side,"side is not correct")
                    assert.equal("ABC", REJECTED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,REJECTED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, REJECTED.size,"size is not correct")
                    assert.equal( user.orgname, REJECTED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, REJECTED.expiryTime,"expiryTime is not correct")
                    assert.exists(REJECTED.expiryTime,"expiryTime is not present")
					assert.exists(REJECTED.execFlags,"execFlags is not present")
					assert.equal(0, REJECTED.spotRate,"spotRate is not correct")
					assert.equal(0, REJECTED.forwardPoints,"forwardPoints is not correct")
					assert.equal(0, REJECTED.averagePrice,"averagePrice is not correct")
					assert.equal(0, REJECTED.cumQty,"cumQty is not correct")
					assert.equal(0, REJECTED.execId,"execId is not correct")
					assert.equal(0, REJECTED.lastPrice,"lastPrice is not correct")
					assert.equal(0, REJECTED.lastQty,"lastQty is not correct")
					assert.equal(0, REJECTED.orderId,"orderId is not correct")
                    //assert.exists(REJECTED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, REJECTED.valueDate,"tenor is not correct")
					assert.equal("REJECTED", REJECTED.executionType,"executionType is not correct")
					assert.equal(0, REJECTED.leavesQty,"leavesQty is not correct")
					assert.exists(REJECTED.counterParty,"counterParty is not present")
					assert.equal( user.username+"@"+ user.orgname, REJECTED.userFullName,"userFullName is not correct")
					assert.equal("place", REJECTED.action,"action is not correct")
					assert.equal("REJECTED", REJECTED.status,"status is not correct")
					assert.equal("RequestValidationError.InvalidDealtCcy", REJECTED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Invalid Currency  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Missing Currency tag test ", function () {

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Missing Currency tag test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_MissingCcyTag_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     //currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Missing Currency tag test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Missing Currency tag test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Missing Currency tag test ", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Missing Currency tag Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    //assert.equal("", FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,FAILED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
                    assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
                    assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
                    assert.exists(FAILED.preferredProviders,"preferredProviders is not present")
                    assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"org is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.CurrencyNotSpecified", FAILED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Missing Currency tag  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Null Size test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null Size test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullSize_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : "", //orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null Size test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null Size test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Null Size test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Null Size Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD, FAILED.symbol,"symbol is not correct")
                    //assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.SizeNotSpecified", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Null Size  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Missing Size tag test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Missing Size tag test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_MissingSize_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     //size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Missing Size tag test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Missing Size tag test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Missing Size tag test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Missing Size tag Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual(null,FAILED.coId,"coId is null")
                    assert.equal(tempReqId, FAILED.coId)
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD, FAILED.symbol,"symbol is not correct")
                    //assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
					assert.exists(FAILED.clientOrderTime,"clientOrderTime is not present")
					assert.exists(FAILED.tradeChannel,"tradeChannel is not present")
					assert.exists(FAILED.preferredProviders,"preferredProviders is not present")

					//assert.exists(FAILED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, FAILED.tenor,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.SizeNotSpecified", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Missing Size tag  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });


///* // below tcs fail, need to be updated after bug fixes
       describe("SOR Order Neg Invalid Size test ", function () {
       // PLT-4984 - Reject reason received from OA is not being sent to Client
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid Size test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidSize_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : "abc", //orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid Size test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid Size test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Invalid Size test", function () {
                if (REJECTED !== "") {

                    console.log("Streaming Outright Order Neg - Invalid Size Test -> rate : ")
                    console.log(REJECTED)

                    assert.exists(REJECTED.coId)
                    assert.notEqual(null,REJECTED.coId,"coId is null")
                    assert.equal(orderData.MARKET, REJECTED.type)
                    assert.equal(orderData.GTT, REJECTED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, REJECTED.side,"side is not correct")
                    assert.equal("ABC", REJECTED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,REJECTED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, REJECTED.size,"size is not correct")
                    assert.equal( user.orgname, REJECTED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, REJECTED.expiryTime,"expiryTime is not correct")
                    assert.exists(REJECTED.expiryTime,"expiryTime is not present")
					assert.exists(REJECTED.execFlags,"execFlags is not present")
					assert.equal(0, REJECTED.spotRate,"spotRate is not correct")
					assert.equal(0, REJECTED.forwardPoints,"forwardPoints is not correct")
					assert.equal(0, REJECTED.averagePrice,"averagePrice is not correct")
					assert.equal(0, REJECTED.cumQty,"cumQty is not correct")
					assert.equal(0, REJECTED.execId,"execId is not correct")
					assert.equal(0, REJECTED.lastPrice,"lastPrice is not correct")
					assert.equal(0, REJECTED.lastQty,"lastQty is not correct")
					assert.equal(0, REJECTED.orderId,"orderId is not correct")
                    //assert.exists(REJECTED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, REJECTED.valueDate,"tenor is not correct")
					assert.equal("REJECTED", REJECTED.executionType,"executionType is not correct")
					assert.equal(0, REJECTED.leavesQty,"leavesQty is not correct")
					assert.exists(REJECTED.counterParty,"counterParty is not present")
					assert.equal( user.username+"@"+ user.orgname, REJECTED.userFullName,"userFullName is not correct")
					assert.equal("place", REJECTED.action,"action is not correct")
					assert.equal("REJECTED", REJECTED.status,"status is not correct")
					assert.equal("RequestValidationError.InvalidDealtCcy", REJECTED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Invalid Size  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Invalid TIF test ", function () {
       // PLT-4984 - Generoc error is seen as a separate msg instead of order response
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid TIF test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidTIF_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : "ABC",//orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid TIF test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid TIF test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Invalid TIF test", function () {
                if (REJECTED !== "") {

                    console.log("Streaming Outright Order Neg - Invalid TIF Test -> rate : ")
                    console.log(REJECTED)

                    assert.exists(REJECTED.coId)
                    assert.notEqual(null,REJECTED.coId,"coId is null")
                    assert.equal(orderData.MARKET, REJECTED.type)
                    assert.equal("ABC", REJECTED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, REJECTED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, REJECTED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,REJECTED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, REJECTED.size,"size is not correct")
                    assert.equal( user.orgname, REJECTED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, REJECTED.expiryTime,"expiryTime is not correct")
                    assert.exists(REJECTED.expiryTime,"expiryTime is not present")
					assert.exists(REJECTED.execFlags,"execFlags is not present")
					assert.equal(0, REJECTED.spotRate,"spotRate is not correct")
					assert.equal(0, REJECTED.forwardPoints,"forwardPoints is not correct")
					assert.equal(0, REJECTED.averagePrice,"averagePrice is not correct")
					assert.equal(0, REJECTED.cumQty,"cumQty is not correct")
					assert.equal(0, REJECTED.execId,"execId is not correct")
					assert.equal(0, REJECTED.lastPrice,"lastPrice is not correct")
					assert.equal(0, REJECTED.lastQty,"lastQty is not correct")
					assert.equal(0, REJECTED.orderId,"orderId is not correct")
                    //assert.exists(REJECTED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, REJECTED.valueDate,"tenor is not correct")
					assert.equal("REJECTED", REJECTED.executionType,"executionType is not correct")
					assert.equal(0, REJECTED.leavesQty,"leavesQty is not correct")
					assert.exists(REJECTED.counterParty,"counterParty is not present")
					assert.equal( user.username+"@"+ user.orgname, REJECTED.userFullName,"userFullName is not correct")
					assert.equal("place", REJECTED.action,"action is not correct")
					assert.equal("REJECTED", REJECTED.status,"status is not correct")
					assert.equal("RequestValidationError.InvalidDealtCcy", REJECTED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Invalid TIF  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Null TIF test ", function () {
       // PLT-4984 - Error is seen as a separate msg instead of order response
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null TIF test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullTIF_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : "",//orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null TIF test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null TIF test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Null TIF test", function () {
                if (REJECTED !== "") {

                    console.log("Streaming Outright Order Neg - Null TIF Test -> rate : ")
                    console.log(REJECTED)

                    assert.exists(REJECTED.coId)
                    assert.notEqual(null,REJECTED.coId,"coId is null")
                    assert.equal(orderData.MARKET, REJECTED.type)
                    assert.equal("ABC", REJECTED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, REJECTED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, REJECTED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,REJECTED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, REJECTED.size,"size is not correct")
                    assert.equal( user.orgname, REJECTED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, REJECTED.expiryTime,"expiryTime is not correct")
                    assert.exists(REJECTED.expiryTime,"expiryTime is not present")
					assert.exists(REJECTED.execFlags,"execFlags is not present")
					assert.equal(0, REJECTED.spotRate,"spotRate is not correct")
					assert.equal(0, REJECTED.forwardPoints,"forwardPoints is not correct")
					assert.equal(0, REJECTED.averagePrice,"averagePrice is not correct")
					assert.equal(0, REJECTED.cumQty,"cumQty is not correct")
					assert.equal(0, REJECTED.execId,"execId is not correct")
					assert.equal(0, REJECTED.lastPrice,"lastPrice is not correct")
					assert.equal(0, REJECTED.lastQty,"lastQty is not correct")
					assert.equal(0, REJECTED.orderId,"orderId is not correct")
                    //assert.exists(REJECTED.tenor,"tenor is not present")
                    assert.equal(mdData.SOR_1W_tenor, REJECTED.valueDate,"tenor is not correct")
					assert.equal("REJECTED", REJECTED.executionType,"executionType is not correct")
					assert.equal(0, REJECTED.leavesQty,"leavesQty is not correct")
					assert.exists(REJECTED.counterParty,"counterParty is not present")
					assert.equal( user.username+"@"+ user.orgname, REJECTED.userFullName,"userFullName is not correct")
					assert.equal("place", REJECTED.action,"action is not correct")
					assert.equal("REJECTED", REJECTED.status,"status is not correct")
					assert.equal("RequestValidationError.InvalidDealtCcy", REJECTED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Null TIF  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

//need to be updated after bug fixes
//

       describe("SOR Order Neg Missing TIF test ", function () {
       // PLT-4984 - Error is seen as a separate msg instead of order response
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Missing TIF test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_MissingTIF_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     //timeInForce : orderData.GTT,
                     expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Missing TIF test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Missing TIF test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Missing TIF test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Missing TIF Test -> rate : ")
                    console.log(FAILED)

                    assert.exists(FAILED.coId)
                    assert.notEqual("FAILED",FAILED.coId,"coId is Missing")
                    assert.equal(orderData.MARKET, FAILED.type)
                    //assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
                    assert.equal(mdData.SOR_baseCurrency_EURUSD, FAILED.currency,"currency is not correct")
                    assert.equal(mdData.SOR_Symbol_EURUSD,FAILED.symbol,"symbol is not correct")
                    assert.equal(orderData.size, FAILED.size,"size is not correct")
                    assert.equal( user.orgname, FAILED.org,"org is not correct")
                    //assert.equal(orderData.ExpiryTime, FAILED.expiryTime,"expiryTime is not correct")
                    assert.exists(FAILED.expiryTime,"expiryTime is not present")
                    assert.exists(mdData.SOR_1W_tenor,FAILED.tenor,"tenor is not present")
                    //assert.equal(mdData.SOR_1W_tenor, FAILED.valueDate,"tenor is not correct")
					assert.equal( user.username+"@"+ user.orgname, FAILED.userFullName,"userFullName is not correct")
					assert.equal("place", FAILED.action,"action is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.TIFNotSpecified", FAILED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Missing TIF  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Missing ExpiryTime test ", function () {
       // GTT orders without expiryTime are seen as GTC orders, same behaviour is seen from FIX client too

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Missing ExpiryTime test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_MissingExpiry_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     //expiryTime : orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Missing ExpiryTime test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Missing ExpiryTime test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Missing ExpiryTime test", function () {
                if (NEW !== "") {

                    console.log("Streaming Outright Order Neg - Missing ExpiryTime Test -> rate : ")
                    console.log(NEW)
                    assert.exists(NEW.coId)
                    assert.notEqual("NEW",NEW.coId,"coId is Missing")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, NEW.side,"side is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Missing ExpiryTime  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

///* // below tcs fail, need to be updated after bug fixes

       describe("SOR Order Neg Invalid ExpiryTime test ", function () {
       // PLT-4984 GTT orders with invalid expiryTime are getting rejected but msg is not sent to client.
       // This is similar to other existing issues

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid ExpiryTime test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidExpiry_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     expiryTime : "abc",//orderData.ExpiryTime,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid ExpiryTime test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid ExpiryTime test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Invalid ExpiryTime test", function () {
                if (NEW !== "") {

                    console.log("Streaming Outright Order Neg - Invalid ExpiryTime Test -> rate : ")
                    console.log(NEW)
                    assert.exists(NEW.coId)
                    assert.notEqual("NEW",NEW.coId,"coId is Missing")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, NEW.side,"side is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Invalid ExpiryTime  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Invalid Tenor test ", function () {
       // PLT-4976 - this has to be updated once the bug is fixed.
       // Right now it gives generic errors
        REJECTED = ""
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid Tenor test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidTenor_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     tenor : "abc",//orderData.Tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid Tenor test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid Tenor test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED" && orderResponses[0].coId === tempReqId) {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Invalid Tenor test", function () {
                if (REJECTED !== "") {

                    console.log("Streaming Outright Order Neg - Invalid Tenor Test -> rate : ")
                    console.log(REJECTED)
                    assert.exists(REJECTED.coId)
                    assert.notEqual("NEW",REJECTED.coId,"coId is Missing")
                    assert.equal(orderData.MARKET, REJECTED.type)
                    assert.equal(orderData.GTT, REJECTED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, REJECTED.side,"side is not correct")
					assert.equal("REJECTED", REJECTED.executionType,"executionType is not correct")
					assert.equal("REJECTED", REJECTED.status,"status is not correct")
					assert.equal("RequestValidationError.InvalidTenor", REJECTED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Invalid Tenor  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Null Tenor test ", function () {
       // PLT-4976 - this has to be updated once the bug is fixed.
       // Right now it gives generic errors
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null Tenor test ************************** ' + new Date());
                 REJECTED = ""

                tempReqId = "StreamingOR_Order_Neg_NullTenor_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     tenor : "",//orderData.Tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null Tenor test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null Tenor test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED" && orderResponses[0].coId === tempReqId) {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED" && orderResponses[0].coId === tempReqId) {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Null Tenor test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Null Tenor Test -> rate : ")
                    console.log(FAILED)
                    assert.exists(FAILED.coId)
                    assert.notEqual("NEW",FAILED.coId,"coId is Missing")
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal("", FAILED.tenor,"tenor is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
					//assert.equal("FAILED", FAILED.executionType,"executionType is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.Invalid.Tenor", FAILED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Null Tenor  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Missing settlementType test ", function () {
       // PLT-4980 - Orders without settlementType tag and tenor != SPOT, goes as ESP, these orders should get rejected.

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Missing settlementType test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_MissingSettlement_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.LIMIT,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     tenor : mdData.SOR_1W_tenor,
                     //settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Missing settlementType test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Missing settlementType test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Missing settlementType test", function () {
                if (NEW !== "") {

                    console.log("Streaming Outright Order Neg - Missing settlementType Test -> rate : ")
                    console.log(NEW)
                    assert.exists(NEW.coId)
                    assert.notEqual("NEW",NEW.coId,"coId is Missing")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, NEW.side,"side is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Missing settlementType  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Invalid settlementType test ", function () {
       // PLT-4980 - this has to be updated once the bug is fixed
       // currently gives a generic error

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Invalid settlementType test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_InvalidSettlement_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.LIMIT,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "abc",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Invalid settlementType test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Invalid settlementType test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Invalid settlementType test", function () {
                if (NEW !== "") {

                    console.log("Streaming Outright Order Neg - Invalid settlementType Test -> rate : ")
                    console.log(NEW)
                    assert.exists(NEW.coId)
                    assert.notEqual("NEW",NEW.coId,"coId is Invalid")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, NEW.side,"side is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Invalid settlementType  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Null settlementType test ", function () {
       // PLT-4980 - this has to be updated once the bug is fixed

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null settlementType test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullSettlement_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.LIMIT,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null settlementType test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null settlementType test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Null settlementType test", function () {
                if (NEW !== "") {

                    console.log("Streaming Outright Order Neg - Null settlementType Test -> rate : ")
                    console.log(NEW)
                    assert.exists(NEW.coId)
                    assert.notEqual("NEW",NEW.coId,"coId is Null")
                    assert.equal(orderData.MARKET, NEW.type)
                    assert.equal(orderData.GTT, NEW.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, NEW.side,"side is not correct")
					assert.equal("NEW", NEW.status,"status is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Null settlementType  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

//need to be updated after bug fixes
//

       describe("SOR Order Neg Missing Tenor test ", function () {

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Missing Tenor test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_MissingTenor_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.MARKET,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     //tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Missing Tenor test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Missing Tenor test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Missing Tenor test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Missing Tenor Test -> rate : ")
                    console.log(FAILED)
                    assert.exists(FAILED.coId)
                    assert.notEqual("FAILED",FAILED.coId,"coId is Missing")
                    assert.equal(orderData.MARKET, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("RequestValidationError.Invalid.Tenor", FAILED.reason,"reason is not correct")
					assert.equal("FORWARD", FAILED.settlementType,"settlementType is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Missing Tenor  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Null coId test ", function () {
       // PLT-4980 - this has to be updated once the bug is fixed

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Null coId test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullSettlement_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.LIMIT,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     coId: "", //tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Null coId test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Null coId test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Null coId test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Null coId Test -> rate : ")
                    console.log(FAILED)

                    assert.notEqual("FAILED",FAILED.coId,"coId is Null")
                    assert.equal(orderData.LIMIT, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("Invalid coId, client order id can not be null or blank", FAILED.reason,"reason is not correct")
                } else {
                    console.log("Streaming Outright Order Neg - Null coId  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR Order Neg Missing coId test ", function () {
       // PLT-4980 - this has to be updated once the bug is fixed

            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright Order Neg - Missing coId test ************************** ' + new Date());
                tempReqId = "StreamingOR_Order_Neg_NullSettlement_" + reqId
                console.log("Streaming Outright Order Neg Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : orderData.LIMIT,
                     side : orderData.BUY,
                     price : "0.9",
                     currency : mdData.SOR_baseCurrency_EURUSD,
                     size : orderData.size,
                     timeInForce : orderData.GTT,
                     tenor : mdData.SOR_1W_tenor,
                     settlementType : "FORWARD",
                     preferredProviders : orderData.providers,
                     //coId: tempReqId
                 }]
                var wsreq = { orders : subrequests }
                console.log("Streaming Outright Order Neg ->  Missing coId test : request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright Order Neg - Missing coId test ->  res : " + JSON.stringify(res))
                    if (res.orderResponses) {
                        orderResponses = res.orderResponses
                        if(orderResponses[0].status === "RECEIVED") {
                            RECEIVED = orderResponses[0]
                        } else if (orderResponses[0].status === "PENDING_NEW") {
                            PENDING_NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "NEW") {
                            NEW = orderResponses[0]
                        } else if (orderResponses[0].status === "FILLED") {
                            FILLED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "REJECTED") {
                            REJECTED = orderResponses[0]
                            done()
                        } else if (orderResponses[0].status === "FAILED") {
                            FAILED = orderResponses[0]
                            done()
                        }

                    }
                }
            });

            it("SOR Order Neg Missing coId test", function () {
                if (FAILED !== "") {

                    console.log("Streaming Outright Order Neg - Missing coId Test -> rate : ")
                    console.log(FAILED)

                    assert.notEqual("FAILED",FAILED.coId,"coId is Null")
                    assert.equal(orderData.LIMIT, FAILED.type)
                    assert.equal(orderData.GTT, FAILED.timeInForce,"timeInForce is not correct")
                    assert.equal(orderData.BUY, FAILED.side,"side is not correct")
					assert.equal("FAILED", FAILED.status,"status is not correct")
					assert.equal("Invalid coId, client order id can not be null or blank", FAILED.reason,"reason is not correct")

                } else {
                    console.log("Streaming Outright Order Neg - Missing coId  test -> Not received RECEIVED message")
                    assert.equal(true,false)
                }
           });
        });


    });
};

//streamingOutrightOrderNegativeTC();
streamingOutrightOrderTC();
