const assert = require('chai').assert
const expect = require('chai').expect
const WebSocket = require('ws')


const env = require('../../config/properties').env
const user = require('../../config/properties').user
const mdData = require('../../config/properties').mdData
//const login = require('../../login').login

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId
let tempReqId
let systemReqId
let withdrawResponse
let inactiveQuote
let activeQuote
let subscriptionAck
let withdrawAck
let res
let errors
let Host
let apikey

// Login credentials should be for MDF enabled org
// For marketdata scripts, org should be getting rates in MDF
// Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

let wsconnect = function (done, cookies) {
    const websocket_url = 'wss://' + env.hostname +  '/v2/fxstream'
    connection = new WebSocket(websocket_url, [], {
        'headers': {
			'Host': env.apiHost,
			'apikey': env.apikey
        }
    })

    connection.onopen = () => {

        done()
    }

    connection.onerror = (error) => {
        console.log(`WebSocket error: ${error}`)
    }
}

let streamingOutrightRateTC = function(){

    describe("Streaming Outright Rate", function () {


        before(function (done) {
            tempReqId = ""
            wsconnect(done);
        });

/* SSO login
	    before(function (done) {
		    login(done, wsconnect)
	    });
*/
        after(function () {
            connection.close()
        });
        let reqId = Math.floor(Math.random() * 1000)


        describe("SOR Subscription test ", function () {
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright -   Subscription Rate test ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_" + reqId
                console.log("Streaming Outright - Subscription Rate Test -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                 console.log(subrequests)
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Rate Test ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Rate Test ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 2 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 2) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                    }
                }
            });

            it("Subscription test", function () {
                if(rateSubscriptionResponses !== "") {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Rate Test -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal('EUR/USD', rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }
                if (activeQuote !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(activeQuote)

                    assert.equal(mdData.SOR_Symbol_EURUSD, activeQuote.symbol)
                    assert.exists(activeQuote.bid)
                    assert.notEqual(null,activeQuote.bidLimit,"bidlimit is null")
                    assert.exists(activeQuote.offer)
                    assert.exists(activeQuote.bidLimit)
                    assert.exists(activeQuote.offerLimit)
                    assert.exists(activeQuote.time)
                    assert.equal(tempReqId, activeQuote.requestId)
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }

                if(rateUnsubscriptionResponses != "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rateUnsubscriptionResponses : ")
                    console.log(rateUnsubscriptionResponses)

                    assert.equal(mdData.typeVwap, rateUnsubscriptionResponses.request.type)
                    assert.equal(tempReqId, rateUnsubscriptionResponses.request.requestId)
                    assert.equal(true, rateUnsubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateUnsubscriptionResponses.request.depth)
                    assert.equal("success", rateUnsubscriptionResponses.status)
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR VWAP rate test ", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright -   VWAP Rate test ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_" + reqId
                console.log("Streaming Outright - Subscription Rate Test -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: tiers,
                     providers: [],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Rate Test ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Rate Test ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 2 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 2) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                    }
                }
            });

            it("SOR VWAP rate test", function () {
                if (activeQuote !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(activeQuote)

                    assert.equal(mdData.SOR_Symbol_EURUSD, activeQuote.symbol)
                    assert.exists(activeQuote.bid)
                    assert.notEqual(null,activeQuote.bid,"bid is null")
                    assert.exists(activeQuote.offer)
                    assert.notEqual(null,activeQuote.offer,"offer is null")
                    assert.exists(activeQuote.bidLimit)
                    assert.notEqual(null,activeQuote.bidLimit,"bidlimit is null")
                    assert.exists(activeQuote.offerLimit)
                    assert.notEqual(null,activeQuote.offerLimit,"offerLimit is null")
                    assert.exists(activeQuote.time)
                    assert.equal(tempReqId, activeQuote.requestId)
                    for (i=0; i<tiers.length; i++) {
                        assert.equal(tiers[i], activeQuote.bidLimit[i])
                        assert.equal(tiers[i], activeQuote.offerLimit[i])
                    }
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }
           });

        });

        describe("SOR FULL rate test ", function () {

            before(function (done) {
                console.log('*************************** Streaming Outright -   FULL Rate test ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_" + reqId
                console.log("Streaming Outright - Subscription Rate Test -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeFull,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     providers: [],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Rate Test ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Rate Test ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 2 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 2) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeFull + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                    }
                }
            });

            it("SOR FULL rate test", function () {
                if (activeQuote !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(activeQuote)

                    assert.equal(mdData.SOR_Symbol_EURUSD, activeQuote.symbol)
                    assert.exists(activeQuote.bid)
                    assert.notEqual(null,activeQuote.bid,"bid is null")
                    assert.exists(activeQuote.offer)
                    assert.notEqual(null,activeQuote.offer,"offer is null")
                    assert.exists(activeQuote.bidLimit)
                    assert.notEqual(null,activeQuote.bidLimit,"bidlimit is null")
                    assert.exists(activeQuote.offerLimit)
                    assert.notEqual(null,activeQuote.offerLimit,"offerLimit is null")
                    assert.exists(activeQuote.time)
                    assert.equal(tempReqId, activeQuote.requestId)
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }
           });

        });

        describe("SOR TOB rate test ", function () {
            before(function (done) {
                console.log('*************************** Streaming Outright -   TOB Rate test ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_" + reqId
                console.log("Streaming Outright - Subscription Rate Test -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeTob,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     providers: [],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Rate Test ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Rate Test ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 2 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 2) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeTob + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                    }
                }
            });

            it("SOR TOB rate test", function () {
                if (activeQuote !== "") {
                    console.log("Streaming Outright - Subscription Rate Test -> rate : ")
                    console.log(activeQuote)

                    assert.equal(mdData.SOR_Symbol_EURUSD, activeQuote.symbol)
                    assert.exists(activeQuote.bid)
                    assert.equal(1, activeQuote.bid.length )
                    assert.notEqual(null,activeQuote.bid,"bid is null")
                    assert.exists(activeQuote.offer)
                    assert.notEqual(null,activeQuote.offer,"offer is null")
                    assert.equal(1, activeQuote.offer.length )
                    assert.exists(activeQuote.bidLimit)
                    assert.notEqual(null,activeQuote.bidLimit,"bidlimit is null")
                    assert.equal(1, activeQuote.bidLimit.length )
                    assert.exists(activeQuote.offerLimit)
                    assert.notEqual(null,activeQuote.offerLimit,"offerLimit is null")
                    assert.equal(1, activeQuote.offerLimit.length )
                    assert.exists(activeQuote.time)
                    assert.equal(tempReqId, activeQuote.requestId)
                } else {
                    console.log("Streaming Outright -   Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }
           });

        });

       describe("SOR FULL rate term subscription test ", function () {

            before(function (done) {
                console.log('*************************** Streaming Outright -   FULL Rate term subscription test ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_" + reqId
                console.log("Streaming Outright - Term Subscription Rate Test -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeFull,
                     org:  user.orgname,
                     customAggregation: true,
					 dealtCurrency: mdData.SOR_termCurrency_EURUSD,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     providers: [],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Term Subscription Rate Test ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Term Subscription Rate Test ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 2 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 2) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeFull + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                    }
                }
            });

            it("SOR FULL rate test", function () {
                if (activeQuote !== "") {
                    console.log("Streaming Outright - Term Subscription Rate Test -> rate : ")
                    console.log(activeQuote)

                    assert.equal(mdData.SOR_Symbol_EURUSD, activeQuote.symbol)
                    assert.exists(activeQuote.bid)
                    assert.notEqual(null,activeQuote.bid,"bid is null")
                    assert.exists(activeQuote.offer)
                    assert.notEqual(null,activeQuote.offer,"offer is null")
                    assert.exists(activeQuote.bidLimit)
                    assert.notEqual(null,activeQuote.bidLimit,"bidlimit is null")
                    assert.exists(activeQuote.offerLimit)
                    assert.notEqual(null,activeQuote.offerLimit,"offerLimit is null")
                    assert.exists(activeQuote.time)
                    assert.equal(tempReqId, activeQuote.requestId)
                } else {
                    console.log("Streaming Outright -   Term Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }
           });

       });

       describe("SOR VWAP rate term subscription test", function () {
            let tiers = [1000000,2000000,3000000 ]
            before(function (done) {
                console.log('*************************** Streaming Outright -   VWAP Rate term subscription test ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_" + reqId
                console.log("Streaming Outright - Term Subscription Rate Test -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
					 dealtCurrency: mdData.SOR_termCurrency_EURUSD,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: tiers,
                     providers: [],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Term Subscription Rate Test ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Term Subscription Rate Test ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 2 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 2) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                    }
                }
            });

            it("SOR VWAP rate test", function () {
                if (activeQuote !== "") {
                    console.log("Streaming Outright - Term Subscription Rate Test -> rate : ")
                    console.log(activeQuote)

                    assert.equal(mdData.SOR_Symbol_EURUSD, activeQuote.symbol)
                    assert.exists(activeQuote.bid)
                    assert.notEqual(null,activeQuote.bid,"bid is null")
                    assert.exists(activeQuote.offer)
                    assert.notEqual(null,activeQuote.offer,"offer is null")
                    assert.exists(activeQuote.bidLimit)
                    assert.notEqual(null,activeQuote.bidLimit,"bidlimit is null")
                    assert.exists(activeQuote.offerLimit)
                    assert.notEqual(null,activeQuote.offerLimit,"offerLimit is null")
                    assert.exists(activeQuote.time)
                    assert.equal(tempReqId, activeQuote.requestId)
                    for (i=0; i<tiers.length; i++) {
                        assert.equal(tiers[i], activeQuote.bidLimit[i])
                        assert.equal(tiers[i], activeQuote.offerLimit[i])
                    }
                } else {
                    console.log("Streaming Outright -   Term Subscription Rate test -> Not received required rates")
                    assert.equal(true,false)
                }
           });

       });

    });
};

let streamingOutrightRateNegativeTC = function(){

// =================== all the negative tcs with error code "request.validation.tradingrelationship.sd.cptyaorg.relationship.missing" should be revisited or corrected

    describe("Streaming Outright Rate Negative scenario ", function () {

        before(function (done) {
            wsconnect(done);
        });

        after(function () {
            connection.close()
        });


        afterEach(function () {
            console.log('about to run a test')
            rateSubscriptionResponses = ""
            flag = false
        });

        let reqId = Math.floor(Math.random() * 1000)


        describe("SOR Subscription - nonSOR ", function () {
		// Ensure AUD/CAD or AUD/JPY is the only one configured, its SOR cps like AUD/CAD_1W etc are not there from LP->FI.
		// For now, AUD/CAD is the only one supported from NTFX to FI
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - nonSOR  ************************** ' + new Date());
                //console.log("==========rateSubscriptionResponses================"+JSON.stringify(rateSubscriptionResponses))
                tempReqId = "StreamingOR_Subscription_nonSOR_" + reqId
                console.log("Streaming Outright - Subscription nonSOR -> reqId = " + reqId)

                var subrequests = [{
                     symbol : "AUD/CAD",
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription nonSOR ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription nonSOR ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(JSON.stringify(rateSubscriptionResponses.errors) !== "[]") {
                            rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - nonSOR CP", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription nonSOR -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    console.log(rateSubscriptionResponses.errors[0].errorCode)
                    console.log(rateSubscriptionResponses.errors[0].errorMessage)
                    assert.equal('AUD/CAD', rateSubscriptionResponses.request.symbol)
                    //assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    //assert.exists(rateSubscriptionResponses.request.tiers)
                    // customAggregation comes as false in negative scenarios, logged a bug
                    assert.equal(false, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    //assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    //assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(0,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("INVALID_INSTRUMENT",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription nonSOR -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Invalid CP ", function () {
            let dealtAmt = '1000000'
            let amount = '1000000.0'
            //rateSubscriptionResponses = ""
            before(function (done) {
                console.log('*************************** Streaming Outright - Invalid CP  ************************** ' + new Date());
                console.log("==========rateSubscriptionResponses================"+JSON.stringify(rateSubscriptionResponses))
                tempReqId = "StreamingOR_Subscription_InvalidCP_" + reqId
                console.log("Streaming Outright - Subscription Invalid CP -> reqId = " + reqId)

                var subrequests = [{
                     symbol : "ABC/XY1",
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Invalid CP ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Invalid CP ->  res : " + JSON.stringify(res))

                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses ) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.errors) { done () }
                    }
                }
            });

            it("Subscription test - Invalid CP", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Invalid CP -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal('ABC/XY1', rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(106,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("currency pair not supported.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright -   Subscription Invalid CP -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Null CP ", function () {
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Null CP  ************************** ' + new Date());
                console.log("==========rateSubscriptionResponses================"+JSON.stringify(rateSubscriptionResponses))
                tempReqId = "StreamingOR_Subscription_nullCP_" + reqId
                console.log("Streaming Outright - Subscription Null CP -> reqId = " + reqId)

                var subrequests = [{
                     symbol : "",
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Null CP ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Null CP ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.errors) { done () }
                    }
                }
            });

            it("Subscription test - Null CP", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Null CP -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal('', rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(102,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("ccy pair is required.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Null CP -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Missing CP tag tag", function () {
                    let dealtAmt = '1000000'
                    let amount = '1000000.0'

                    before(function (done) {
                        console.log('*************************** Streaming Outright - Missing CP tag tag ************************** ' + new Date());
                        console.log("==========rateSubscriptionResponses================"+JSON.stringify(rateSubscriptionResponses))
                        tempReqId = "StreamingOR_Subscription_nullCP_" + reqId
                        console.log("Streaming Outright - Subscription Missing CP tag tag-> reqId = " + reqId)

                        var subrequests = [{
                            // symbol : "",
                             type : mdData.typeVwap,
                             org:  user.orgname,
                             customAggregation: true,
                             settlementType: mdData.SOR_settlementType,
                             tenor: mdData.SOR_1W_tenor,
                             tiers: [1000000,2000000,3000000],
                             requestId: tempReqId
                         }]
                        var wsreq = { rateSubscriptions : subrequests }
                        console.log("Streaming Outright - Subscription Missing CP tag tag->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                        connection.send(JSON.stringify(wsreq));
                        i = 1

                        activeQuote = ""
                        flag = false
                        connection.onmessage = (e) => {
                            res = JSON.parse(e.data)
                            console.log("Streaming Outright - Subscription Missing CP tag tag->  res : " + JSON.stringify(res))
                            if (res.rate) {
                                rate = res.rate
                                if(i < 3 && rate.bidLimit !== null) {
                                    activeQuote = rate
                                    i= i + 1
                                } else if (rate.bidLimit === null) {
                                    inactiveQuote = rate
                                } else if (i === 3) {
                                    connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                                    i++
                                }
                            } else if(res.rateUnsubscriptionResponses) {
                                rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                                done()
                            } else if (res.subscriptionAck) {
                                subscriptionAck = res.subscriptionAck[0]
                            } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                                rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                                if(rateSubscriptionResponses.errors) { done () }
                            }
                        }
                    });

                    it("Subscription test - Missing CP tag", function () {
                        if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                            // validate    subscriptionAck
                            console.log("Streaming Outright - Subscription Missing CP tag tag-> rateSubscriptionResponses : ")
                            console.log(rateSubscriptionResponses)
                            //assert.equal('', rateSubscriptionResponses.request.symbol)
                            assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                            assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                            assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                            assert.exists(rateSubscriptionResponses.request.tiers)
                            assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                            assert.equal(0, rateSubscriptionResponses.request.depth)
                            assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                            assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                            assert.equal(102,rateSubscriptionResponses.errors[0].errorCode)
                            assert.equal("ccy pair is required.",rateSubscriptionResponses.errors[0].errorMessage)
                        } else {
                            console.log("Streaming Outright - Subscription Missing CP tag tag-> Not received rateSubscriptionResponses")
                            assert.equal(true,false)
                        }

                   });

                });

        describe("SOR Subscription - Invalid Type ", function () {
        // bug  PLT-4982 - error message should be part of subscrition response
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Invalid Type  ************************** ' + new Date());
                console.log("==========rateSubscriptionResponses================"+JSON.stringify(rateSubscriptionResponses))
                tempReqId = "StreamingOR_Subscription_InvalidType_" + reqId
                console.log("Streaming Outright - Subscription Invalid Type -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : "abc",
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Invalid Type ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Invalid Type ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.errors) { done () }
                    } else if (res.errors) {
                        errors = res.errors[0]
                        done()
                    }
                }
            });

            it("Subscription test - Invalid Type", function () {
                console.log("================rateSubscriptionResponses==" + rateSubscriptionResponses)
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Invalid Type -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal("abc", rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(1,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Not a valid request.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Invalid Type -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

                if(errors !== "") {
                    console.log("Streaming Outright - Subscription Invalid Type -> rateSubscriptionResponses : ")
                    console.log(errors)
                    assert.equal(1,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Not a valid request.",rateSubscriptionResponses.errors[0].errorMessage)
                }

           });

        });

        describe("SOR Subscription - Missing Type tag ", function () {
        // bug  PLT-4982 - error message should be part of subscrition response
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Missing Type tag  ************************** ' + new Date());
                console.log("==========rateSubscriptionResponses================"+JSON.stringify(rateSubscriptionResponses))
                tempReqId = "StreamingOR_Subscription_InvalidType_" + reqId
                console.log("Streaming Outright - Subscription Missing Type tag -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     //type : "abc",
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Missing Type tag ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Missing Type tag ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.errors) { done () }
                    } else if (res.errors) {
                        errors = res.errors[0]
                        done()
                    }
                }
            });

            it("Subscription test - Missing Type tag", function () {
                console.log("================rateSubscriptionResponses==" + rateSubscriptionResponses)
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Missing Type tag -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    //assert.equal("abc", rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(103,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("type is required.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Missing Type tag -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

                if(errors !== "") {
                    console.log("Streaming Outright - Subscription Missing Type tag -> rateSubscriptionResponses : ")
                    console.log(errors)
                    assert.equal(103,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("type is required.",rateSubscriptionResponses.errors[0].errorMessage)
                }

           });

        });

        describe("SOR Subscription - Null Type ", function () {
        // bug PLT-4982 - error message should be part of subscrition response
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Null Type  ************************** ' + new Date());
                console.log("==========rateSubscriptionResponses================"+JSON.stringify(rateSubscriptionResponses))
                tempReqId = "StreamingOR_Subscription_NullType_" + reqId
                console.log("Streaming Outright - Subscription Null Type -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : "",
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Null Type ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Null Type ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.errors) { done () }
                    } else if (res.errors) {
                        errors = res.errors[0]
                        done()
                    }
                }
            });

            it("Subscription test - Null Type", function () {
                console.log("================rateSubscriptionResponses==" + rateSubscriptionResponses)
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Null Type -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal("abc", rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(1,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Not a valid request.",rateSubscriptionResponses.errors[0].errorMessage)
                }

                if(errors !== "" && errors !== undefined) {
                    console.log("Streaming Outright - Subscription Null Type -> rateSubscriptionResponses : ")
                    console.log(errors)
                    assert.equal(1,errors.errorCode)
                    assert.equal("Not a valid request.",errors.errorMessage)
                }

           });

        });

        describe("SOR Subscription - Invalid Org ", function () {
		// Ensure AUD/CAD is the only one configured, its SOR cps like AUD/CAD_1W etc are not there from LP->FI.
		// For now, AUD/CAD is the only one supported from NTFX to FI
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Invalid Org  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_InvalidOrg_" + reqId
                console.log("Streaming Outright - Subscription Invalid Org -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org: "abc",
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Invalid Org ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Invalid Org ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.errors) { done () }
                    }
                }
            });

            it("Subscription test - Invalid Org CP", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Invalid Org -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal("abc", rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(108,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Not a valid Org.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Invalid Org -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Missing Org tag ", function () {
		// Ensure AUD/CAD is the only one configured, its SOR cps like AUD/CAD_1W etc are not there from LP->FI.
		// For now, AUD/CAD is the only one supported from NTFX to FI
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Missing Org tag  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_InvalidOrg_" + reqId
                console.log("Streaming Outright - Subscription Missing Org tag -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     //org: "abc",
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Missing Org tag ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Missing Org tag ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.errors) { done () }
                    }
                }
            });

            it("Subscription test - Missing Org tag CP", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Missing Org tag -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal("failed",rateSubscriptionResponses.status)
                    //assert.equal("Not a valid Org.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Missing Org tag -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Null Org ", function () {
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Null Org  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_NullOrg_" + reqId
                console.log("Streaming Outright - Subscription Null Org -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org: "",
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Null Org ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Null Org ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.errors) { done () }
                    }
                }
            });

            it("Subscription test - Null Org CP", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Null Org -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal("", rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(108,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Not a valid Org.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Null Org -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - CustomAggregation=False ", function () {
        // Bug PLT-4981 - subscription should fail when customAggregation is false. It should not give ESP rates
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - CustomAggregation=False  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_CustAggFalse_" + reqId
                console.log("Streaming Outright - Subscription CustomAggregation=False -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: false,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription CustomAggregation=False ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription CustomAggregation=False ->  res : " + JSON.stringify(res))

                    if (res.rate) {
                        rate = res.rate
                        console.log("==========i====" + i)
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }

                }
            });

            it("Subscription test - CustomAggregation=False", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription CustomAggregation=False -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(false, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(132,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Only Custom Aggregation supported for Streaming outright Subscription ",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription CustomAggregation=False -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Null CustomAggregation ", function () {
        // Bug PLT-4981 - subscription should fail when customAggregation is false. It should not give ESP rates
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Null CustomAggregation  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_CustAggNull_" + reqId
                console.log("Streaming Outright - Subscription Null CustomAggregation -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: '',
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Null CustomAggregation ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Null CustomAggregation ->  res : " + JSON.stringify(res))
                    if (res.rate && res.rate.requestId === tempReqId) {
                        rate = res.rate
                        console.log("==========i====" + i)
                        if(i < 2 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 2) {
                        console.log("=======2=======unsubscription================")
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":false, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        console.log("--------rateSubscriptionResponses---" +  JSON.stringify(rateSubscriptionResponses))
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Null CustomAggregation", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Null CustomAggregation -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(false, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(132,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Only Custom Aggregation supported for Streaming outright Subscription ",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Two-Way Subscription Null CustomAggregation -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Missing CustomAggregation tag ", function () {
        // Bug PLT-4981 - subscription should fail when customAggregation is false. It should not give ESP rates
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Missing CustomAggregation tag  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_CustAggNull_" + reqId
                console.log("Streaming Outright - Subscription Missing CustomAggregation tag -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     //customAggregation: '',
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Missing CustomAggregation tag ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Missing CustomAggregation tag ->  res : " + JSON.stringify(res))
                    if (res.rate && res.rate.requestId === tempReqId) {
                        rate = res.rate
                        console.log("==========i====" + i)
                        if(i < 2 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 2) {
                        console.log("=======2=======unsubscription================")
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":false, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        console.log("--------rateSubscriptionResponses---" +  JSON.stringify(rateSubscriptionResponses))
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Missing CustomAggregation tag", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Missing CustomAggregation tag -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    //assert.equal(false, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(132,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Only Custom Aggregation supported for Streaming outright Subscription ",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Two-Way Subscription Missing CustomAggregation tag -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Invalid settlementType ", function () {
        // Bug PLT-4982 - subscription should fail when settlementType is invalid/null and error message should be part of subscriptionResponse
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Invalid settlementType  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_InvalidSettType_" + reqId
                console.log("Streaming Outright - Subscription Invalid settlementType -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: "abc", //mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Invalid settlementType ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Invalid settlementType ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {

 // uncomment these lines once the bug related to settlementType is fixed
                          rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                          console.log("============"+JSON.stringify(rateSubscriptionResponses))
                          if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }

                    }
                }
            });

            it("Subscription test - Invalid settlementType", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Invalid settlementType -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(106,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("currency pair not supported.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Two-Way Subscription Invalid settlementType -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Null settlementType ", function () {
        // Bug PLT-4982 - subscription should fail when settlementType is invalid/null and error message should be part of subscriptionResponse
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Null settlementType  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_NullSettType_" + reqId
                console.log("Streaming Outright - Subscription Null settlementType -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: "", //mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Null settlementType ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Null settlementType ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Null settlementType", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Null settlementType -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(106,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("currency pair not supported.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Two-Way Subscription Null settlementType -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Missing settlementType tag ", function () {
        // Bug PLT-4982 - subscription should fail when settlementType is invalid/null and error message should be part of subscriptionResponse
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Missing settlementType tag  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_InvalidSettType_" + reqId
                console.log("Streaming Outright - Subscription Missing settlementType tag -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     //settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Missing settlementType tag ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Missing settlementType tag ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {

 // uncomment these lines once the bug related to settlementType is fixed
                          rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                          console.log("============"+JSON.stringify(rateSubscriptionResponses))
                          if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }

                    }
                }
            });

            it("Subscription test - Missing settlementType tag", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Missing settlementType tag -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    //assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(133,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Invalid tenor value for SPOT subscription",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Two-Way Subscription Missing settlementType tag -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Invalid Tenor ", function () {
        // Bug PLT-4980 - subscription should fail when tenor is invalid/null/brokenDate
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Invalid Tenor  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_InvalidTenor_" + reqId
                console.log("Streaming Outright - Subscription Invalid Tenor -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: "abc", //mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Invalid Tenor ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Invalid Tenor ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Invalid Tenor", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Invalid Tenor -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    //assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    //assert.exists(rateSubscriptionResponses.request.tiers)
                    //assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal("failed", rateSubscriptionResponses.status)
                    assert.equal(0,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("INTERNAL_SERVER_ERROR",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Invalid Tenor -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Null Tenor ", function () {
        // Bug PLT-4980 - subscription should fail when tenor is invalid/null/brokenDate
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Null Tenor  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_NullTenor_" + reqId
                console.log("Streaming Outright - Subscription Null Tenor -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: "", //mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Null Tenor ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Null Tenor ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Null Tenor", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Null Tenor -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal('', rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(130,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Tenor is null for Non Spot request",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Null Tenor -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - MissingTenor tag ", function () {
        // Bug PLT-4980 - subscription should fail when tenor is invalid/null/brokenDate
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - MissingTenor tag  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_InvalidTenor_" + reqId
                console.log("Streaming Outright - Subscription MissingTenor tag -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     //tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription MissingTenor tag ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription MissingTenor tag ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - MissingTenor tag", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription MissingTenor tag -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    //assert.equal("abc", rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(130,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Tenor is null for Non Spot request",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription MissingTenor tag -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Broken date ", function () {
        // Bug PLT-4980 - subscription should fail when tenor is invalid/null/brokenDate
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Broken date  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_BrokenDate_" + reqId
                console.log("Streaming Outright - Subscription Broken date -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: "28-08-2025", //mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Broken date ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Broken date ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Broken date", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
    //Streaming Outright - Subscription Broken date ->  res : {"rateSubscriptionResponses":[{"request":{"symbol":"EUR/USD","org":
    //        "pfOrg","requestId":"StreamingOR_Subscription_BrokenDate_783","customAggregation":false,"depth":0},"status":"failed",
     //       "errors":[{"errorCode":0,"errorMessage":"INTERNAL_SERVER_ERROR"}]}]}

                    console.log("Streaming Outright - Subscription Broken date -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    //assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    //assert.exists(rateSubscriptionResponses.request.tiers)
                    //assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal("failed", rateSubscriptionResponses.status)
                    //assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(0,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("INTERNAL_SERVER_ERROR",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Broken date -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Without tiers", function () {
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Without tiers ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_WithoutTiers_" + reqId
                console.log("Streaming Outright - Subscription Without tiers-> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     //tiers: [1000000,2000000,3000000],
                     requestId: tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Without tiers->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Without tiers->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Without tiers", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Without tiers-> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal(tempReqId, rateSubscriptionResponses.request.requestId)
                    //assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(104,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("Tiers is mandatory for RateType VWAP.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright - Subscription Without tiers-> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Null requestId ", function () {
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Null requestId  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_NullReqId_" + reqId
                console.log("Streaming Outright - Subscription Null requestId -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000],
                     requestId: '' //tempReqId
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Null requestId ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Null requestId ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses  &&  res.rateSubscriptionResponses[0].request.requestId === tempReqId) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            console.log("===== error occured =====")
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Null requestId", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Null requestId -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.equal('', rateSubscriptionResponses.request.requestId)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(106,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("currency pair not supported.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright -   Subscription Null requestId -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

        describe("SOR Subscription - Missing requestId tag ", function () {
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** Streaming Outright - Missing requestId tag  ************************** ' + new Date());
                tempReqId = "StreamingOR_Subscription_MissingReqId_" + reqId
                console.log("Streaming Outright - Subscription Missing requestId tag -> reqId = " + reqId)

                var subrequests = [{
                     symbol : mdData.SOR_Symbol_EURUSD,
                     type : mdData.typeVwap,
                     org:  user.orgname,
                     customAggregation: true,
                     settlementType: mdData.SOR_settlementType,
                     tenor: mdData.SOR_1W_tenor,
                     tiers: [1000000,2000000,3000000]

                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("Streaming Outright - Subscription Missing requestId tag ->  streamingOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                activeQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Streaming Outright - Subscription Missing requestId tag ->  res : " + JSON.stringify(res))
                    if (res.rate) {
                        rate = res.rate
                        if(i < 3 && rate.bidLimit !== null) {
                            activeQuote = rate
                            i= i + 1
                        } else if (rate.bidLimit === null) {
                            inactiveQuote = rate
                        } else if (i === 3) {
                            connection.send('{"rateUnsubscriptions":[{"requestId":"' + tempReqId + '", "customAggregation":true, "type":"' + mdData.typeVwap + '"}]}')
                            i++
                        }
                    } else if(res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses[0]
                        done()
                    } else if (res.subscriptionAck) {
                        subscriptionAck = res.subscriptionAck[0]
                    } else if (res.rateSubscriptionResponses) {
                        rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                        if(rateSubscriptionResponses.status !== "success") {
                            done ()
                        }
                    }
                }
            });

            it("Subscription test - Missing requestId tag", function () {
                if(rateSubscriptionResponses !== "" && rateSubscriptionResponses !== undefined ) {
                    // validate    subscriptionAck
                    console.log("Streaming Outright - Subscription Missing requestId tag -> rateSubscriptionResponses : ")
                    console.log(rateSubscriptionResponses)
                    assert.equal(mdData.SOR_Symbol_EURUSD, rateSubscriptionResponses.request.symbol)
                    assert.equal(mdData.typeVwap, rateSubscriptionResponses.request.type)
                    assert.equal( user.orgname, rateSubscriptionResponses.request.org)
                    assert.exists(rateSubscriptionResponses.request.tiers)
                    assert.equal(true, rateSubscriptionResponses.request.customAggregation)
                    assert.equal(0, rateSubscriptionResponses.request.depth)
                    assert.equal(mdData.SOR_1W_tenor, rateSubscriptionResponses.request.tenor)
                    assert.equal(mdData.SOR_settlementType, rateSubscriptionResponses.request.settlementType)
                    assert.equal(101,rateSubscriptionResponses.errors[0].errorCode)
                    assert.equal("request id is required.",rateSubscriptionResponses.errors[0].errorMessage)
                } else {
                    console.log("Streaming Outright -   Subscription Missing requestId tag -> Not received rateSubscriptionResponses")
                    assert.equal(true,false)
                }

           });

        });

    });

};

streamingOutrightRateTC();
//streamingOutrightRateNegativeTC();
