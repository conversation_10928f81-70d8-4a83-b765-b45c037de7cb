========================== benchmark script ================================

  Benchmark negative scenario -
====after login===
websocket_url: ws://cqtop10-07:10180/fxstream
    invalid JSON test
request ---> {"fxbenchmarkSubscriptions":[{"symbol":"ABC/XYZ","priceSource":"FXB"}]}
res ={"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
errors : [{"errorCode":1,"errorMessage":"Not a valid request."}]
subscription response: undefined
      √ Benchmark subscription invalidJSON CP test
    InvalidCP subscription test
request ---> {"fxbenchmarkSubscriptions":[{"symbol":"ABC/XYZ","priceSource":"FXB"}]}
res ={"fxbenchmarkSubscriptionResponses":[{"request":{"symbol":"ABC/XYZ","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":204,"errorMessage":"Currency pair not supported."}]}]}
fxbenchmarkSubscriptionResponses : [{"request":{"symbol":"ABC/XYZ","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":204,"errorMessage":"Currency pair not supported."}]}]
subscription response: [{"request":{"symbol":"ABC/XYZ","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":204,"errorMessage":"Currency pair not supported."}]}]
      √ Benchmark subscription invalid CP test
    UnsupportedCP subscription test
request --->{"fxbenchmarkSubscriptions":[{"symbol":"JPY/AED","priceSource":"FXB"}]}
res ={"fxbenchmarkSubscriptionResponses":[{"request":{"symbol":"JPY/AED","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":204,"errorMessage":"Currency pair not supported."}]}]}
fxbenchmarkSubscriptionResponses : [{"request":{"symbol":"JPY/AED","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":204,"errorMessage":"Currency pair not supported."}]}]
subscription response: [{"request":{"symbol":"JPY/AED","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":204,"errorMessage":"Currency pair not supported."}]}]
      √ Benchmark subscription unspported CP test
    Unsupported PriceSource test
request ---> {"fxbenchmarkSubscriptions":[{"symbol":"AUD/CAD","priceSource":"INT"}]}
res ={"fxbenchmarkSubscriptionResponses":[{"request":{"symbol":"AUD/CAD","priceSource":"INT"},"status":"failed","errors":[{"errorCode":202,"errorMessage":"Valid Price Source is required."}]}]}
fxbenchmarkSubscriptionResponses : [{"request":{"symbol":"AUD/CAD","priceSource":"INT"},"status":"failed","errors":[{"errorCode":202,"errorMessage":"Valid Price Source is required."}]}]
subscription response: [{"request":{"symbol":"AUD/CAD","priceSource":"INT"},"status":"failed","errors":[{"errorCode":202,"errorMessage":"Valid Price Source is required."}]}]
      √ Benchmark subscription unuspported priceSource test
    Duplicate subscription test
res ={"fxbenchmarkSubscriptionResponses":[{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]}
res ={"fxbenchmarkSubscriptionResponses":[{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":205,"errorMessage":"Currency pair already subscribed."}]}]}
fxbenchmarkSubscriptionResponses : [{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":205,"errorMessage":"Currency pair already subscribed."}]}]
in tmptrue
duplicateSubscriptionResponse: [{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"failed","errors":[{"errorCode":205,"errorMessage":"Currency pair already subscribed."}]}]
      √ benchmark duplicate subscription test
request --->{"fxbenchmarkUnsubscriptions":[{"symbol":"USD/CAD","priceSource":"FXB"}]}

  Benchmark
websocket_url: ws://cqtop10-07:10180/fxstream
    Subscription test
res ={"fxbenchmarkSubscriptionResponses":[{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]}
fxbenchmarkSubscriptionResponses : [{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]
subscription response: [{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]
      √ subscription test
    Unsubscription test
res :{"fxbenchmark":{"currencyPair":"USD/CAD","rate":1.18953,"guid":"G-4796976cd-1766b001f3e-FXB-22afbac","timestamp":1608112938814}}
res :{"fxbenchmark":{"currencyPair":"USD/CAD","rate":1.18953,"guid":"G-4796976cd-1766b001f9e-FXB-22afc4d","timestamp":1608112938910}}
res :{"fxbenchmark":{"currencyPair":"USD/CAD","rate":1.18953,"guid":"G-4796976cd-1766b00200d-FXB-22afcf8","timestamp":1608112939021}}
res :{"fxbenchmarkUnsubscriptionResponses":[{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]}
fxbenchmarkUnsubscriptionResponses : [{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]
      √ benchmark unsubscription test (309ms)
res :{"fxbenchmarkSubscriptionResponses":[{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]}
    Rate test
res ={"fxbenchmarkUnsubscriptionResponses":[{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]}
res ={"fxbenchmarkSubscriptionResponses":[{"request":{"symbol":"USD/CAD","priceSource":"FXB"},"status":"success"}]}
res ={"fxbenchmark":{"currencyPair":"USD/CAD","rate":1.18953,"guid":"G-4796976cd-1766b00219b-FXB-22aff8a","timestamp":1608112939419}}
fxbenchmark : {"currencyPair":"USD/CAD","rate":1.18953,"guid":"G-4796976cd-1766b00219b-FXB-22aff8a","timestamp":1608112939419}
fxbenchmark response: {"currencyPair":"USD/CAD","rate":1.18953,"guid":"G-4796976cd-1766b00219b-FXB-22aff8a","timestamp":1608112939419}
      √ Benchmark rate test


  8 passing (5s)

res ={"fxbenchmark":{"currencyPair":"USD/CAD","rate":1.18953,"guid":"G-4796976cd-1766b002200-FXB-22b0031","timestamp":1608112939520}}
fxbenchmark : {"currencyPair":"USD/CAD","rate":1.18953,"guid":"G-4796976cd-1766b002200-FXB-22b0031","timestamp":1608112939520}
C:\Users\<USER>\AppData\Roaming\npm\node_modules\mocha\lib\runner.js:911
    throw err;
    ^

Error: done() called multiple times in hook <Benchmark   Rate test  "before all" hook> of file D:\Testing\Platform\WS2\test\ws-benchmark-test.js
    at createMultipleDoneError (C:\Users\<USER>\AppData\Roaming\npm\node_modules\mocha\lib\errors.js:289:13)
    at multiple (C:\Users\<USER>\AppData\Roaming\npm\node_modules\mocha\lib\runnable.js:294:24)
    at done (C:\Users\<USER>\AppData\Roaming\npm\node_modules\mocha\lib\runnable.js:305:14)
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\mocha\lib\runnable.js:422:7
    at WebSocket.connection.onmessage (D:\Testing\Platform\WS2\test\ws-benchmark-test.js:103:10)
    at WebSocket.onMessage (D:\Testing\Platform\WS2\node_modules\ws\lib\event-target.js:120:16)
    at WebSocket.emit (events.js:321:20)
    at Receiver.receiverOnMessage (D:\Testing\Platform\WS2\node_modules\ws\lib\websocket.js:801:20)
    at Receiver.emit (events.js:321:20)
    at Receiver.dataMessage (D:\Testing\Platform\WS2\node_modules\ws\lib\receiver.js:436:14)
    at D:\Testing\Platform\WS2\node_modules\ws\lib\receiver.js:393:23
    at D:\Testing\Platform\WS2\node_modules\ws\lib\permessage-deflate.js:306:9
    at D:\Testing\Platform\WS2\node_modules\ws\lib\permessage-deflate.js:385:7
    at afterWrite (_stream_writable.js:528:5)
    at onwrite (_stream_writable.js:508:7)
    at InflateRaw.afterTransform (_stream_transform.js:98:3)
    at Zlib.processCallback (zlib.js:578:8) {
  code: 'ERR_MOCHA_MULTIPLE_DONE',
  valueType: 'undefined',
  value: undefined
}


====================================== Stop Order ================================================

  19 passing (2m)
  4 failing

  1) Stop Order -
       Stop DAY Buy Term V4 Order Test
         "before all" hook for "Stop DAY Sell Term V4 ExecFlag NoCross Order Test":
     Error: Timeout of 20000ms exceeded. For async tests and hooks, ensure "done()" is called; if returning a Promise, ensure it resolves. (D:\Testing\Platform\WS2\test\ws-StopOrder-test.js)
      at listOnTimeout (internal/timers.js:549:17)
      at processTimers (internal/timers.js:492:7)

  2) Negative Stop Order TCs -
        Negative Scenario - Null Currency
         "before all" hook for " Negative Scenario - Null Currency ":
     Error: Timeout of 20000ms exceeded. For async tests and hooks, ensure "done()" is called; if returning a Promise, ensure it resolves. (D:\Testing\Platform\WS2\test\ws-StopOrder-test.js)
      at listOnTimeout (internal/timers.js:549:17)
      at processTimers (internal/timers.js:492:7)

  3) Negative Stop Order TCs -
        Negative Scenario - Blank coID
         "before all" hook for " Negative Scenario - Blank coID ":
     Error: Timeout of 20000ms exceeded. For async tests and hooks, ensure "done()" is called; if returning a Promise, ensure it resolves. (D:\Testing\Platform\WS2\test\ws-StopOrder-test.js)
      at listOnTimeout (internal/timers.js:549:17)
      at processTimers (internal/timers.js:492:7)

  4) Negative Stop Order TCs -
        Negative Scenario - NoCross
         "before all" hook for " Negative Scenario - NoCross ":
     Error: Timeout of 20000ms exceeded. For async tests and hooks, ensure "done()" is called; if returning a Promise, ensure it resolves. (D:\Testing\Platform\WS2\test\ws-StopOrder-test.js)
      at listOnTimeout (internal/timers.js:549:17)
      at processTimers (internal/timers.js:492:7)