{"name": "ws-testcase", "version": "1.0.0", "description": "run testcase from this folder for jenkins", "main": "login-test.js", "dependencies": {"chai": "^4.3.7", "date-and-time": "^3.0.3", "delay": "^6.0.0", "describe": "^1.2.0", "jsonschema": "^1.4.1", "lodash": "^4.17.21", "mocha-jenkins-reporter": "^0.4.8", "node": "^16.6.1", "ws": "^8.13.0"}, "devDependencies": {"mocha": "^10.2.0", "mochawesome": "^7.1.3", "mochawesome-merge": "^4.4.1", "mochawesome-report-generator": "^6.2.0"}, "scripts": {"test-jenkins": "JUNIT_REPORT_PATH=/report.xml mocha --colors --reporter mocha-j<PERSON><PERSON>-reporter", "test": "npx mocha --timeout 20000 ws-rfsSwap-trade.js"}, "author": "", "license": "ISC"}