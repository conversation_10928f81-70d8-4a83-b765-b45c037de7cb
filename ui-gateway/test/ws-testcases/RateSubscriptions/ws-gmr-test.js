const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const benckmarkData = require('../../config/properties').benckmarkData

let fxbenchmarkSubscriptionResponses
let fxbenchmarkUnsubscriptionResponses
let gridmidrate
let connection

//const login = require('../login').login

// below are needed for schema validation
const Validator = require('jsonschema').Validator;
const v = new Validator();
const benchmarkSchema = require('../../config/top10/benchmark-schema')
let res

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

let gmrTC = function(){
    describe("GMR - ", function(){
    	before(function (done) {
    		wsconnect(done);
    	});

    	after(function () {
    		connection.close()
    	});


    	describe("Subscription test ", function () {
    		before(function (done) {
    			connection.onmessage = (e) => {
    				let res = JSON.parse(e.data)
    				console.log("res =" + JSON.stringify(res))
    				if (res.fxbenchmarkSubscriptionResponses) {
    					fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                        console.log('gmrSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
    					done()
    				}
    			}
     			connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
    		});

    		after(function (done) {
                connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
                done()
           	});

    		it("Benchmark GMR - Subscription test", function(){
        		console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
    			assert.equal(fxbenchmarkSubscriptionResponses[0].status, 'success')
    		});
     	});
/*
  	    describe("Unsubscription test ", function () {
    		it("gmr unsubscription test", function(done){
	   			connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
	   			connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
       			connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
    				console.log("res :" + JSON.stringify(res))
    				if (res.fxbenchmarkUnsubscriptionResponses) {
    					fxbenchmarkUnsubscriptionResponses = res.fxbenchmarkUnsubscriptionResponses
                        console.log('gmrUnsubscriptionResponses : ' + JSON.stringify(fxbenchmarkUnsubscriptionResponses))
                         assert.equal(fxbenchmarkUnsubscriptionResponses[0].status, 'success')
    					done()
    				}
    			}
     		});
 		});
*/
   	    describe("GridMidRate test ", function () {
    		before(function (done) {
    			connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
    			connection.onmessage = (e) => {

    				let res = JSON.parse(e.data)
    				console.log("res =" + JSON.stringify(res))
    				if (res.gridmidrate) {
    					gridmidrate = res.gridmidrate
                        console.log('gmr : ' + JSON.stringify(gridmidrate))
    					done()
    				}

    			}
     		});

    		after(function (done) {
                connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
                done()
           	});

    		it("Benchmark GMR - Rate test", function(){
        		console.log('gmr response: ' + JSON.stringify(gridmidrate))
    			assert.equal(benckmarkData.symbol, gridmidrate.currencyPair)
    			assert.exists(gridmidrate.rate)
    			assert.notEqual('0', gridmidrate.rate)
	   			assert.exists(gridmidrate.guid)
   	    		assert.exists(gridmidrate.timestamp)
    		});
     	});

 	});
}

gmrTC();
