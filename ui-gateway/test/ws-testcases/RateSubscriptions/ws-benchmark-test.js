const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const benckmarkData = require('../../config/properties').benckmarkData

let fxbenchmarkSubscriptionResponses
let fxbenchmarkUnsubscriptionResponses
let fxbenchmark
let benchmarkRequest

//const login = require('../login').login

// below are needed for schema validation
const Validator = require('jsonschema').Validator;
const v = new Validator();
const benchmarkSchema = require('../../config/top10/benchmark-schema')
let res

let wsconnect = function (done, cookies) {
	const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
 	console.log("websocket_url: " + websocket_url)
 	connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey //'Cg2oDBUxwF5typzfBH59W4ttTrY5mMlz' //''
   		}
	})

	connection.onopen = () => {
		done()
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

// benchmark rates are obtained from broker FXB and stream benchmark in Top10
let benchmarkTC = function(){
    describe("Benchmark  ", function(){

	    before(function (done) {
		    wsconnect(done);
	    });

    	after(function () {
    		connection.close()
    	});


    	describe("Subscription test ", function () {
    		before(function (done) {
    			connection.onmessage = (e) => {
    				let res = JSON.parse(e.data)
    			 	console.log("res =" + JSON.stringify(res))
    				if (res.fxbenchmarkSubscriptionResponses) {
    					fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                        console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
    					done()
    				}
    			}
    			console.log("benchmark Subscription test : " + '{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
     			connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
    		});

    		after(function () {
                connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
           	});

    		it("Benchmark FXB - subscription test", function(){
        		console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
    			assert.equal(fxbenchmarkSubscriptionResponses[0].status, 'success')
    		});
     	});

  	    describe("Unsubscription test ", function () {
    		it("Benchmark FXB - unsubscription test", function(done){
    		    console.log("benchmark unsubscription test : " + '{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
    		    console.log("benchmark unsubscription test : " + '{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
	   			connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
	   			connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
       			connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
    				console.log("res :" + JSON.stringify(res))
    				if (res.fxbenchmarkUnsubscriptionResponses) {
    					fxbenchmarkUnsubscriptionResponses = res.fxbenchmarkUnsubscriptionResponses
                        console.log('fxbenchmarkUnsubscriptionResponses : ' + JSON.stringify(fxbenchmarkUnsubscriptionResponses))
                         assert.equal(fxbenchmarkUnsubscriptionResponses[0].status, 'success')
    					done()
    				}
    			}
     		});


 		});

   	    describe("Rate test ", function () {
    		before(function (done) {
                console.log("Benchmark rate test : " + '{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
    			connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
                rateflag = true
//                unsubscriptionFlag = true
    			connection.onmessage = (e) => {
    				let res = JSON.parse(e.data)
    				console.log("res =" + JSON.stringify(res))
    				if (res.fxbenchmark) {
    					fxbenchmark = res.fxbenchmark
                        console.log('fxbenchmark : ' + JSON.stringify(fxbenchmark))
                        console.log("======flag="+rateflag)
       					if(rateflag) {
    					    rateflag = false
    					    done()
  					}
    				}

    			}
     		});

    		after(function () {
                connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
           	});


    		it("Benchmark FXB - Rate test", function(){
        		console.log('fxbenchmark response: ' + JSON.stringify(fxbenchmark))
    			assert.equal(benckmarkData.symbol, fxbenchmark.currencyPair)
    			assert.exists(fxbenchmark.rate)
    			assert.exists(fxbenchmark.timestamp)
    			assert.exists(fxbenchmark.guid)
    		});
     	});

 	});
};

let NegativeTC = function(){
    describe("Benchmark negative scenario -", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("====after login===")
    	});


    	describe("invalid JSON test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.errors) {
            			    errors = res.errors
                            console.log('errors : ' + JSON.stringify(errors))
            				done()
            		}
            	}
            	    console.log('request ---> {"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.invalidSymbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol1":' +  benckmarkData.supportedSymbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            });

         	it("Benchmark FXB - InvalidJSON CP test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
                assert.equal('1', JSON.parse(JSON.stringify(res.errors[0].errorCode)))
                assert.equal('Not a valid request.', JSON.parse(JSON.stringify(res.errors[0].errorMessage)))
            });
        });

//Invalid URL test can not be automated since WS connection itself doesnt happen and we will not get any error message
    	describe("InvalidCP subscription test", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
            	    console.log('request ---> {"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.invalidSymbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.invalidSymbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            });

         	it("Benchmark FXB - Invalid CP test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('204', JSON.parse(JSON.stringify(fxbenchmarkSubscriptionResponses[0].errors[0])).errorCode)
                assert.equal('Currency pair not supported.', JSON.parse(JSON.stringify(fxbenchmarkSubscriptionResponses[0].errors[0])).errorMessage)

            });

        });

    	describe("UnsupportedCP subscription test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
            	    console.log('request --->{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.unsupportedSymbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.unsupportedSymbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            });

         	it("Benchmark FXB - Unspported CP test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('204', JSON.parse(JSON.stringify(fxbenchmarkSubscriptionResponses[0].errors[0])).errorCode)
                assert.equal('Currency pair not supported.', JSON.parse(JSON.stringify(fxbenchmarkSubscriptionResponses[0].errors[0])).errorMessage)

            });
        });

    	describe("Unsupported PriceSource test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
            	    console.log('request ---> {"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.supportedSymbol + '","priceSource":"' + benckmarkData.unsupportedPriceSource + '"}]}')
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.supportedSymbol + '","priceSource":"' + benckmarkData.unsupportedPriceSource + '"}]}')
            });

         	it("Benchmark FXB - Unuspported priceSource test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('202', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('Valid Price Source is required.', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

    	describe("Null value for symbol ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
            	    console.log('request ---> {"fxbenchmarkSubscriptions":[{"symbol":"' + "" + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + "" + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            });

         	it("Benchmark FXB - Null value for symbol test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('201', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('Currency Pair is required.', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

   	    describe("Null value for priceSource ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
            	    console.log('request ---> {"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.supportedSymbol + '","priceSource":"' + "" + '"}]}')
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.supportedSymbol + '","priceSource":"' + "" + '"}]}')
            });

         	it("Benchmark FXB - Null value for priceSource test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('202', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('Valid Price Source is required.', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

   	    describe("Missing mandatory tag PriceSource", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
            	    console.log('request ---> {"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.supportedSymbol + '"}]}')
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.supportedSymbol +  '"}]}')
            });

         	it("Benchmark FXB - Missing mandatory tag PriceSource test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('202', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('Valid Price Source is required.', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

    	describe("Missing mandatory tag symbol ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
            	    console.log('request ---> {"fxbenchmarkSubscriptions":[{"priceSource":"' + benckmarkData.priceSource + '"}]}')
             		connection.send('{"fxbenchmarkSubscriptions":[{"priceSource":"' + benckmarkData.priceSource + '"}]}')
            });

         	it("Benchmark FXB - Missing mandatory tag symbol test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('201', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('Currency Pair is required.', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

// Keep duplicate subscription to end, it doesnt work if moved to middle. Dont know the reason.
   	    describe("Duplicate subscription test ", function () {
    	    before(function (done) {
    		    let tmp=false
     		    connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
     		    connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
    		    connection.onmessage = (e) => {
    			    let res = JSON.parse(e.data)
    			    console.log("res =" + JSON.stringify(res))
    			    if (res.fxbenchmarkSubscriptionResponses) {
           		        if (res.fxbenchmarkSubscriptionResponses[0].errors) {
        				    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
                            tmp=true
               		    }
    			    }
           		    if (tmp) {
           	    	    console.log("in tmp" + tmp)
               		    done();
           		    }
    		    }
                connection.close();
 		    });


    		after(function () {
    		    console.log('request --->{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
                connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
           	});

     	    it("Benchmark FXB - Duplicate subscription test", function(){
                console.log('duplicateSubscriptionResponse: ' + JSON.stringify(fxbenchmarkSubscriptionResponses));
           	    assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('205', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('Currency pair already subscribed.', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

    });

}

benchmarkTC();
NegativeTC();
