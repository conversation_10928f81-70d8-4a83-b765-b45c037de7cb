const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const benckmarkData = require('../../config/properties').benckmarkData

let fxchiefdealerSubscriptionResponses
let fxchiefdealerUnsubscriptionResponses
let fxchiefdealer
let chiefdealerRequest

// below are needed for schema validation
const Validator = require('jsonschema').Validator;
const v = new Validator();
//const chiefdealerSchema = require('./../config/top10/chiefdealer-schema')
let res

let wsconnect = function (done, cookies) {
	//const websocket_url = 'ws://' + env.kongHost + ':' + env.kongPort + '/fxstream'
	const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
 	console.log("websocket_url: " + websocket_url)
 	connection = new WebSocket(websocket_url, [], {
 		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey //''
   		}
   	})

	connection.onopen = () => {
		done()
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let chiefdealerTC = function(){
    describe("chiefdealer - ", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("====after login===")
    	});

    	after(function () {
    		connection.close()
    	});

  	    describe("Unsubscription test ", function () {
    		it("chiefdealer unsubscription test", function(done){
     			connection.send('{"chiefDealerNotification":"subscribe"}')
                connection.send('{"chiefDealerNotification":"unsubscribe"}')
       			connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
    				console.log("res 1:" + JSON.stringify(res))
                    if (res.chiefDealerNotificationStatus == "unsubscribed") {
                        console.log('cd : ' + res.chiefDealerNotificationStatus)
                        assert.equal(res.chiefDealerNotificationStatus, 'unsubscribed')
    					done()
    				}
     			}
     		});
 		});

    	describe("Subscription test ", function () {
    		before(function (done) {
    			connection.onmessage = (e) => {
    				let res = JSON.parse(e.data)
    				console.log("res =" + JSON.stringify(res))
    				if (res.chiefDealerNotificationStatus == "subscribed") {
    					fxchiefdealerSubscriptionResponses = res
                        console.log('fxchiefdealerSubscriptionResponses : ' + JSON.stringify(fxchiefdealerSubscriptionResponses))
                        connection.send('{"chiefDealerNotification":"unsubscribe"}')
    					done()
    				}
    			}
     			connection.send('{"chiefDealerNotification":"subscribe"}')
    		});

/*
    		after(function () {
                connection.send('{"chiefDealerNotification":"unsubscribe"}')
           	});
*/


    		it("subscription test", function(){
        		console.log('subscription response: ' + JSON.stringify(fxchiefdealerSubscriptionResponses))
    			assert.equal(fxchiefdealerSubscriptionResponses.chiefDealerNotificationStatus, 'subscribed')
    		});
     	});


 	});
};


let NegativeTC = function(){
    describe("chiefdealer negative scenario -", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("====after login===")
    	});

    	describe("invalid JSON test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.errors) {
            			    errors = res.errors
                            console.log('errors : ' + JSON.stringify(errors))
            				done()
            		}
            	}
             		connection.send('{"chiefDealerNotification":{"subscribe"}}')
            });

         	it("chiefdealer subscription invalidJSON CP test", function(){
                console.log('subscription response: ' + JSON.stringify(fxchiefdealerSubscriptionResponses))
                assert.equal('1', JSON.parse(JSON.stringify(res.errors[0].errorCode)))
                assert.equal('Not a valid request.', JSON.parse(JSON.stringify(res.errors[0].errorMessage)))
            });
        });

    });
}

NegativeTC();
chiefdealerTC();
