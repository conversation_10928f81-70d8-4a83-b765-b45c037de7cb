const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const benckmarkData = require('../../config/properties').benckmarkData
const orderData = require('../../config/properties').orderData

let tickerSubscriptionReq = '{"tradeTickerSubscriptions":[{"symbol":"' + orderData.symbol_EURUSD + '"}]}'
let tickerUnsubscriptionReq = '{"tradeTickerUnsubscriptions":[{"symbol":"' + orderData.symbol_EURUSD + '"}]}'
let tradeTickerSubscriptionResponses
let tradetickerUnsubscriptionResponses
let tradeticker
let res

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }


let tradetickerTC = function(){
    describe("tradeticker - ", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("====after login===")
    	});

    	after(function () {
    		connection.close()
    	});


    	describe("Subscription test ", function () {
    		before(function (done) {
    		    console.log("ticker subscription request = " + tickerSubscriptionReq)
     			connection.send(tickerSubscriptionReq)
    			connection.onmessage = (e) => {
    				let res = JSON.parse(e.data)
    				console.log("res =" + JSON.stringify(res))
    				if (res.tradeTickerSubscriptionResponses) {
    					tradeTickerSubscriptionResponses = res.tradeTickerSubscriptionResponses
                        console.log('tradeTickerSubscriptionResponses : ' + JSON.stringify(tradeTickerSubscriptionResponses))
    					done()
    				}
    			}
    		});

    		after(function () {
    		    console.log("ticker unsubscription request = " + tickerUnsubscriptionReq)
                connection.send(tickerUnsubscriptionReq)
           	});

    		it("subscription test", function(){
        		console.log('subscription response: ' + JSON.stringify(tradeTickerSubscriptionResponses))
    			assert.equal(tradeTickerSubscriptionResponses[0].status, 'success')
    		});
     	});

  	    describe("Unsubscription test ", function () {
    		it("tradeticker unsubscription test", function(done){
   				connection.send(tickerSubscriptionReq)
	   			connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
    				console.log("res :" + JSON.stringify(res))
    				if (res.tradeTickerUnsubscriptionResponses) {
    					tradeTickerUnsubscriptionResponses = res.tradeTickerUnsubscriptionResponses
                        console.log('tradeTickerUnsubscriptionResponses : ' + JSON.stringify(tradeTickerUnsubscriptionResponses))
                        assert.equal(tradeTickerUnsubscriptionResponses[0].status, 'success')
    					done()
    				}
                    connection.send(tickerUnsubscriptionReq)
    			}
     		});
 		});


// Trade for the logged in org with the subscribed ccyPair should be done after subscription to get the tradeticker details, otherwise tcs (below 2) will fail
// Incase of RestAPI, it gets the last trade details whereas WS needs trade after subscription.
// Integrate tradeing api once it is ready
// Also, added bid and offer trade to validate isBid field, so Sell and Buy trades should be done
// Also, trade rate is not validated since we cant fix the rate in Top10

   	    describe("TradeTicker Offer test ", function () {
    		before(function (done) {
    		    coId = parseInt(Math.floor(Math.random() * 1000))
    			connection.send(tickerSubscriptionReq)
    			//Placing order
    			connection.send('{"orders":[{"coId":"'+coId+'","currency":"'+orderData.baseCurrency_EURUSD+'", "size":"1000", "type":"Market", "side":"Sell", "symbol":"'+orderData.symbol_EURUSD+'", "execFlags" : [], "timeInForce":"IOC"}]}')
    			connection.onmessage = (e) => {
    				let res = JSON.parse(e.data)
    				console.log("res 3=" + JSON.stringify(res))
    				if (res.tradeticker) {
    					tradeticker = res.tradeticker
                        console.log('tradeticker : ' + JSON.stringify(tradeticker))
    					done()
    				}

    			}
     		});

    		after(function () {
                connection.send(tickerUnsubscriptionReq)
           	});


    		it("Tradeticker - Offer test", function(){
        		console.log('tradeticker response: ' + JSON.stringify(tradeticker))
    			assert.equal(orderData.symbol_EURUSD, tradeticker.symbol)
    			assert.exists(tradeticker.rate)
    			assert.notEqual('0', tradeticker.rate)
    			assert.exists(tradeticker.time)
    			assert.exists(tradeticker.isBid)
    			assert.equal(false, tradeticker.isBid)
    		});
     	});

   	    describe("TradeTicker Bid test ", function () {
    		before(function (done) {
    			connection.send(tickerSubscriptionReq)
       			//Placing order
    		    coId = parseInt(Math.floor(Math.random() * 1000))
    			connection.send('{"orders":[{"coId":"'+coId+'","currency":"'+orderData.baseCurrency_EURUSD+'", "size":"1000", "type":"Market", "side":"Buy", "symbol":"'+orderData.symbol_EURUSD+'", "execFlags" : [], "timeInForce":"IOC"}]}')
    			connection.onmessage = (e) => {
    				let res = JSON.parse(e.data)
    				console.log("res 3=" + JSON.stringify(res))
    				if (res.tradeticker) {
    					tradeticker = res.tradeticker
                        console.log('tradeticker : ' + JSON.stringify(tradeticker))
    					done()
    				}
    			}
     		});

    		after(function () {
                connection.send(tickerUnsubscriptionReq)
           	});


    		it("Tradeticker - Bid test", function(){
        		console.log('tradeticker response: ' + JSON.stringify(tradeticker))
    			assert.equal(orderData.symbol_EURUSD, tradeticker.symbol)
    			assert.exists(tradeticker.rate)
    			assert.notEqual('0', tradeticker.rate)
    			assert.exists(tradeticker.time)
    			assert.exists(tradeticker.isBid)
    			assert.equal(true, tradeticker.isBid)

    		});
     	});
 	});
};


let NegativeTC = function(){
    describe("tradeticker negative scenario -", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("====after login===")
    	});

    	after(function () {
    		connection.close()
    	});


    // Below invalid json script should fail when extra tag value is given, but it is passing, Eng has to fix it.
    	describe("Extra tag test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.errors) {
            			    errors = res.errors
                            console.log('errors : ' + JSON.stringify(errors))
            				done()
            		}
            	}
            	console.log('{"tradeTickerSubscriptions":[{"symbol":"' + orderData.symbol_EURUSD + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            	connection.send('{"tradeTickerSubscriptions":[{"symbol" : "' + orderData.symbol_EURUSD + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            });

         	it("Tradeticker - Extra tag test ", function(){
                console.log('subscription response: ' + JSON.stringify(errors))
                assert.equal('1', JSON.parse(JSON.stringify(res.errors[0].errorCode)))
                assert.equal('Not a valid request.', JSON.parse(JSON.stringify(res.errors[0].errorMessage)))
            });
        });

    	describe("Invalid JSON test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.errors) {
            			    errors = res.errors
                            console.log('errors : ' + JSON.stringify(errors))
            				done()
            		}
            	}
            	console.log('{"tradeTickerSubscriptions":[{"symbol":"' + orderData.symbol_EURUSD + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            	connection.send('{"tradeTickerSubscriptions":[{"symbol" "' + orderData.symbol_EURUSD + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            });

         	it("Tradeticker - Invalid JSON test ", function(){
                console.log('subscription response: ' + JSON.stringify(errors))
                assert.equal('1', JSON.parse(JSON.stringify(res.errors[0].errorCode)))
                assert.equal('Not a valid request.', JSON.parse(JSON.stringify(res.errors[0].errorMessage)))
            });
        });

    	describe("InvalidCP test", function () {
    	    before(function (done) {
           		connection.send('{"tradeTickerSubscriptions":[{"symbol":"' + benckmarkData.invalidSymbol + '"}]}')
           		console.log("tradeTickerSubscriptions request =" + '{"tradetickerSubscriptions":[{"symbol":"' + benckmarkData.invalidSymbol + '"}]}')
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
             		 if (res.tradeTickerSubscriptionResponses) {
            			    tradeTickerSubscriptionResponses = res.tradeTickerSubscriptionResponses
                            console.log('tradeTickerSubscriptionResponses : ' + JSON.stringify(tradeTickerSubscriptionResponses))
            				done()
            		}
            	}
            });

         	it("Tradeticker - Invalid CP test", function(){
                console.log('subscription response: ' + JSON.stringify(tradeTickerSubscriptionResponses))
            	assert.equal('failed', tradeTickerSubscriptionResponses[0].status)
                assert.equal('302', JSON.parse(JSON.stringify(tradeTickerSubscriptionResponses[0].errors[0])).errorCode)
                assert.equal('currency pair is invalid.', JSON.parse(JSON.stringify(tradeTickerSubscriptionResponses[0].errors[0])).errorMessage)

            });

        });

// This fails, need to give correct unsupported ccypair
    	describe("UnsupportedCP subscription test ", function () {
    	    before(function (done) {
           		connection.send('{"tradeTickerSubscriptions":[{"symbol":"' + benckmarkData.unsupportedSymbol + '"}]}')
           		console.log("tradeTickerSubscriptions request =" + '{"tradetickerSubscriptions":[{"symbol":"' + benckmarkData.unsupportedSymbol + '"}]}')

                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.tradeTickerSubscriptionResponses) {
            			    tradeTickerSubscriptionResponses = res.tradeTickerSubscriptionResponses
                            console.log('tradeTickerSubscriptionResponses : ' + JSON.stringify(tradeTickerSubscriptionResponses))
            				done()
            		}
            	}
            });

         	it("Tradeticker - Unspported CP test", function(){
                console.log('subscription response: ' + JSON.stringify(tradeTickerSubscriptionResponses))
            	assert.equal('success', tradeTickerSubscriptionResponses[0].status)
//                assert.equal('204', JSON.parse(JSON.stringify(tradeTickerSubscriptionResponses[0].errors[0])).errorCode)
//                assert.equal('currency pair is invalid.', JSON.parse(JSON.stringify(tradeTickerSubscriptionResponses[0].errors[0])).errorMessage)

            });
        });

   	    describe("Duplicate subscription test ", function () {
    	    before(function (done) {
     		    connection.send('{"tradeTickerSubscriptions":[{"symbol":"' + orderData.symbol_EURUSD + '"}]}')
     		    connection.send('{"tradeTickerSubscriptions":[{"symbol":"' + orderData.symbol_EURUSD +  '"}]}')
          		console.log("tradeTickerSubscriptions request =" + '{"tradetickerSubscriptions":[{"symbol":"' + orderData.symbol_EURUSD + '"}]}')
     		    connection.onmessage = (e) => {
    			    let res = JSON.parse(e.data)
    			    console.log("res =" + JSON.stringify(res))
    			    if (res.tradeTickerSubscriptionResponses) {
           		        if (res.tradeTickerSubscriptionResponses[0].errors) {
        				    tradeTickerSubscriptionResponses = res.tradeTickerSubscriptionResponses
                            console.log('tradeTickerSubscriptionResponses : ' + JSON.stringify(tradeTickerSubscriptionResponses))
                            done()
               		    }
    			    }
    		    }
                connection.close();
 		    });

    		after(function () {
                connection.send('{"tradeTickerUnsubscriptions":[{"symbol":"' + orderData.symbol_EURUSD + '"}]}')
           	});

     	    it("Tradeticker - duplicate subscription test", function(){
                console.log('duplicateSubscriptionResponse: ' + JSON.stringify(tradeTickerSubscriptionResponses));
           	    assert.equal('failed', tradeTickerSubscriptionResponses[0].status)
                assert.equal('304', JSON.parse(JSON.stringify((tradeTickerSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('currency pair already subscribed.', JSON.parse(JSON.stringify((tradeTickerSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

    });
}

NegativeTC();
tradetickerTC();
