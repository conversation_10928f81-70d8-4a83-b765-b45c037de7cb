const assert = require('chai').assert
const WebSocket = require('ws')

//const login = require('../login').login
const env = require('../../config/properties').env
const mdData = require('../../config/properties').mdData

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId


// Login credentials should be for MDF enabled org
// For marketdata scripts, org should be getting rates in MDF
// Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

let wsconnect = function (done, cookies) {
	const websocket_url = 'wss://' + env.hostname + ':' + env.port + '/v2/fxstream'

	connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
	})

	connection.onopen = () => {
		done()
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let marketDataTC = function(){

    describe("MarketData ", function () {

	    before(function (done) {
		    wsconnect(done);
	    });

	    after(function () {
		    connection.close();
	    });


// Keep unsubscription test before subscription test, otherwise reqIds will get messed up
        describe("Unsubscription test ", function () {
   		    before(function (done) {
   		        reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: Unsubscription - reqId : " + reqId)
                console.log("MarketData: Unsubscription - raeSubscription : " +'{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeFull + '","requestId":' + parseInt(reqId)+'}]}')
                connection.send('{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeFull + '","requestId":' + parseInt(reqId)+'}]}')
                connection.send('{"rateUnsubscriptions":[{"requestId":' + parseInt(reqId) + '}]}')
  			    connection.onmessage = (e) => {
    			    let res = JSON.parse(e.data)
        			console.log("MarketData: Unsubscription - res : " + JSON.stringify(res))
        			if (res.rateUnsubscriptionResponses) {
    	    			rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
    		    	    console.log("MarketData: Unsubscription - rateUnsubscriptionResponses : " + JSON.stringify(rateUnsubscriptionResponses))
    			        done()
    			    }
    		    }
    	    });

         //{"requestId":"83","customAggregation":false,"depth":0}
         //{"rateUnsubscriptionResponses":[{"request":{"requestId":19},"status":"success"}]}
    	    it("MarketData Generic - Unsubscription test", function () {
       		    console.log("MarketData: Unsubscription - reqId : " + '{"reqId":' + parseInt(reqId) + '}')
        		assert.equal(rateUnsubscriptionResponses[0].status, 'success')
        		assert.equal(JSON.stringify(rateUnsubscriptionResponses[0].request), '{"requestId":"' + parseInt(reqId) + '","customAggregation":false,"depth":0}')
    	    });
	    });

    	describe("Subscription test ", function () {

		    before(function (done) {
    			reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: subscription - reqId : " + reqId)
		    	connection.send('{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeFull + '","requestId":' + parseInt(reqId)+'}]}')

			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("MarketData: subscription - res : " + JSON.stringify(res))
	    			if (res.rateSubscriptionResponses) {
		    			rateSubscriptionResponses = res.rateSubscriptionResponses
			    		console.log("MarketData: subscription test- rateSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponses))
			    		connection.send('{"rateUnsubscriptions":[{"requestId":' + parseInt(reqId) + '}]}')
				    	//done()
				    } else if (res.rateUnsubscriptionResponses) {
    	    			rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
    		    	    console.log("MarketData: Subscription test - rateUnsubscriptionResponses : " + JSON.stringify(rateUnsubscriptionResponses))
    			        done()
				    }
			    }
		    });

    		it("MarketData Generic - Subscription test", function () {
	    	    console.log("MarketData: subscription - MarketDataSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponses))
		    	assert.equal(rateSubscriptionResponses[0].status, 'success')
			    assert.equal(rateSubscriptionResponses[0].request.symbol, mdData.supportedSymbol)
                assert.equal(rateSubscriptionResponses[0].request.type, mdData.typeFull)
                assert.equal(rateSubscriptionResponses[0].request.requestId, reqId)
	    	});
	    });


	    describe("Rate test - FULL ", function () {
            let FullRate
		    before(function (done) {
                fullReqId = parseInt(Math.floor(Math.random() * 100))
                //fullReqId = "FULL" + reqId
                console.log("MarketData: rate - reqId = " + reqId)
                subscriptionRequest = '{"rateSubscriptions":[{"symbol":"' +  mdData.supportedSymbol + '","type":"' + mdData.typeFull + '","requestId":' + fullReqId+'}]}'
                console.log("Rate test - subscription request : "+ subscriptionRequest)
		    	connection.send(subscriptionRequest)
	    		flag = false
	    		unsubscriptionFlag = true
    			connection.onmessage = (e) => {
	    			let res = JSON.parse(e.data)
		    		console.log("MarketData: rate - res : " + JSON.stringify(res))
			    	if (res.rate ) {

                        if(res.rate.requestId ==  fullReqId) {
                        flag = true
				    	FullRate = res.rate
					    console.log("MarketData: rate - rate response : " + JSON.stringify(FullRate))
                            if(flag && unsubscriptionFlag) {
                                unsubscriptionFlag = false
                                connection.send('{"rateUnsubscriptions":[{"requestId":' + fullReqId + '}]}')
                            }
 	    		    	}
	    		    } else if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
                        console.log("MarketData: Subscription test - rateUnsubscriptionResponses : " + JSON.stringify(rateUnsubscriptionResponses))
                        if(rateUnsubscriptionResponses[0].status == "success")
                        {
                            done()
                        }
                    }
    		    }
		    });

        //MarketData: rate - rate response : {"symbol":"USD/JPY","bid":[110.45],"offer":[110.82],"bidLimit":[1000000],"offerLimit":[1000000],"time":1589806173714,"requestId":92}
        	it("MarketData Generic - Rate FULL ", function () {

	    	    console.log("MarketData: rate - rate response : " + JSON.stringify(FullRate))
		    	assert.equal(FullRate.symbol, mdData.supportedSymbol)
			    assert.exists(FullRate.bid)
    			assert.exists(FullRate.offer)
	    		assert.exists(FullRate.bidLimit)
		    	assert.exists(FullRate.offerLimit)
		    	assert.exists(FullRate.time)
		    	assert.notEqual(FullRate.bid,0)
		    	//assert.equal(rate.bid,"[110.45]")
		    	assert.notEqual(FullRate.offer,0)
		    	assert.notEqual(FullRate.bidLimit,0)
		    	assert.notEqual(FullRate.offerLimit,0)
                assert.equal(FullRate.requestId, fullReqId)

		    });
	    });

	    describe("Rate test - RAW ", function () {
            let RawRate
		    before(function (done) {
                fullReqId = parseInt(Math.floor(Math.random() * 100))
                console.log("MarketData: rate - reqId = " + reqId)
                subscriptionRequest = '{"rateSubscriptions":[{"symbol":"' +  mdData.supportedSymbol + '","type":"' + mdData.typeRaw + '","requestId":' + fullReqId+'}]}'
                console.log("Rate test - subscription request : "+ subscriptionRequest)
		    	connection.send(subscriptionRequest)
	    		flag = false
	    		unsubscriptionFlag = true
    			connection.onmessage = (e) => {
	    			let res = JSON.parse(e.data)
		    		console.log("MarketData: rate - res : " + JSON.stringify(res))
			    	if (res.rate ) {
                        if(res.rate.requestId ==  fullReqId) {
                        flag = true
				    	RawRate = res.rate
					    console.log("MarketData: rate - rate response : " + JSON.stringify(RawRate))
                            if(flag && unsubscriptionFlag) {
                                unsubscriptionFlag = false
                                connection.send('{"rateUnsubscriptions":[{"requestId":' + fullReqId + '}]}')
                            }
 	    		    	}
	    		    } else if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
                        console.log("MarketData: Subscription test - rateUnsubscriptionResponses : " + JSON.stringify(rateUnsubscriptionResponses))
                        if(rateUnsubscriptionResponses[0].status == "success")
                        {
                            done()
                        }
                    }
    		    }
		    });

        //MarketData: rate - rate response : {"symbol":"USD/JPY","bid":[110.45],"offer":[110.82],"bidLimit":[1000000],"offerLimit":[1000000],"time":1589806173714,"requestId":92}
        	it("MarketData Generic - Rate RAW ", function () {
	    	    console.log("MarketData: rate - rate response : " + JSON.stringify(RawRate))
		    	assert.equal(RawRate.symbol, mdData.supportedSymbol)
			    assert.exists(RawRate.bid)
    			assert.exists(RawRate.offer)
	    		assert.exists(RawRate.bidLimit)
		    	assert.exists(RawRate.offerLimit)
		    	assert.exists(RawRate.time)
		    	assert.notEqual(RawRate.bid,0)
		    	//assert.equal(rate.bid,"[110.45]")
		    	assert.notEqual(RawRate.offer,0)
		    	assert.notEqual(RawRate.bidLimit,0)
		    	assert.notEqual(RawRate.offerLimit,0)
                assert.equal(RawRate.requestId, fullReqId)

		    });
	    });

	    describe("Rate test - TOB ", function () {
            let TobRate
		    before(function (done) {
                fullReqId = parseInt(Math.floor(Math.random() * 100))
                console.log("MarketData: rate - reqId = " + reqId)
                subscriptionRequest = '{"rateSubscriptions":[{"symbol":"' +  mdData.supportedSymbol + '","type":"' + mdData.typeTob + '","requestId":' + fullReqId+'}]}'
                console.log("Rate test - subscription request : "+ subscriptionRequest)
		    	connection.send(subscriptionRequest)
	    		flag = false
	    		unsubscriptionFlag = true
    			connection.onmessage = (e) => {
	    			let res = JSON.parse(e.data)
		    		console.log("MarketData: rate - res : " + JSON.stringify(res))
			    	if (res.rate ) {

                        if(res.rate.requestId ==  fullReqId) {
                        flag = true
				    	TobRate = res.rate
					    console.log("MarketData: rate - rate response : " + JSON.stringify(TobRate))
                            if(flag && unsubscriptionFlag) {
                                unsubscriptionFlag = false
                                connection.send('{"rateUnsubscriptions":[{"requestId":' + fullReqId + '}]}')
                            }
 	    		    	}
	    		    } else if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
                        console.log("MarketData: Subscription test - rateUnsubscriptionResponses : " + JSON.stringify(rateUnsubscriptionResponses))
                        if(rateUnsubscriptionResponses[0].status == "success")
                        {
                            done()
                        }
                    }
    		    }
		    });

        //MarketData: rate - rate response : {"symbol":"USD/JPY","bid":[110.45],"offer":[110.82],"bidLimit":[1000000],"offerLimit":[1000000],"time":1589806173714,"requestId":92}
        	it("MarketData Generic - Rate TOB ", function () {

	    	    console.log("MarketData: rate - rate response : " + JSON.stringify(TobRate))
		    	assert.equal(TobRate.symbol, mdData.supportedSymbol)
			    assert.exists(TobRate.bid)
    			assert.exists(TobRate.offer)
	    		assert.exists(TobRate.bidLimit)
		    	assert.exists(TobRate.offerLimit)
		    	assert.exists(TobRate.time)
		    	assert.notEqual(TobRate.bid,0)
		    	//assert.equal(rate.bid,"[110.45]")
		    	assert.notEqual(TobRate.offer,0)
		    	assert.notEqual(TobRate.bidLimit,0)
		    	assert.notEqual(TobRate.offerLimit,0)
                assert.equal(TobRate.requestId, fullReqId)

		    });
	    });

    });
};

let NegativeTC = function(){
    describe("MarketData negative scenario - ", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("MarketData Login")
    	});

	    after(function () {
		    connection.close()
	    });

    	describe("InvalidJSON test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    res = JSON.parse(e.data)
            		console.log("MarketData: InvalidJSON - res : " + JSON.stringify(res))
            		 if (res.errors) {
            			    errors = res.errors
                            console.log('MarketData: InvalidJSON - errors : ' + JSON.stringify(errors))
            				done()
            		}
            	}
           		connection.send('{"rateSubscriptions":[{"symbol1":"' + mdData.supportedSymbol + '","type":"' + mdData.typeFull + '","requestId":' + parseInt(reqId)+'}]}')
            });

         	it("MarketData Generic - InvalidJSON test", function(){
                assert.equal('1', JSON.parse(JSON.stringify(res.errors[0].errorCode)))
                assert.equal('Not a valid request.', JSON.parse(JSON.stringify(res.errors[0].errorMessage)))
             });
        });

//Invalid URL test can not be automated since WS connection itself doesnt happen and we will not get any error message
    	describe("InvalidCP test", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("MarketData: InvalidCP - res : " + JSON.stringify(res))
            		 if (res.rateSubscriptionResponses) {
            			      rateSubscriptionResponses = res.rateSubscriptionResponses
                              console.log('MarketData: InvalidCP - rateSubscriptionResponses : ' + JSON.stringify(rateSubscriptionResponses))
                              done()
            		}
            	}
            	reqId = Math.floor(Math.random() * 100)
             	connection.send('{"rateSubscriptions":[{"symbol":"' + mdData.invalidSymbol + '","type":"' + mdData.typeFull + '","requestId":' + parseInt(reqId)+'}]}')
            });

         	it("MarketData Generic - invalidCP test", function(){
                console.log('MarketData: InvalidCP - subscription response : ' + JSON.stringify(rateSubscriptionResponses))
                assert.equal('106', JSON.parse(JSON.stringify(rateSubscriptionResponses[0].errors[0])).errorCode)
                assert.equal('currency pair not supported.', JSON.parse(JSON.stringify(rateSubscriptionResponses[0].errors[0])).errorMessage)
            });

        });

	    describe("InvalidType test", function () {
		    before(function (done) {
    			connection.onmessage = (e) => {
	    			res = JSON.parse(e.data)
		    		console.log("MarketData: InvalidType - res : " + JSON.stringify(res))
			    	if (res.errors) {
				    	errors = res.errors
					    console.log("MarketData: InvalidType - res : " + JSON.stringify(errors))
					    done()
			    	}
			    }
    			reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: InvalidType - reqId : " + reqId)
		    	connection.send('{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"XYZ"' + ',"requestId":' + parseInt(reqId)+'}]}')
		    });

         	it("MarketData Generic - invalidDataType test", function(){
         	    assert.equal('1', res.errors[0].errorCode)
                assert.equal('Not a valid request.', res.errors[0].errorMessage)
             });
	    });


	    describe("Invalid reqId test", function () {
		    before(function (done) {
    			connection.onmessage = (e) => {
	    			res = JSON.parse(e.data)
		    		console.log("MarketData: InvalidReqId - res : " + JSON.stringify(res))
			    	if (res.errors) {
				    	errors = res.errors
					    console.log("MarketData: InvalidReqId - errors : " + JSON.stringify(errors))
					    done()
			    	}
			    }
    			reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: InvalidReqId - reqId : " + reqId)
		    	connection.send('{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeFull + '","requestId":' + "ABC" +'}]}')
		    });

        	it("MarketData Generic - Invalid reqId test", function(done){
                assert.equal('1', res.errors[0].errorCode)
                assert.equal('Not a valid request.', res.errors[0].errorMessage)
                done()
            });
	    });

    });
}

marketDataTC();
NegativeTC();