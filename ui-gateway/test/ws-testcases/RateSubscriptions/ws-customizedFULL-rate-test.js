const assert = require('chai').assert
const WebSocket = require('ws')

const env = require('../../config/properties').env
const mdData = require('../../config/properties').mdData
const userData = require('../../config/properties').user

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey   //'Cg2oDBUxwF5typzfBH59W4ttTrY5mMlz'
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

let marketDataTC = function(){

    describe("MarketData ", function () {

	    before(function (done) {
		    wsconnect(done);
	    });

	    after(function () {
		    connection.close()
	    });


// Keep unsubscription test before subscription test, otherwise reqIds will get messed up
        describe("TC1 - FULL - unsubscription - customAggregation : true ", function () {
   		    before(function (done) {
   		        reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: Unsubscription - reqId : " + reqId)
                var subrequests = [{ 
                     symbol : mdData.supportedSymbol,
                     type   : mdData.typeFull,
                     requestId : parseInt(reqId),
                     org : userData.orgname,
                     customAggregation : true
                }]
                var wsreq = { rateSubscriptions : subrequests }
                connection.send(JSON.stringify(wsreq));
                console.log("MarketData: Sending Unsubscription request - reqId = " + JSON.stringify(wsreq))
                //connection.send('{"rateUnsubscriptions":[{"requestId":' + parseInt(reqId) + '}]}')
		        unsubscriptionRequest = '{"rateUnsubscriptions":[{"requestId" : "' + reqId + '", "customAggregation": "true", "type": "FULL"}]}'
	            connection.send(unsubscriptionRequest)

                connection.onmessage = (e) => {
                    let res = JSON.parse(e.data)
                    console.log("MarketData: Unsubscription - res : " + JSON.stringify(res))
                    if (res.rateUnsubscriptionResponses) {
                        if (res.rateUnsubscriptionResponses) {
                            rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
                            console.log("MarketData: Unsubscription - rateUnsubscriptionResponses : " + JSON.stringify(rateUnsubscriptionResponses))
                            done()
                        }
                    }
    		    }
    	    });

            //{"rateUnsubscriptionResponses":[{"request":{"requestId":19},"status":"success"}]}
            //MarketData: Unsubscription - rateUnsubscriptionResponses : [{"request":{"type":"FULL","requestId":"56","customAggregation":true,"depth":0},"status":"success"}]
            it("TC1 - FULL - unsubscription - customAggregation : true", function () {
                console.log("MarketData: Unsubscription - reqId : " + '{"reqId":' + parseInt(reqId) + '}')
                assert.equal(rateUnsubscriptionResponses[0].status, 'success')
                console.log("unsubscription request details - " +  rateUnsubscriptionResponses[0].request)
                console.log("unsubscription request details - " +  rateUnsubscriptionResponses[0].request.type)
                console.log("unsubscription request details - " +  rateUnsubscriptionResponses[0].request.requestId)
                console.log("unsubscription request details - " +  rateUnsubscriptionResponses[0].request.customAggregation)
                assert.equal(rateUnsubscriptionResponses[0].request.type, 'FULL')
                assert.equal(rateUnsubscriptionResponses[0].request.requestId, reqId)
                assert.equal(rateUnsubscriptionResponses[0].request.customAggregation, true)
                assert.equal(rateUnsubscriptionResponses[0].request.depth, '0')
                assert.equal(rateUnsubscriptionResponses[0].status, 'success')

                //, '{"requestId":"' + parseInt(reqId) + '","customAggregation":false,"depth":0}')
            });

        });

	    describe("TC2 - FULL - customAggregation : true - provider tag", function () {
		    before(function (done) {
                reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: rate - reqId = " + reqId)
                 var subrequests = [{ 
                     symbol : mdData.supportedSymbol,
                     type   : "FULL",
                     requestId : parseInt(reqId),
                     org : userData.orgname,
                     customAggregation: true,
                     providers : ["VISA"]
                 }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("MarketData: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                connection.onmessage = (e) => {
	    			let res = JSON.parse(e.data)
		    		console.log("MarketData: rate - res : " + JSON.stringify(res))
			    	if (res.rate) {
				    	rate = res.rate
					    console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
					    done()
			    	}
			     }
		    });

            //Sample rate respone --
            // {"rate":{"symbol":"EUR/USD","bid":[1.18767,1.18767,1.18767],"offer":[1.18968,1.18968,1.18968],"bidLimit":[1000000,3000000,7000000],"offerLimit":[1000000,3000000,7000000],"time":1704224481543,"requestId":"76"}}
    		it("TC2 - FULL - customAggregation : true - provider tag", function () {
    		    console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
		    	assert.equal(mdData.supportedSymbol, rate.symbol)
			    assert.exists("bid")
    			assert.exists("offer")
	    		assert.exists("bidLimit")
		    	assert.exists("offerLimit")
			    assert.notEqual(0, rate.bid)
			    assert.notEqual(0, rate.offer)
			    assert.notEqual(0, rate.bidLimit)
			    assert.notEqual(0, rate.offerLimit)
			    assert.exists("requestId")
			    assert.exists("time")
		    });

            after(function (done) {
		        unsubscriptionRequest = '{"rateUnsubscriptions":[{"requestId" : "' + reqId + '", "customAggregation": "true", "type": "FULL"}]}'
	            connection.send(unsubscriptionRequest)
                 connection.onmessage = (e) => {
                    let res = JSON.parse(e.data)
                    if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
                        done()
                    }
                 }
            });
	    });


	    describe("TC3 - FULL - customAggregation : true - requested size tag ", function () {
	    // requestedSize is used to specify the LP tier from where the liquidity should be considered for aggregation.
	    // This tc doesnt have proper validation of this since LP rates are not fixed.
		    before(function (done) {
                reqId = Math.floor(Math.random() * 100)
                console.log("TC3 MarketData: rate - reqId = " + reqId)
                 var subrequests = [{
                     symbol : mdData.supportedSymbol,
                     type   : mdData.typeFull,
                     requestId : parseInt(reqId),
                     org : userData.orgname,
                     customAggregation: true,
                     requestedSize : 10000
                 }]
                 var wsreq = { rateSubscriptions : subrequests }
                 console.log("TC3 MarketData: request : " + JSON.stringify(wsreq))
                 connection.send(JSON.stringify(wsreq));
    		        connection.onmessage = (e) => {
                        let res = JSON.parse(e.data)
                        console.log("TC3 MarketData: subscription - res : " + JSON.stringify(res))
                        if (res.rateSubscriptionResponses) {
                            rateSubscriptionResponses = res.rateSubscriptionResponses
                            console.log("TC3 MarketData: subscription test- rateSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponses))
                            //done()
                        }  else if (res.rate) {
                            rate = res.rate
                            console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
                            done()
 			    	    }

       			    }
       		});

            it("TC3 - FULL - customAggregation : true - requested size tag ", function () {
                console.log("TC3 MarketData: subscription - MarketDataSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponses))
                assert.equal(rateSubscriptionResponses[0].status, 'success')
                assert.equal(rateSubscriptionResponses[0].request.symbol, mdData.supportedSymbol)
                assert.equal(mdData.supportedSymbol, rate.symbol)
                assert.exists("requestId")
            });

            after(function (done) {
		        unsubscriptionRequest = '{"rateUnsubscriptions":[{"requestId" : "' + reqId + '", "customAggregation": "true", "type": "FULL"}]}'
	            connection.send(unsubscriptionRequest)
                connection.onmessage = (e) => {
                    let res = JSON.parse(e.data)
                    if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
                        done()
                    }
                }
            });
	    });


	    describe("TC4 - FULL - customAggregation : true - dealtCurrency : baseCcy ", function () {
		    before(function (done) {
                reqId = "TC4_Full_CustomeAgg_dealtCcy_" + Math.floor(Math.random() * 100)
                console.log("MarketData: rate - reqId = " + reqId)
                 var subrequests = [{
                             symbol : mdData.supportedSymbol,
                             type   : "FULL",
                             requestId : reqId,
                             org : userData.orgname,
                             customAggregation: true,
                             providers : ["VISA"],
                             dealtCurrency : mdData.supportedSymbol_baseCcy
                         }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("MarketData: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
    			 connection.onmessage = (e) => {
	    			let res = JSON.parse(e.data)
		    		console.log("MarketData: rate - res : " + JSON.stringify(res))
			    	if (res.rate) {
				    	rate = res.rate
					    console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
					    done()
			    	}
			     }
		    });

            //Sample rate respone --
            // MarketData: rate - rate response : {"symbol":"EUR/USD","bid":[1.27532],"offer":[1.27652],"bidLimit":[10000000],"offerLimit":[10000000],"bidProviders":["VISA "],"offerProviders":["VISA "],"time":1744380849312,"requestId":"TC4_68"}
    		it("TC4 - FULL - customAggregation : true - dealtCurrency : baseCcy", function () {
    		    console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
		    	assert.equal(mdData.supportedSymbol, rate.symbol)
			    assert.exists("bid")
    			assert.exists("offer")
	    		assert.exists("bidLimit")
		    	assert.exists("offerLimit")
			    assert.notEqual(0, rate.bid)
			    assert.notEqual(0, rate.offer)
			    assert.notEqual(0, rate.bidLimit)
			    assert.notEqual(0, rate.offerLimit)
			    assert.exists("requestId")
			    assert.exists("time")
		    });

		    after(function (done) {
		        unsubscriptionRequest = '{"rateUnsubscriptions":[{"requestId" : "' + reqId + '", "customAggregation": "true", "type": "FULL"}]}'
	            connection.send(unsubscriptionRequest)
	            connection.onmessage = (e) => {
                    let res = JSON.parse(e.data)
                    console.log("unsubscription res : " + JSON.stringify(res))
                    if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
                        done()
       			    }
                }
            });
        });

	    describe("TC5 - FULL - customAggregation : true - dealtCurrency : termCcy", function () {

		    before(function (done) {
                reqId = "TC5_Full_CustomeAgg_dealtCcy_" + Math.floor(Math.random() * 100)
                console.log("MarketData: rate - reqId = " + reqId)
                 var subrequests = [{
                    symbol : mdData.supportedSymbol,
                    type   : "FULL",
                    requestId : reqId,
                    org : userData.orgname,
                    customAggregation: true,
                    providers : ["VISA"],
                    dealtCurrency : mdData.supportedSymbol_termCcy
                }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("MarketData: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
    		    connection.onmessage = (e) => {
	    			let res = JSON.parse(e.data)
		    		console.log("MarketData: rate - res : " + JSON.stringify(res))
			    	if (res.rate) {
				    	rate = res.rate
					    console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
					    done()
			    	}
			    }
		    });

            //Sample rate respone --
            // MarketData: rate - rate response : {"symbol":"EUR/USD","bid":[1.27532],"offer":[1.27652],"bidLimit":[10000000],"offerLimit":[10000000],"bidProviders":["VISA "],"offerProviders":["VISA "],"time":1744380849312,"requestId":"TC4_68"}
    		it("TC5 - FULL - customAggregation : true - dealtCurrency : termCcy", function () {
    		    console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
		    	assert.equal(mdData.supportedSymbol, rate.symbol)
			    assert.exists("bid")
    			assert.exists("offer")
	    		assert.exists("bidLimit")
		    	assert.exists("offerLimit")
			    assert.notEqual(0, rate.bid)
			    assert.notEqual(0, rate.offer)
			    assert.notEqual(0, rate.bidLimit)
			    assert.notEqual(0, rate.offerLimit)
			    assert.exists("requestId")
			    assert.exists("time")
		    });


		     after(function (done) {
		        unsubscriptionRequest = '{"rateUnsubscriptions":[{"requestId" : "' + reqId + '", "customAggregation": "true", "type": "FULL"}]}'
	            connection.send(unsubscriptionRequest)
                connection.onmessage = (e) => {
    			    let res = JSON.parse(e.data)
                    console.log(" unsubscribing res : " + JSON.stringify(res))
                    if (res.rateUnsubscriptionResponses) {
                        rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
                        console.log("unsubscribed " + reqId )
                        done()
      			    }
                }
             });
	    });



    });
};

marketDataTC();
