const assert = require('chai').assert
const WebSocket = require('ws')

const env = require('../../config/properties').env
const mdData = require('../../config/properties').mdData
const userData = require('../../config/properties').user

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

let marketDataTC = function(){

    describe("MarketData ", function () {

	    before(function (done) {
		    wsconnect(done);
	    });

	    after(function () {
		    connection.close()
	    });


// Keep unsubscription test before subscription test, otherwise reqIds will get messed up
        describe("TC1 - Unsubscription test ", function () {
   		    before(function (done) {
   		        reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: Unsubscription - reqId : " + reqId)
                var subrequests = [{ 
                             symbol : mdData.supportedSymbol,
                             type   : mdData.typeVwap,
                             requestId : parseInt(reqId),
                             tiers : [1000000],
                             org : userData.orgname,
                         }]
                var wsreq = { rateSubscriptions : subrequests }
                //console.log("MarketData: Unsubscription - rateSubscription : " +'{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeVwap + '","requestId":' + parseInt(reqId)+'}]}')
                //connection.send('{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeVwap + '","requestId":' + parseInt(reqId)+'}]}')
                connection.send(JSON.stringify(wsreq));

                console.log("MarketData: Sending Unsubscription request - reqId = " + JSON.stringify(wsreq))
                connection.send('{"rateUnsubscriptions":[{"requestId":' + parseInt(reqId) + ',"customAggregation":false,"depth":0}]}')
  			    
                connection.onmessage = (e) => {
    			    let res = JSON.parse(e.data)
        			console.log("MarketData: Unsubscription - res : " + JSON.stringify(res))
        			if (res.rateUnsubscriptionResponses) {
    	    			rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
    		    	    console.log("MarketData: Unsubscription - rateUnsubscriptionResponses : " + JSON.stringify(rateUnsubscriptionResponses))
    			        done()
    			    }
    		    }
    	    });

         //{"rateUnsubscriptionResponses":[{"request":{"requestId":19},"status":"success"}]}
    	 it("TC1 - MarketData Customized - Unsubscription test", function () {
       		    console.log("MarketData: Unsubscription - reqId : " + '{"reqId":' + parseInt(reqId) + '}')
        		assert.equal(rateUnsubscriptionResponses[0].status, 'success')
        		assert.equal(JSON.stringify(rateUnsubscriptionResponses[0].request), '{"requestId":"' + parseInt(reqId) + '","customAggregation":false,"depth":0}')
    	    });
        });

	    describe("TC2 - VWAP Rate Message test with custom agg true ", function () {

		    before(function (done) {
                reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: rate - reqId = " + reqId)
                 var subrequests = [{ 
                             symbol : mdData.supportedSymbol,
                             type   : "VWAP",
                             requestId : parseInt(reqId),
                             org : userData.orgname,
                             customAggregation: true,
                             tiers : [1000000,3000000,7000000]
                         }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("MarketData: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
    			 connection.onmessage = (e) => {
	    			let res = JSON.parse(e.data)
		    		console.log("MarketData: rate - res : " + JSON.stringify(res))
			    	if (res.rate) {
				    	rate = res.rate
					    console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
					    done()
			    	}
			     }
		    });

            //Sample rate respone --
            // {"rate":{"symbol":"EUR/USD","bid":[1.18767,1.18767,1.18767],"offer":[1.18968,1.18968,1.18968],"bidLimit":[1000000,3000000,7000000],"offerLimit":[1000000,3000000,7000000],"time":1704224481543,"requestId":"76"}}
    		it("TC2 - VWAP rate test with custom agg true - Rate test", function () {
    		    console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
		    	assert.equal(mdData.supportedSymbol, rate.symbol)
			    assert.exists("bid")
    			assert.exists("offer")
	    		assert.exists("bidLimit")
		    	assert.exists("offerLimit")
		    	assert.exists(rate.bidLimit[0])
		    	assert.exists(rate.bidLimit[1])
		    	assert.exists(rate.bidLimit[2])
		    	assert.exists(rate.offerLimit[0])
		    	assert.exists(rate.offerLimit[2])
//		    	assert.equal(rate.bidLimit[0],1000000)
//		    	assert.equal(rate.bidLimit[1],3000000)
//		    	assert.equal(rate.bidLimit[2],7000000)
//		    	assert.equal(rate.offerLimit[0],1000000)
//		    	assert.equal(rate.offerLimit[1],3000000)
//		    	assert.equal(rate.offerLimit[2],7000000)
			    assert.notEqual(0, rate.bid)
			    assert.notEqual(0, rate.offer)
			    assert.notEqual(0, rate.bidLimit)
			    assert.notEqual(0, rate.offerLimit)
			    assert.exists("requestId")
			    assert.exists("time")
		    });

		     after(function (done) {
	                connection.send('{"rateUnsubscriptions":[{"requestId":' + parseInt(reqId) + '}]}')
                     done()
              });
	    });

	    describe("TC3 - Rate Subscription response test with custom agg true ", function () {

		    before(function (done) {
                reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: rate - reqId = " + reqId)
                 var subrequests = [{
                             symbol : mdData.supportedSymbol,
                             type   : "VWAP",
                             requestId : parseInt(reqId),
                             org : userData.orgname,
                             customAggregation: true,
                             tiers : [1000000,3000000,7000000]
                         }]
                var wsreq = { rateSubscriptions : subrequests }
                console.log("MarketData: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
    		 connection.onmessage = (e) => {
            				    let res = JSON.parse(e.data)
                				console.log("MarketData: subscription - res : " + JSON.stringify(res))
            	    			if (res.rateSubscriptionResponses) {
            		    			rateSubscriptionResponses = res.rateSubscriptionResponses
            			    		console.log("MarketData: subscription test- rateSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponses))
            				    	done()
            				    }
            			    }
            		    });

             it("TC3 - MarketData Generic - Subscription test", function () {
            	    	    console.log("MarketData: subscription - MarketDataSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponses))
            		    	assert.equal(rateSubscriptionResponses[0].status, 'success')
            			    assert.equal(rateSubscriptionResponses[0].request.symbol, mdData.supportedSymbol)
                            assert.equal(rateSubscriptionResponses[0].request.type, mdData.typeVwap)
                            assert.equal(rateSubscriptionResponses[0].request.requestId, reqId)
            	    	});

		        after(function (done) {
	                connection.send('{"rateUnsubscriptions":[{"requestId":' + parseInt(reqId) + '}]}')
                     done()
              });
	    });

    });
};

let NegativeTC = function () {

         	    before(function (done) {
         		    wsconnect(done);
         	    });

         	    after(function () {
         		    connection.close()
         	    });


        describe("NTC1 Rate Subscription for VWP Tiers for FULL Book ", function () {
   		    before(function (done) {
   		        reqId = Math.floor(Math.random() * 100)
                console.log("MarketData: Unsubscription - reqId : " + reqId)
                var subrequests = [{
                             symbol : mdData.supportedSymbol,
                             type   : mdData.typeVwap,
                             requestId : parseInt(reqId),
                             org : userData.orgname,
                             tiers : [10000,20000,30000]
                         }]
                var wsreq = { rateSubscriptions : subrequests }
                //console.log("MarketData: Unsubscription - rateSubscription : " +'{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeVwap + '","requestId":' + parseInt(reqId)+'}]}')
                //connection.send('{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeVwap + '","requestId":' + parseInt(reqId)+'}]}')
                connection.send(JSON.stringify(wsreq));

                console.log("MarketData: Sending Unsubscription request - reqId = " + JSON.stringify(wsreq))
                connection.send('{"rateUnsubscriptions":[{"requestId":' + parseInt(reqId) + '}]}')

                connection.onmessage = (e) => {
    			    let res = JSON.parse(e.data)
        			console.log("MarketData: Unsubscription - res : " + JSON.stringify(res))
        			if (res.rateUnsubscriptionResponses) {
    	    			rateUnsubscriptionResponses = res.rateUnsubscriptionResponses
    	    			reqId = rateUnsubscriptionResponses[0].request.requestId
    		    	    console.log("MarketData: Unsubscription - rateUnsubscriptionResponses : " + JSON.stringify(rateUnsubscriptionResponses))
    			        done()
    			    }
    		    }
    	    });

         //{"rateUnsubscriptionResponses":[{"request":{"requestId":19},"status":"success"}]}
    	    it("NTC1 MarketData Customized - Unsubscription test", function () {
        		assert.equal(rateUnsubscriptionResponses[0].status, 'success')
        		assert.equal(JSON.stringify(rateUnsubscriptionResponses[0].request), '{"requestId":"' + reqId + '","customAggregation":false,"depth":0}')
    	    });

        });


}

marketDataTC();
NegativeTC();