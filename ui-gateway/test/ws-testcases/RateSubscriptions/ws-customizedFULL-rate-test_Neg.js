const assert = require('chai').assert
const WebSocket = require('ws')

const env = require('../../config/properties').env
const mdData = require('../../config/properties').mdData
const userData = require('../../config/properties').user

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey  //'Cg2oDBUxwF5typzfBH59W4ttTrY5mMlz'
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }


let NegativeTC = function () {

    before(function (done) {
        wsconnect(done);
    });

    after(function () {
        connection.close()
    });

    describe("NTC1 Rate Subscription for FULL Book with Tiers gives error ", function () {
        before(function (done) {
            reqId = "NTC1_" + Math.floor(Math.random() * 100)
            console.log("NTC1 MarketData: Subscription- reqId : " + reqId)
            var subrequests = [{
                         symbol : mdData.supportedSymbol,
                         type   : mdData.typeFull,
                         requestId : parseInt(reqId),
                         org : userData.orgname,
                         tiers : [200000]
                     }]
            var wsreq = { rateSubscriptions : subrequests }
            //console.log("MarketData: Unsubscription - rateSubscription : " +'{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeFull + '","requestId":' + parseInt(reqId)+'}]}')
            //connection.send('{"rateSubscriptions":[{"symbol":"' + mdData.supportedSymbol + '","type":"' + mdData.typeFull + '","requestId":' + parseInt(reqId)+'}]}')
            connection.send(JSON.stringify(wsreq));

            connection.onmessage = (e) => {
                let res = JSON.parse(e.data)
                console.log("NTC1 MarketData: Subscription - res : " + JSON.stringify(res))
                if (res.rateSubscriptionResponses) {
                    rateSubscriptionResponses = res.rateSubscriptionResponses
                    console.log("NTC1 MarketData: rateSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponses))
                    done()
                }
            }
        });

    //NTC1 MarketData: rateSubscriptionResponses : [{"request":{"symbol":"EUR/USD","type":"FULL","org":"pfAutomationWSuser1",
    //"tiers":[200000],"customAggregation":false,"depth":0},"status":"failed",
    //"errors":[{"errorCode":101,"errorMessage":"request id is required."},{"errorCode":114,
    //"errorMessage":"Tiers are not needed for aggregation type FULL"}] }]
        it("NTC1 Error Message Test ", function () {
            assert.equal(rateSubscriptionResponses[0].status, 'failed')
            assert.equal(rateSubscriptionResponses[0].request.symbol, mdData.supportedSymbol)
            assert.exists(rateSubscriptionResponses[0].request.tiers)
            //assert.exists(rateSubscriptionResponses[0].request.requestId)
            assert.equal(JSON.stringify(rateSubscriptionResponses[0].errors[0].errorMessage), '"request id is required."')
            assert.equal(JSON.stringify(rateSubscriptionResponses[0].errors[0].errorCode), 101)
            assert.equal(JSON.stringify(rateSubscriptionResponses[0].errors[1].errorMessage), '"Tiers are not needed for aggregation type FULL"')
            assert.equal(JSON.stringify(rateSubscriptionResponses[0].errors[1].errorCode), 114)

        });

    });

    describe("NTC2 Rate Subscription for providers ALL for FULL Book ", function () {
        before(function (done) {
            reqId = Math.floor(Math.random() * 100)
            var subrequests = [{
                         symbol : mdData.supportedSymbol,
                         type   : mdData.typeFull,
                         requestId : parseInt(reqId),
                         org : userData.orgname,
                         customAggregation : true,
                         providers : ["ALL"]
                     }]
            var wsreq = { rateSubscriptions : subrequests }
            connection.send(JSON.stringify(wsreq));

            count = 1;
            connection.onmessage = (e) => {
                let res = JSON.parse(e.data)
                console.log("NTC2 - MarketData: rateSubscriptionResponses - res : " + JSON.stringify(res.rateSubscriptionResponses[0].status))
                  if(res.rateSubscriptionResponses) {
                          rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                          count = parseInt(count) + 1
                            if(rateSubscriptionResponses.status == "success") {
                                rateSubscriptionResponsesSuccess = rateSubscriptionResponses
                                console.log("TC3 MarketData: subscription test- rateSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponsesSuccess))
                                //done()
                            } else if(rateSubscriptionResponses.status == "failed") {
                                rateSubscriptionResponsesFailure = rateSubscriptionResponses
                                done()
                            }
                  } if (res.rate) {
                        rate = res.rate
                        console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
                        done()
                  }
              }
           });

        it("NTC2 Error Message Test ", function () {
            console.log("rateSubscriptionResponsesSuccess: " +JSON.stringify(rateSubscriptionResponsesSuccess))
            console.log("rateSubscriptionResponsesFailure: " +JSON.stringify(rateSubscriptionResponsesFailure))
            assert.equal(rateSubscriptionResponsesSuccess.status, 'success')
            assert.equal(rateSubscriptionResponsesSuccess.request.symbol, mdData.supportedSymbol)
            assert.exists(rateSubscriptionResponsesSuccess.request.requestId)
            assert.equal(JSON.stringify(rateSubscriptionResponsesFailure.errors[0].errorMessage), '"NO_PROVIDER"')
            assert.equal(JSON.stringify(rateSubscriptionResponsesFailure.errors[0].errorCode), 0)
        });

    });

    describe("NTC3 Rate Subscription customAggregation : false with provider tag for FULL Book ", function () {
        before(function (done) {
            reqId = Math.floor(Math.random() * 100)
            var subrequests = [{
                         symbol : mdData.supportedSymbol,
                         type   : mdData.typeFull,
                         requestId : parseInt(reqId),
                         org : userData.orgname,
                         customAggregation : false,
                         providers : ["VISA"]
                     }]
            var wsreq = { rateSubscriptions : subrequests }
            connection.send(JSON.stringify(wsreq));

            count = 1;
            connection.onmessage = (e) => {
                let res = JSON.parse(e.data)
                console.log("TC3 MarketData: subscription test- res : " + JSON.stringify(res))
// {"rateSubscriptionResponses":[{"request":{"symbol":"EUR/USD","type":"FULL","org":"pfAutomationWSuser1","requestId":"80","customAggregation":false,"providers":["VISA"],"depth":0},"status":"failed","errors":[{"errorCode":111,"errorMessage":"requestedSize, providers, depth can be specified only for customAggregation."}]}]}
                  if(res.rateSubscriptionResponses) {
                          rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                          count = parseInt(count) + 1
                            if(rateSubscriptionResponses.status == "success") {
                                rateSubscriptionResponsesSuccess = rateSubscriptionResponses
                                console.log("TC3 MarketData: subscription test- rateSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponsesSuccess))
                                //done()
                            } else if(rateSubscriptionResponses.status == "failed") {
                                rateSubscriptionResponsesFailure = rateSubscriptionResponses
                                done()
                            }
                  } if (res.rate) {
                        rate = res.rate
                        console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
                        done()
                  }
              }
           });

        it("NTC3 Rate Subscription customAggregation : false with provider tag for FULL Book", function () {
            console.log("rateSubscriptionResponsesFailure: " +JSON.stringify(rateSubscriptionResponsesFailure))
            console.log("rateSubscriptionResponsesFailure: " +JSON.stringify(rateSubscriptionResponsesFailure))
            assert.equal(rateSubscriptionResponsesFailure.status, 'failed')
            assert.equal(rateSubscriptionResponsesFailure.request.symbol, mdData.supportedSymbol)
            assert.exists(rateSubscriptionResponsesFailure.request.requestId)
            assert.equal(JSON.stringify(rateSubscriptionResponsesFailure.errors[0].errorMessage), '"requestedSize, providers, depth can be specified only for customAggregation."')
            assert.equal(JSON.stringify(rateSubscriptionResponsesFailure.errors[0].errorCode), 111)
        });

    });

    describe("NTC4 Rate Subscription customAggregation : false with dealtCurrency tag for FULL Book ", function () {
        before(function (done) {
            reqId = Math.floor(Math.random() * 100)
            var subrequests = [{
                         symbol : mdData.supportedSymbol,
                         type   : mdData.typeFull,
                         requestId : parseInt(reqId),
                         org : userData.orgname,
                         customAggregation : false,
                         dealtCurrency : mdData.supportedSymbol_termCcy
                     }]
            var wsreq = { rateSubscriptions : subrequests }
            connection.send(JSON.stringify(wsreq));

            count = 1;
            connection.onmessage = (e) => {
                let res = JSON.parse(e.data)
                console.log("TC4 MarketData: subscription test- res : " + JSON.stringify(res))
// {"rateSubscriptionResponses":[{"request":{"symbol":"EUR/USD","type":"FULL","org":"pfAutomationWSuser1","requestId":"80","customAggregation":false,"providers":["VISA"],"depth":0},"status":"failed","errors":[{"errorCode":111,"errorMessage":"requestedSize, providers, depth can be specified only for customAggregation."}]}]}
                  if(res.rateSubscriptionResponses) {
                          rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                          count = parseInt(count) + 1
                            if(rateSubscriptionResponses.status == "success") {
                                rateSubscriptionResponsesSuccess = rateSubscriptionResponses
                                console.log("TC4 MarketData: subscription test- rateSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponsesSuccess))
                                //done()
                            } else if(rateSubscriptionResponses.status == "failed") {
                                rateSubscriptionResponsesFailure = rateSubscriptionResponses
                                done()
                            }
                  } if (res.rate) {
                        rate = res.rate
                        console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
                        done()
                  }
              }
           });

        it("NTC4 Rate Subscription customAggregation : false with dealtCurrency tag for FULL Book ", function () {
            console.log("rateSubscriptionResponsesFailure: " +JSON.stringify(rateSubscriptionResponsesFailure))
            assert.equal(rateSubscriptionResponsesFailure.status, 'failed')
            assert.equal(rateSubscriptionResponsesFailure.request.symbol, mdData.supportedSymbol)
            assert.exists(rateSubscriptionResponsesFailure.request.requestId)
            assert.equal(JSON.stringify(rateSubscriptionResponsesFailure.errors[0].errorMessage), '"Dealt currency is valid only for Custom Aggregation."')
            assert.equal(JSON.stringify(rateSubscriptionResponsesFailure.errors[0].errorCode), 117)
        });

    });

    describe("NTC5 Rate Subscription customAggregation : false with requestedSize tag for FULL Book ", function () {
        before(function (done) {
            reqId = Math.floor(Math.random() * 100)
            var subrequests = [{
                         symbol : mdData.supportedSymbol,
                         type   : mdData.typeFull,
                         requestId : parseInt(reqId),
                         org : userData.orgname,
                         customAggregation : false,
                         requestedSize : 10000
                     }]
            var wsreq = { rateSubscriptions : subrequests }
            connection.send(JSON.stringify(wsreq));

            count = 1;
            connection.onmessage = (e) => {
                let res = JSON.parse(e.data)
                console.log("TC5 MarketData: subscription test- res : " + JSON.stringify(res))
// {"rateSubscriptionResponses":[{"request":{"symbol":"EUR/USD","type":"FULL","org":"pfAutomationWSuser1","requestId":"80","customAggregation":false,"providers":["VISA"],"depth":0},"status":"failed","errors":[{"errorCode":111,"errorMessage":"requestedSize, providers, depth can be specified only for customAggregation."}]}]}
                  if(res.rateSubscriptionResponses) {
                          rateSubscriptionResponses = res.rateSubscriptionResponses[0]
                          count = parseInt(count) + 1
                            if(rateSubscriptionResponses.status == "success") {
                                rateSubscriptionResponsesSuccess = rateSubscriptionResponses
                                console.log("TC5 MarketData: subscription test- rateSubscriptionResponses : " + JSON.stringify(rateSubscriptionResponsesSuccess))
                                //done()
                            } else if(rateSubscriptionResponses.status == "failed") {
                                rateSubscriptionResponsesFailure = rateSubscriptionResponses
                                done()
                            }
                  } if (res.rate) {
                        rate = res.rate
                        console.log("MarketData: rate - rate response : " + JSON.stringify(rate))
                        done()
                  }
              }
           });

        it("NTC5 Rate Subscription customAggregation : false with requestedSize tag for FULL Book ", function () {
            console.log("rateSubscriptionResponsesFailure: " +JSON.stringify(rateSubscriptionResponsesFailure))
            console.log("rateSubscriptionResponsesFailure: " +JSON.stringify(rateSubscriptionResponsesFailure))
            assert.equal(rateSubscriptionResponsesFailure.status, 'failed')
            assert.equal(rateSubscriptionResponsesFailure.request.symbol, mdData.supportedSymbol)
            assert.exists(rateSubscriptionResponsesFailure.request.requestId)
            assert.equal(JSON.stringify(rateSubscriptionResponsesFailure.errors[0].errorMessage), '"requestedSize, providers, depth can be specified only for customAggregation."')
            assert.equal(JSON.stringify(rateSubscriptionResponsesFailure.errors[0].errorCode), 111)
        });

    });

}

NegativeTC();
