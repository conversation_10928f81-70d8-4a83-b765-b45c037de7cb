const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const benckmarkData = require('../../config/properties').benckmarkData

let fxbenchmarkSubscriptionResponses
let fxbenchmarkUnsubscriptionResponses
let gridmidrate
let connection

//const login = require('../login').login

// below are needed for schema validation
const Validator = require('jsonschema').Validator;
const v = new Validator();
const benchmarkSchema = require('../../config/top10/benchmark-schema')
let res

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

let gmrNegativeTC = function(){
    describe("GMR negative scenario -", function(){
    	before(function (done) {
    		wsconnect(done);
    	});

    	after(function () {
    		connection.close()
    	});



    	describe("Invalid JSON test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.errors) {
            			    errors = res.errors
                            console.log('errors : ' + JSON.stringify(errors))
            				done()
            		}
            	}
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol1":' +  benckmarkData.supportedSymbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
            });

         	it("Benchmark GMR - InvalidJSON CP test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
                assert.equal('1', JSON.parse(JSON.stringify(res.errors[0].errorCode)))
                assert.equal('Not a valid request.', JSON.parse(JSON.stringify(res.errors[0].errorMessage)))
            });
        });

    	describe("InvalidCP subscription test", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('gmrSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.invalidSymbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
            });

         	it("Benchmark GMR - Invalid CP test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('204', JSON.parse(JSON.stringify(fxbenchmarkSubscriptionResponses[0].errors[0])).errorCode)
                assert.equal('Currency pair not supported.', JSON.parse(JSON.stringify(fxbenchmarkSubscriptionResponses[0].errors[0])).errorMessage)

            });

        });

    	describe("UnsupportedCP subscription test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('gmrSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.unsupportedSymbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
            });

         	it("Benchmark GMR - UnsupportedCP test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('204', JSON.parse(JSON.stringify(fxbenchmarkSubscriptionResponses[0].errors[0])).errorCode)
                assert.equal('Currency pair not supported.', JSON.parse(JSON.stringify(fxbenchmarkSubscriptionResponses[0].errors[0])).errorMessage)

            });
        });

    	describe("Unsupported PriceSource test ", function () {
    	    before(function (done) {
                connection.onmessage = (e) => {
            	    let res = JSON.parse(e.data)
            		console.log("res =" + JSON.stringify(res))
            		 if (res.fxbenchmarkSubscriptionResponses) {
            			    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('gmrSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            				done()
            		}
            	}
             		connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.supportedSymbol + '","priceSource":"' + benckmarkData.unsupportedPriceSource + '"}]}')
            });

         	it("Benchmark GMR - Unsupported PriceSource test", function(){
                console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
            	assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('202', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('Valid Price Source is required.', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

   	    describe("Duplicate subscription test ", function () {
    	    before(function (done) {
    		    let tmp=false
     		    connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
     		    connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
    		    connection.onmessage = (e) => {
    			    let res = JSON.parse(e.data)
    			    console.log("res =" + JSON.stringify(res))
    			    if (res.fxbenchmarkSubscriptionResponses) {
           		        if (res.fxbenchmarkSubscriptionResponses[0].errors) {
        				    fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                            console.log('gmrSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
                            tmp=true
               		    }
    			    }
           		    if (tmp) {
               		    done();
           		    }
    		    }
                connection.close();
 		    });

    		after(function () {
                connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.gmrPriceSource + '"}]}')
           	});

     	    it("Benchmark GMR - Duplicate subscription test", function(){
                console.log('duplicateSubscriptionResponse: ' + JSON.stringify(fxbenchmarkSubscriptionResponses));
           	    assert.equal('failed', fxbenchmarkSubscriptionResponses[0].status)
                assert.equal('205', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('Currency pair already subscribed.', JSON.parse(JSON.stringify((fxbenchmarkSubscriptionResponses[0].errors)[0])).errorMessage)
            });
        });

    });
}

gmrNegativeTC();

