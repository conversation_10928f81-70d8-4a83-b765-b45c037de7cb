const assert = require('chai').assert
const expect = require('chai').expect
const WebSocket = require('ws')

//const login = require('../login').login
const env = require('../../config/properties').env
const rfsData = require('../../config/properties').rfsData

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId
let systemReqId
let rfsWithdrawResponse
let rfsInactiveQuote
let rfsActiveQuote
let rfsSubscriptionAck
let rfsWithdrawAck
let res
let errors
let Host
let apikey

// Login credentials should be for MDF enabled org
// For marketdata scripts, org should be getting rates in MDF
// Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

let wsconnect = function (done, cookies) {
    //const websocket_url = 'ws://' + env.kongHost + ':' + env.kongPort + '/fxstream'
    const websocket_url = 'wss://' + env.hostname +  '/v2/fxstream'
    connection = new WebSocket(websocket_url, [], {
        'headers': {
			'Host': env.apiHost,
			'apikey': env.apikey
        }
    })

    connection.onopen = () => {

        done()
    }

    connection.onerror = (error) => {
        console.log(`WebSocket error: ${error}`)
    }
}

let rfsOutrightNegativeTC = function(){

// =================== all the negative tcs with error code "request.validation.tradingrelationship.sd.cptyaorg.relationship.missing" should be revisited or corrected

    describe("RFS outright Negative scenario ", function () {

        before(function (done) {
            wsconnect(done);
        });

        after(function () {
            connection.close()
        });

        //{ "rfsSubscriptions" : [ { "symbol": "EUR/USD", "amount": "1000000.0", "dealtCurrency": "EUR", "expiry": 15, "nearValueDate": "1W", "farDealtAmount" : "1000000.0","farValueDate" : "2W", "fixingDate" : "" , "farFixingDate" : "", "side": "BUY", "priceType": "Swap", "customerAccount": "pfOrg", "customerOrg": "pfOrg", "priceViewType": 1, "depth": 2, "channel": "DNET/RFS/BB", "providers": ["NTFX","MSFX","SG","SUCD","UBS","WFNA"], "clOrderId": "view1MultiLP2" } ] }
        tmpReqId = Math.floor(Math.random() * 100)

        describe("Invalid CP ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid CP ************************** ' + new Date());
                reqId = "RFS_OR_InvalidCP_"+tmpReqId
                console.log("Rfs outright - Invalid CP - > reqId = " + reqId)
                var subrequests = [{
                             symbol : 'ABC/ACB',
                             amount : "1000000.0",
                             dealtCurrency : rfsData.baseCcy,
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid CP - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid CP - > res : " + JSON.stringify(res))

                    if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                        done()
                    }
                }
            });
            //Rfs outright - Invalid CP - > res : {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,"clOrderId":"67","status":"ERROR","errorCode":"RequestValidationError.InvalidCurrency"}]}

            it("Invalid CP", function () {
                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                assert.equal('"RequestValidationError.InvalidCurrency"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
          });

        });

        describe("Invalid Amt ", function () {
        //{"expiryTimeInSeconds":0,"clOrderId":"2","status":"ERROR"}
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid Amt ************************** ' + new Date());
                reqId = "RFS_OR_InvalidAmt_"+tmpReqId
                console.log("Rfs outright - Invalid Amt - > reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "abc",
                             dealtCurrency : rfsData.baseCcy,
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid Amt - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid Amt - > res : " + JSON.stringify(res))

                    if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                        done()
                    }
                }
            });

            //"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

            it("Invalid Amt", function () {
                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                assert.equal('"RequestValidationError.amount"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))

          });

        });

        describe("Invalid dealtCurrency ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid dealtCurrency ************************** ' + new Date());
                reqId = "RFS_OR_InvalidDealtCurrency_"+tmpReqId
                console.log("Rfs outright - Invalid dealtCurrency -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "ABC",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid dealtCurrency - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                console.log("Rfs outright - Invalid dealtCurrency - > res : " + JSON.stringify(wsreq))
                    if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                        done()
                    }
                }
            });

//==========Rfs Spot - rate response : {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,
//"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

            it("Invalid dealtCurrency", function () {
                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                assert.equal('"RequestValidationError.InvalidCurrency"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
            });

        });

        describe("Without nearValueDate", function () {
// RFSSpot is ignoring value date, it allows 1W etc tenors too in the request, but actually considers as spot request based on priceType
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Without nearValueDate ************************** ' + new Date());
                reqId = "RFS_OR_WithoutNearValueDate_"+tmpReqId
                console.log("Rfs outright - Without nearValueDate : rate - reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Without nearValueDate -> rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Without nearValueDate - > res : " + JSON.stringify(res))

                    if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                        done()
                    }
                }
            });

            //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
            it("Without nearValueDate", function () {
                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                assert.equal('"RequestValidationError.ValueDateNotSpecified"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))

           });
        });

        describe("Invalid nearValueDate ", function () {
// AP-10389
// RFSSpot is ignoring value date, it allows 1W etc tenors too in the request, but actually considers as spot request based on priceType
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid nearValueDate ************************** ' + new Date());
                reqId = "RFS_OR_InvalidNearValueDate_"+tmpReqId
                console.log("Rfs outright - Invalid nearValueDate : rate - reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : "3",
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid nearValueDate -> rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid nearValueDate - > res : " + JSON.stringify(res))
                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                         } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                         }
                    } else if (res.rfsWithdrawAck) {
                        console.log("Rfs outright - Invalid nearValueDate -> rfsWithdrawAck received")
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        done()
                    } else if (res.rfsResponses) {
                        rfsWithdrawResponse = res.rfsResponses[0]
                        console.log("Rfs outright - Invalid nearValueDate -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                        done()
                    }
                }
            });

// quote : {"requestId":"G4796976d3188c01d9e5f4b7d","priceType":"Outright","effectiveTime":0,"symbol":"EUR/USD","ttl":119,"dealtCurrency":"EUR","status":"A","nearValueDate":"2023-06-26","bids":[{"legType":0,"quoteId":"G-4796976d4-188c01da1a1-NTFXA-1220-Spoorthy-NTFX-1686850347431","type":"BID","dealtAmount":1000000,"settledAmount":1053740,"provider":"NTFX","rate":1.05374,"spotRate":1.05364,"forwardPoint":0.0001,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-4796976d4-188c01da1a1-NTFXA-1220-Spoorthy-NTFX-1686850347431","type":"OFFER","dealtAmount":1000000,"settledAmount":1055060,"provider":"NTFX","rate":1.05506,"spotRate":1.05486,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
            it("Invalid nearValueDate", function () {
                console.log("===============Rfs outright - Invalid nearValueDate -> quote : " + JSON.stringify(rfsSubscriptionResponses))
                 assert.equal(0, rfsSubscriptionResponses.expiryTimeInSeconds)
                 assert.exists(rfsSubscriptionResponses.clOrderId)
                 assert.equal('ERROR', rfsSubscriptionResponses.status)
                 assert.equal('"INCORRECT_REQUEST_PARAMS"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
           });
        });

        describe("Without fixingDate", function () {

            let dealtAmt = '1000000'
            rfsResponses = ""
            rfsRates = ""
            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Without fixingDate ************************** ' + new Date());
                reqId = "RFS_OR_WithoutFixingDate_"+tmpReqId
                console.log("Rfs outright - Without fixingDate : rate - reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             //nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Without fixingDate -> rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                     console.log("Rfs outright - Without fixingDat - > res : " + JSON.stringify(res))

                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 2 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                         } else if (i ===2) {
                            console.log("systemReqId : " + systemReqId)
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                         }
                    } else if (res.rfsWithdrawAck) {
                        console.log("Rfs outright - Without fixingDate -> rfsWithdrawAck received")
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        done()
                    } else if (res.rfsResponses) {
                        rfsResponses = res.rfsResponses[0]
                        done()
                    }
                }
            });

            //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
            ////Rfs outright - Without fixingDat - > res : {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,"clOrderId":"RFS_OR_WithoutFixingDate_11","status":"ERROR","errorCode":"RequestValidationError.ValueDateNotSpecified"}]}
            it("Without fixingDate", function () {
                 console.log("Rfs outright - Without fixingDate - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                 assert.equal(0, rfsSubscriptionResponses.expiryTimeInSeconds)
                 assert.exists(rfsSubscriptionResponses.clOrderId)
                 assert.equal('ERROR', rfsSubscriptionResponses.status)
                 assert.equal('"RequestValidationError.ValueDateNotSpecified"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))

          });
        });

        describe("Invalid side ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid side ************************** ' + new Date());
                reqId = "RFS_OR_InvalidSide_"+tmpReqId
                console.log("Rfs outright - Invalid side -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : "ONE_WAY",
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid side - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid side - > res : " + JSON.stringify(res))

                    if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                        done()
                    } else if (res.errors) {
                        errors = res.errors
                        console.log("errors : "+ JSON.stringify(errors))
                        done()
                    }
                }
            });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
            it("Invalid Side", function () {
                assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
            });
        });

        describe("Invalid priceType ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid priceType ************************** ' + new Date());
                reqId = "RFS_OR_InvalidPriceType_"+tmpReqId
                console.log("Rfs outright - Invalid priceType -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : "xspot",
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid priceType - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid priceType - > res : " + JSON.stringify(res))

                    if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                        done()
                    } else if (res.errors) {
                        errors = res.errors
                        done()
                    }
                }
            });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
            it("Invalid priceType", function () {
               assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
            });
        });

        describe("Invalid customerAccount ", function () {
      // AP-10414 -> It should get rejected, but working fine.
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid customerAccount ************************** ' + new Date());
                reqId = "RFS_OR_InvalidCustomerAccount_"+tmpReqId
                console.log("Rfs outright - Invalid customerAccount -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : "abc",
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid customerAccount - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid customerAccount - > res : " + JSON.stringify(res))
                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                         } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                         }
                    } else if (res.rfsWithdrawAck) {
                        console.log("Rfs outright - Invalid customerAccount -> rfsWithdrawAck received")
                    } else if (res.rfsSubscriptionResponses) {
                        rfsWithdrawResponse = res.rfsSubscriptionResponses[0]
                        done()
                    }
                }

            });

            it("Invalid customerAccount", function () {
                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                assert.equal('"RequestValidationError.InvalidAccount"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))

             });
        });

        describe("Invalid customerOrg ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid customerOrg ************************** ' + new Date());
                reqId = "RFS_OR_InvalidCustomerOrg_"+tmpReqId
                console.log("Rfs outright - Invalid customerOrg -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: "ABCOrg",
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid customerOrg - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

               connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid customerOrg - > res : " + JSON.stringify(res))
                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                            console.log("Rfs outright - Invalid customerOrg -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                         } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                         }
                    } else if (res.rfsWithdrawAck) {
                        console.log("Rfs outright - Invalid customerOrg -> rfsWithdrawAck received")
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        done()
                    }
                }

            });

            it("Invalid customerOrg", function () {
               assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                assert.equal('"RequestValidationError.InvalidOrg"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))

            });

      });

        describe("Invalid priceViewType ", function () {
       // priceViewType =1 -> aggregated view, any other number (0 or other positive integers are considered as non aggregated view where each LP quote is sent separately
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid priceViewType ************************** ' + new Date());
                reqId = "RFS_OR_InvalidPriceViewType_"+tmpReqId
                console.log("Rfs outright - Invalid priceViewType -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: 8,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid priceViewType - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

               connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid priceViewType - > res : " + JSON.stringify(res))
                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                            console.log("Rfs outright - Invalid priceViewType -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                         } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                         }
                    } else if (res.rfsWithdrawAck) {
                        console.log("Rfs outright - Invalid priceViewType -> rfsWithdrawAck received")
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                    } else if (res.rfsResponses) {
                        rfsResponses = res.rfsResponses[0]
                        done()
                    }
               }

            });

            it("Invalid priceViewType", function () {
// Incorrect failure reason - 2023-06-15 12:53:21,300  INFO AsyncLog #[-] outgoing json message : <EMAIL> :
// {"rfsSubscriptionResponses":[{"requestId":"G4796976d3188bf1e1b0a4990","expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2023/06/15 12:53:21","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date 1W. RFS submission failed for NTFX"},"clOrderId":"88","status":"ERROR","errorCode":"Request.Validation.TradingRelationship.SD.CptyAOrg.Relationship.Missing"}]}
// Check PLT-4656
                console.log("Rfs outright - Negative priceViewType -> " + JSON.stringify(rfsResponses))
                assert.exists(JSON.stringify(rfsResponses.requestId))
                assert.exists(JSON.stringify(rfsResponses.rfsMessage))
                assert.exists(JSON.stringify(rfsResponses.clOrderId))

            });

       });

        describe("Negative priceViewType ", function () {
// testcase is failing with invalid reason
       // priceViewType =1 -> aggregated view, any other number (0 or other positive/Negative integers are considered as non aggregated view where each LP quote is sent separately
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Negative priceViewType ************************** ' + new Date());
                reqId = "RFS_OR_NegativePriceViewType_"+tmpReqId
                console.log("Rfs outright - Negative priceViewType -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: "-2",
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Negative priceViewType - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

               connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid priceViewType - > res : " + JSON.stringify(res))
                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 2 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                         } else if (i ===2) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                         }
                    } else if (res.rfsWithdrawAck) {
                        console.log("Rfs outright - Negative priceViewType -> rfsWithdrawAck received")
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        //done()
                    } else if (res.rfsResponses) {
                        rfsResponses = res.rfsResponses[0]
                        done()
                    }
                }

            });

            it("Negative priceViewType", function () {
                console.log("Rfs outright - Negative priceViewType -> " + JSON.stringify(rfsResponses))
                assert.exists(JSON.stringify(rfsResponses.requestId))
                assert.exists(JSON.stringify(rfsResponses.rfsMessage))
                assert.exists(JSON.stringify(rfsResponses.clOrderId))
            });

      });

        describe("Invalid depth ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid depth ************************** ' + new Date());
                reqId = "RFS_OR_InvalidDepth_"+tmpReqId
                console.log("Rfs outright - Invalid depth -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth: "a",
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid depth - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                connection.onmessage = (e) => {
                     res = JSON.parse(e.data)
                     console.log("Rfs outright - Invalid depth - > res : " + JSON.stringify(res))

                     if (res.rfsSubscriptionAck) {
                         rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                     } else if (res.rfsSubscriptionResponses) {
                         rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                         done()
                     } else if (res.errors) {
                         errors = res.errors[0]
                         done()
                     }
                 }
             });

             //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
             it("Invalid Depth", function () {
                 assert.equal('1', errors.errorCode)
                 assert.equal('Not a valid request.', errors.errorMessage)
             });
        });

        describe("Invalid providers ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid Providers ************************** ' + new Date());
                reqId = "RFS_OR_InvalidProviders_"+tmpReqId
                console.log("Rfs outright - Invalid Providers -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : rfsData.channel,
                             providers: ["ABC"],
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid providers - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                rfsActiveQuote = ""
                 connection.onmessage = (e) => {
                      res = JSON.parse(e.data)
                      console.log("Rfs outright - Invalid providers - > res : " + JSON.stringify(res))
                      if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                     } else if (res.rfsSubscriptionAck) {
                          rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                      } else if (res.rfsSubscriptionResponses) {
                          rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                          done()
                      } else if (res.errors) {
                          errors = res.errors
                         // done()
                      } else if (res.rfsResponses) {
                            rfsResponses = res.rfsResponses[0]
                            done()
                      }
                  }
              });

//current response is as below -- 
//{"rfsSubscriptionResponses":[{"requestId":"G4796976dd18d1bd6b409d966","expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2024/01/18 09:11:12","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date 1W. RFS submission failed for ABC"},"clOrderId":"RFS_OR_InvalidProviders_60","status":"ERROR","errorCode":"Request.Validation.ProrataForward.NonBroker.Provider.NotSupported"}]}
              it("Invalid Providers", function () {
//Rfs outright - Invalid providers - > res : {"rfsSubscriptionResponses":[{"requestId":"G4796976d3188bf27070c4998","expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2023/06/15 13:03:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date 1W. RFS submission failed for ABC"},"clOrderId":"46","status":"ERROR","errorCode":"Request.Validation.TradingRelationship.SD.CptyAOrg.Relationship.Missing"}]}
                console.log("Rfs outright - Invalid providers - > rfsSubscriptionResponses : ")
                console.log(rfsSubscriptionResponses)
                assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                assert.equal(reqId,rfsSubscriptionResponses.clOrderId)
                assert.equal("RFS Failed",rfsSubscriptionResponses.rfsMessage.eventName)
                expect((rfsSubscriptionResponses.rfsMessage.eventDetails)).to.have.string('RFS Submission Failed')
                //expect(JSON.stringify(rfsSubscriptionResponses.rfsMessage.eventDetails)).to.have.string('"RFS Request Withdrawn for EUR/USD with Dealt
                assert.equal('ERROR', rfsSubscriptionResponses.status)
                assert.equal('"INTERNAL_SERVER_ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
               });
           });

        describe("Invalid channel ", function () {

//TC needs to be fixed
//OA logs when it failed.
//2023-06-15 15:49:33,888  INFO RDS-receiver-1 RFSWorkflowHandlerC #[<EMAIL>] ConfigError: RFS Request validation failed for the User [user1], Organization [Spoorthy]. TransactionId FXI9351020171. Reason :  Request Validation Error. FI to LP Relationship missing between SD Request.Validation.TradingRelationship.SD.CptyAOrg.Relationship.Missing and CounterParty 0.
//2023-06-15 15:49:33,889  WARN RDS-receiver-1 RFSController #[<EMAIL>] RFSController.subscribe could not be processed for user:User user1@Spoorthy.9837001#171020688#V21 priceRequest=pfOrg~EUR/USD~1000000.0~EUR~TWO_WAY~Outright~abc~1W~null~null~~null~null~null~null~null~1~json20~5~null~null~null~null~due to error: Request.Validation.TradingRelationship.SD.CptyAOrg.Relationship.Missing
//2023-06-15 15:49:33,889  WARN RDS-receiver-1 RFSRMQMessageGateway #[<EMAIL>] handleRequest - Error in subscription Request.Validation.TradingRelationship.SD.CptyAOrg.Relationship.Missing id=45
//2023-06-15 15:49:33,890  INFO RDS-receiver-1 RFSRMQMessageGateway #[<EMAIL>] sendMessage RFSX sent. m=RfsRmqMessageWrapper{sentBy='ppfxiadp75-OAWl4, clOrderId='45, userFullName='<EMAIL>, rfsPriceResponse=BaseResponse{errorCode='Request.Validation.TradingRelationship.SD.CptyAOrg.Relationship.Missing', status=ERROR, responseTuples=null}} , rkey=RFS.UIG1

      // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright Negative TC - Invalid channel ************************** ' + new Date());
                reqId = "RFS_OR_InvalidChannel_"+tmpReqId
                console.log("Rfs outright - Invalid channel -> reqId = " + reqId )
                var subrequests = [{
                             symbol : 'EUR/USD',
                             amount : "1000000.0",
                             dealtCurrency : "EUR",
                             expiry: rfsData.expiry,
                             nearValueDate : rfsData.nlTenor,
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeOR,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth : rfsData.depth,
                             channel : "abc",
                             providers: rfsData.providers,
                             clOrderId: reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Invalid channel - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

               connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Invalid channel - > res : " + JSON.stringify(res))
                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                         } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                         }
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        console.log("Rfs outright - Invalid channel -> rfsSubscriptionResponses received")
                        console.log("====rfsSubscriptionResponses========"+ JSON.stringify(rfsSubscriptionResponses))
                        done()
                    } else if (res.rfsWithdrawAck) {
                        console.log("Rfs outright - Invalid channel -> rfsWithdrawAck received")

                    } else if (res.rfsResponses) {
                        rfsResponses = res.rfsResponses[0]

                    }
                }

            });

            it("Invalid channel", function () {
                refData = rfsSubscriptionResponses.refData
                console.log("============refData===="+JSON.stringify(refData))
                console.log("============refData===="+JSON.stringify(refData.instrument))

                 assert.equal('"EUR/USD"', JSON.stringify(refData.instrument))
                 assert.exists(JSON.stringify(rfsSubscriptionResponses.refData))
                 assert.exists(JSON.stringify(rfsSubscriptionResponses.rfsMessage))
                 assert.exists(JSON.stringify(rfsSubscriptionResponses.rfsMessage.eventDetails))
                 assert.equal('"OK"', JSON.stringify(rfsSubscriptionResponses.status))
           });
      });

        describe("without channel tag", function () {
       // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
             let dealtAmt = '1000000'

             before(function (done) {
                 console.log('*************************** rfs outright Negative TC - without channel tag ************************** ' + new Date());
                 reqId = "RFS_OR_WithoutChannel_"+tmpReqId
                 console.log("Rfs outright - without channel tag -> reqId = " + reqId )
                 var subrequests = [{
                              symbol : 'EUR/USD',
                              amount : "1000000.0",
                              dealtCurrency : "EUR",
                              expiry: rfsData.expiry,
                              nearValueDate : rfsData.nlTenor,
                              fixingDate : "" ,
                              side : rfsData.sideType2Way,
                              priceType : rfsData.priceTypeOR,
                              customerAccount : rfsData.customerAccount,
                              customerOrg: rfsData.customerOrg,
                              priceViewType: rfsData.aggregatedView,
                              depth : rfsData.depth,
                              providers: rfsData.providers,
                              clOrderId: reqId
                  }]
                 var wsreq = { rfsSubscriptions : subrequests }
                 console.log("Rfs outright - without channel tag - > rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                 connection.send(JSON.stringify(wsreq));
                 i = 1

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - without channel tag - > res : " + JSON.stringify(res))

                    if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                    }else if (res.rfsRates) {
                         rfsRates = res.rfsRates[0]
                         if(i < 2 && rfsRates.status === "A") {
                            rfsActiveQuote = rfsRates
                            i= i + 1
                            systemReqId = rfsRates.requestId
                         } else if (rfsRates.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                         } else if (i === 2) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                         }
                    } else if (res.rfsResponses) {
                        rfsResponses = res.rfsResponses[0]
                        done()
                    }
                }
            });

//Rfs outright - without channel tag - > res.rfsSubscriptionResponses received{"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/23 08:25:55","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"93","status":"ERROR","errorCode":"channel:may not be null"}
//Rfs outright - without channel tag - > res : {"rfsRates":[{"requestId":"G4796976d3188c25d73444f90","priceType":"Outright",
//"effectiveTime":0,"symbol":"EUR/USD","ttl":117,"dealtCurrency":"EUR","status":"A","nearValueDate":"2023-06-27",
//"bids":[{"legType":0,"quoteId":"G-4796976d4-188c25d9a0c-NTFXA-26-Spoorthy-NTFX-1686888094288","type":"BID",
//"dealtAmount":1000000,"settledAmount":1053690,"provider":"NTFX","rate":1.05369,"spotRate":1.05359,"forwardPoint":0.0001,"midRate":0}],
//"offers":[{"legType":0,"quoteId":"G-4796976d4-188c25d9a0c-NTFXA-26-Spoorthy-NTFX-1686888094288","type":"OFFER","dealtAmount":1000000,"settledAmount":1055110,"provider":"NTFX","rate":1.05511,"spotRate":1.05491,"forwardPoint":0.0002,"midRate":0}],"mids":[]}]}
            it("without channel tag", function () {
            //it will be REQUEST_WITHDRAWN if rates are received or REQUEST_EXPIRED if rates are not received
                assert.exists(JSON.stringify(rfsResponses.requestId))
                assert.exists(JSON.stringify(rfsResponses.rfsMessage))
                assert.exists(JSON.stringify(rfsResponses.clOrderId))
            });

       });


    });
};

rfsOutrightNegativeTC();
