    const assert = require('chai').assert
    const expect = require('chai').expect
    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let reqId
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let rfsFwdFwdNegativeTC = function(){

        describe("RFS FwdFwd Negative scenario ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });

            //{ "rfsSubscriptions" : [ { "symbol": "EUR/USD", "amount": "1000000.0", "dealtCurrency": "EUR", "expiry": 15, "nearValueDate": "1W", "farDealtAmount" : "1000000.0","farValueDate" : "2W", "fixingDate" : "" , "farFixingDate" : "", "side": "BUY", "priceType": "Swap", "customerAccount": "pfOrg", "customerOrg": "pfOrg", "priceViewType": 1, "depth": 2, "channel": "DNET/RFS/BB", "providers": ["NTFX","MSFX","SG","SUCD","UBS","WFNA"], "clOrderId": "view1MultiLP2" } ] }

            describe("Invalid CP ", function () {
                let dealtAmt = '1,000,000.00'

                // rfsFwdFwdSubscriptions: request : {"rfsSubscriptions":[{"symbol":"ABC/ACB","amount":"1000000.0","dealtCurrency":"EUR","expiry":10,"nearValueDate":"1W","farDealtAmount":"1000000.0","farValueDate":"2W","fixingDate":"","farFixingDate":"","side":"TWO_WAY","priceType":"FwdFwd","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":17}]}
                //{"rfsSubscriptionAck":[{"request":{"symbol":"ABC/ACB","amount":"1000000.0","dealtCurrency":"EUR","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"expiry":10,"side":"TWO_WAY","priceType":"FwdFwd","channel":"DNET/RFS/BB","customerOrg":"pfOrg","customerAccount":"pfOrg","farDealtAmount":"1000000.0","nearValueDate":"1W","farValueDate":"2W","fixingDate":"","farFixingDate":"","clOrderId":"17","priceViewType":1,"blockTrade":false,"intentToClear":false,"depth":5,"mtf":false,"enableCustomerSpreads":false,"isManualRequest":false},"status":"received"}]}
                //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/10/14 16:46:42","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB. Near leg: 2-Way 1,000,000.00. Value Date 1W. Far leg: 2-Way 1,000,000.00. Value Date 2W. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}
                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid CP ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid CP - > reqId = " + reqId)
                    var subrequests = [{
                                 symbol : 'ABC/ACB',
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid CP - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Invalid CP - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - Invalid CP - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Invalid CP - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });
//{"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,"clOrderId":"73","status":"ERROR","errorCode":"RequestValidationError.InvalidCurrency"}]}

                it("Invalid CP", function () {
                // rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/10/14 16:46:42","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB. Near leg: 2-Way 1,000,000.00. Value Date 1W. Far leg: 2-Way 1,000,000.00. Value Date 2W. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}
                //RFS FwdFwd - Invalid CP - > rfsSubscriptionResponses {"expiryTimeInSeconds":0,"clOrderId":"51","status":"ERROR","errorCode":"RequestValidationError.InvalidCurrency"}
                    console.log("RFS FwdFwd - Invalid CP - > rfsSubscriptionAck_Request : " + rfsSubscriptionAck)
                    console.log("RFS FwdFwd - Invalid CP - > rfsSubscriptionResponses " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    //assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventTime))
                    //assert.equal('"RFS Failed"', JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventName))
                    //expect(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for ABC/ACB. Near leg: 2-Way 1,000,000.00. Value Date 1W. Far leg: 2-Way 1,000,000.00. Value Date 2W. RFS submission failed');
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    //assert.equal('"INCORRECT_REQUEST_PARAMS"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                    assert.equal('"RequestValidationError.InvalidCurrency"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
              });

            });

            describe("Invalid Amt ", function () {
            //rfsFwdFwdSubscriptions: request : {"rfsSubscriptions":[{"symbol":"EUR/USD","amount":"abc","dealtCurrency":"EUR","expiry":10,"nearValueDate":"1W","farDealtAmount":"1000000.0","farValueDate":"2W","fixingDate":"","farFixingDate":"","side":"TWO_WAY","priceType":"FwdFwd","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":90}]}
            //{"rfsSubscriptionAck":[{"request":{"symbol":"EUR/USD","amount":"abc","dealtCurrency":"EUR","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"expiry":10,"side":"TWO_WAY","priceType":"FwdFwd","channel":"DNET/RFS/BB","customerOrg":"pfOrg","customerAccount":"pfOrg","farDealtAmount":"1000000.0","nearValueDate":"1W","farValueDate":"2W","fixingDate":"","farFixingDate":"","clOrderId":"90","priceViewType":1,"blockTrade":false,"intentToClear":false,"depth":5,"mtf":false,"enableCustomerSpreads":false,"isManualRequest":false},"status":"received"}]}
            //{"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,"clOrderId":"90","status":"ERROR"}]}
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid Amt ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid Amt - > reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "abc",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid Amt - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Invalid Amt - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - Invalid Amt - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Invalid Amt - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                it("Invalid Amt", function () {
                    console.log("RFS FwdFwd - Invalid Amt - > rfsSubscriptionAck_Request : " + rfsSubscriptionAck)
                    console.log("RFS FwdFwd - Invalid Amt - > rfsSubscriptionResponses " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
              });

            });

            describe("Invalid dealtCurrency ", function () {
            //request : {"rfsSubscriptions":[{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"ABC","expiry":10,"nearValueDate":"1W","farDealtAmount":"1000000.0","farValueDate":"2W","fixingDate":"","farFixingDate":"","side":"TWO_WAY","priceType":"FwdFwd","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":95}]}
            //{"rfsSubscriptionAck":[{"request":{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"ABC","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"expiry":10,"side":"TWO_WAY","priceType":"FwdFwd","channel":"DNET/RFS/BB","customerOrg":"pfOrg","customerAccount":"pfOrg","farDealtAmount":"1000000.0","nearValueDate":"1W","farValueDate":"2W","fixingDate":"","farFixingDate":"","clOrderId":"95","priceViewType":1,"blockTrade":false,"intentToClear":false,"depth":5,"mtf":false,"enableCustomerSpreads":false,"isManualRequest":false},"status":"received"}]}
            //{"expiryTimeInSeconds":0,"clOrderId":"95","status":"ERROR"}

                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid dealtCurrency ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid dealtCurrency -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "ABC",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid dealtCurrency - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Invalid dealtCurrency - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - Invalid dealtCurrency - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Invalid dealtCurrency - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                it("Invalid dealtCurrency", function () {
                    console.log("RFS FwdFwd - Invalid dealtCurrency - > rfsSubscriptionAck_Request : " + rfsSubscriptionAck)
                    console.log("RFS FwdFwd - Invalid dealtCurrency - > rfsSubscriptionResponses : " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                });

            });

            describe("Invalid nearValueDate ", function () {
            //request : {"rfsSubscriptions":[{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"EUR","expiry":10,"nearValueDate":"XX","farDealtAmount":"1000000.0","farValueDate":"2W","fixingDate":"","farFixingDate":"","side":"TWO_WAY","priceType":"FwdFwd","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":31}]}
            // {"rfsSubscriptionAck":[{"request":{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"EUR","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"expiry":10,"side":"TWO_WAY","priceType":"FwdFwd","channel":"DNET/RFS/BB","customerOrg":"pfOrg","customerAccount":"pfOrg","farDealtAmount":"1000000.0","nearValueDate":"XX","farValueDate":"2W","fixingDate":"","farFixingDate":"","clOrderId":"31","priceViewType":1,"blockTrade":false,"intentToClear":false,"depth":5,"mtf":false,"enableCustomerSpreads":false,"isManualRequest":false},"status":"received"}]}
            // {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/10/14 17:13:19","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD. Near leg: 2-Way 1,000,000.00. Value Date XX. Far leg: 2-Way 1,000,000.00. Value Date 2W. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"31","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid nearValueDate ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid nearValueDate : rate - reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "XX",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid nearValueDate -> rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Invalid nearValueDate -> res   : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("RFS FwdFwd - Invalid nearValueDate -> waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS FwdFwd - Invalid nearValueDate -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("systemReqId : " + systemReqId)
                                connection.send('{"RFS FwdFwd - Invalid nearValueDate -> rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS FwdFwd - Invalid nearValueDate -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS FwdFwd - Invalid nearValueDate -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Without nearValueDate - > res.rfsSubscriptionResponses : " + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                //{"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,"clOrderId":"46","status":"ERROR","errorCode":"RequestValidationError.ValueDateNotSpecified"}]}
//                it("Invalid nearValueDate", function () {
//                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds), "ExpiryTime is not matching")
//                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
//                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
//                    assert.equal('"RequestValidationError.ValueDateNotSpecified"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
//               });
                it("Invalid nearValueDate", function () {
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds), "ExpiryTime is not matching")
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventTime))
                    assert.equal('"RFS Failed"', JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventName))
                    expect(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for EUR/USD. Near leg: 2-Way 1,000,000.00. Value Date XX. Far leg: 2-Way 1,000,000.00. Value Date 2W. RFS submission failed');
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"INCORRECT_REQUEST_PARAMS"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
               });
            });

            describe("Without nearValueDate", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Without nearValueDate ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Without nearValueDate : rate - reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
//                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Without nearValueDate -> rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Without nearValueDate - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - Without nearValueDate - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Without nearValueDate - > res.rfsSubscriptionResponses : " + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
                it("Without nearValueDate", function () {
                    console.log("RFS FwdFwd - Without nearValueDate - > rfsSubscriptionAck_Request : " + rfsSubscriptionAck)
                    console.log("RFS FwdFwd - Without nearValueDate - > rfsSubscriptionResponses : " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    //assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventTime))
                    //assert.equal('"RFS Failed"', JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventName))
                    //expect(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for EUR/USD. Near leg: 2-Way 1,000,000.00. Value Date null. Far leg: 2-Way 1,000,000.00. Value Date 2W. RFS submission failed');
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    //assert.equal('"Request.Validation.Tenor/ValueDate.Missing"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                    assert.equal('"RequestValidationError.ValueDateNotSpecified"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
               });
            });

            describe("Without fixingDate", function () {
//  RFS works fine without fixing date too.
                let dealtAmt =  '1000000'  ///'1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Without fixingDate ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Without fixingDate : rate - reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
  								 farDealtAmount : "1000000.0",
  								 farValueDate : "2W",
                                 fixingDate : "" ,
  								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Without fixingDate -> rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("RFS FwdFwd - Without fixingDate -> res   : " + JSON.stringify(res))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                                done()
                            } else if (rate.status === "I") {
                                console.log("RFS FwdFwd - Without fixingDate -> waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS FwdFwd - Without fixingDate -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("systemReqId : " + systemReqId)
                                connection.send('{"RFS FwdFwd - Without fixingDate -> rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS FwdFwd - Without fixingDate -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS FwdFwd - Without fixingDate -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

                //No failed message, this works,,
                //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
                it("Without fixingDate", function () {
                     console.log("RFS FwdFwd - Without fixingDate - > rfsSubscriptionResponses : " + rfsActiveQuote)
                     let bidsArray = rfsActiveQuote.bids
                     let bidRate = bidsArray[0]
                     let offerArray = rfsActiveQuote.offers
                     let offerRate = offerArray[0]
                     assert.exists(rfsActiveQuote.requestId)
                     assert.exists(rfsActiveQuote.effectiveTime)
                     assert.exists(rfsActiveQuote.ttl)
                     // bid rate validation
                     assert.exists(bidRate.quoteId)
                     assert.exists(bidRate.settledAmount)
                     assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                     assert.exists(bidRate.provider)
                     assert.isNotNull(bidRate.provider)
                     assert.exists(bidRate.rate)
                     assert.notEqual("0",bidRate.rate, "rate is zero")
                     assert.exists(bidRate.spotRate)
                     assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                     assert.exists(bidRate.forwardPoint)
                     assert.exists(bidRate.midRate)
                     // offer rate validation
                     assert.exists(offerRate.quoteId)
                     assert.exists(offerRate.settledAmount)
                     assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                     assert.exists(offerRate.provider)
                     assert.isNotNull(offerRate.provider)
                     assert.exists(offerRate.rate)
                     assert.notEqual("0",offerRate.rate, "rate is zero")
                     assert.exists(offerRate.spotRate)
                     assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                     assert.exists(offerRate.forwardPoint)
                     assert.exists(offerRate.midRate)
                     assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                     assert.equal(rfsData.priceTypeFwdFwd, rfsActiveQuote.priceType)
                     assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                     assert.equal('A', rfsActiveQuote.status)
                     assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                     assert.equal(dealtAmt, bidRate.dealtAmount)
                     assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                     assert.equal(dealtAmt, offerRate.dealtAmount)
               });
            });

            describe("Invalid side ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid side ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid side -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : "ONE_WAY",
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid side - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Invalid side - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - Invalid side - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Invalid side - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        } else if (res.errors) {
                            errors = res.errors
                            console.log("errors : "+ JSON.stringify(errors))
                            done()
                        }
                    }
                });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                it("Invalid Side", function () {
                    console.log("RFS FwdFwd - Invalid side - > errors = " + JSON.stringify(errors))
                    assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                    assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                });
            });

            describe("Invalid priceType ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid priceType ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid priceType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : "xspot",
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid priceType - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Invalid priceType - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - Invalid priceType - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Invalid priceType - > res.rfsSubscriptionResponses : " + rfsSubscriptionResponses)
                            //done()
                        } else if (res.errors) {
                            errors = res.errors
                            console.log("errors : "+ JSON.stringify(errors))
                            done()
                        }
                    }
                });

                //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                it("Invalid priceType", function () {
                    console.log("RFS FwdFwd - Invalid priceType - > errors : " + JSON.stringify(errors))
                    assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                    assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                });
            });

            describe("Invalid customerAccount ", function () {
          // AP-10414 -> It should get rejected, but working fine. TC will pass now since valid assertions are not added yet
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid customerAccount ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid customerAccount -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : "abc",
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid customerAccount - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Invalid customerAccount - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - Invalid customerAccount - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Invalid customerAccount - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                it("Invalid customerAccount", function () {
                    console.log("RFS FwdFwd - Invalid customerAccount - > rfsSubscriptionAck_Request : " + rfsSubscriptionAck)
                    console.log("RFS FwdFwd - Invalid customerAccount - > rfsSubscriptionResponses " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.InvalidAccount"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
              });

            });

         describe("Invalid customerOrg ", function () {
          // AP-10414 -> It should get rejected, but working fine. TC will pass now since valid assertions are not added yet
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid customerAccount ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid customerOrg -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount :  rfsData.customerAccount,
                                 customerOrg: "invalid",
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid customerOrg - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - Invalid customerOrg - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - Invalid customerOrg - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - Invalid customerOrg - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                it("Invalid customerAccount", function () {
                    console.log("RFS FwdFwd - Invalid customerOrg - > rfsSubscriptionAck_Request : " + rfsSubscriptionAck)
                    console.log("RFS FwdFwd - Invalid customerOrg - > rfsSubscriptionResponses " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.InvalidOrg"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
              });

            });


            describe("Invalid priceViewType ", function () {
           // priceViewType =1 -> aggregated view, any other number (0 or other positive integers are considered as non aggregated view where each LP quote is sent separately
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid priceViewType ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid priceViewType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: 8,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid priceViewType - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("RFS FwdFwd - Invalid priceViewType ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("RFS FwdFwd - Invalid priceViewType ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS FwdFwd - Invalid priceViewType -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("RFS FwdFwd - Invalid priceViewType -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS FwdFwd - Invalid priceViewType -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS FwdFwd - Invalid priceViewType -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }

                });

                it("Invalid priceViewType", function () {
                     console.log("RFS FwdFwd - Invalid priceViewType -> quote : " + JSON.stringify(rfsActiveQuote))
                     let bidsArray = rfsActiveQuote.bids
                     let bidRate = bidsArray[0]
                     let offerArray = rfsActiveQuote.offers
                     let offerRate = offerArray[0]
                     // bid rate validation
                     assert.exists(bidRate.rate)
                     assert.notEqual("0",bidRate.rate, "rate is zero")
                     // offer rate validation
                     assert.exists(offerRate.rate)
                     assert.notEqual("0",offerRate.rate, "rate is zero")
                });

           });

            describe("Negative priceViewType ", function () {
           // priceViewType =1 -> aggregated view, any other number (0 or other positive/Negative integers are considered as non aggregated view where each LP quote is sent separately
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Negative priceViewType ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Negative priceViewType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: "-2",
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Negative priceViewType - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("RFS FwdFwd - Negative priceViewType ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                                done()
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS FwdFwd - Negative priceViewType -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("RFS FwdFwd - Negative priceViewType -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS FwdFwd - Negative priceViewType -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS FwdFwd - Negative priceViewType -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }

                });

                it("Negative priceViewType", function () {
                     console.log("RFS FwdFwd - Negative priceViewType -> quote : " + JSON.stringify(rfsActiveQuote))
                     let bidsArray = rfsActiveQuote.bids
                     let bidRate = bidsArray[0]
                     let offerArray = rfsActiveQuote.offers
                     let offerRate = offerArray[0]
                     // bid rate validation
                     assert.exists(bidRate.rate)
                     assert.notEqual("0",bidRate.rate, "rate is zero")
                     // offer rate validation
                     assert.exists(offerRate.rate)
                     assert.notEqual("0",offerRate.rate, "rate is zero")
                });

          });

            describe("Invalid depth ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid depth ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid depth -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: "a",
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid depth - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                         res = JSON.parse(e.data)
                         console.log("RFS FwdFwd - Invalid depth - > res : " + JSON.stringify(res))

                         if (res.rfsSubscriptionAck) {
                             rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                             console.log("RFS FwdFwd - Invalid depth - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                         } else if (res.rfsSubscriptionResponses) {
                             rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                             console.log("RFS FwdFwd - Invalid depth - > res.rfsSubscriptionResponses : " + rfsSubscriptionResponses)
                             //done()
                         } else if (res.errors) {
                             errors = res.errors
                             console.log("errors : "+ JSON.stringify(errors))
                             done()
                         }
                     }
                 });

 //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                 it("Invalid Depth", function () {
                     console.log("RFS FwdFwd - Invalid depth - > errors : " + JSON.stringify(errors))
                     assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                     assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                 });
            });


            describe("Invalid providers ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid providers ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid providers -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: ["ABC"],
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid providers - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                     connection.onmessage = (e) => {
                          res = JSON.parse(e.data)
                          console.log("RFS FwdFwd - Invalid providers - > res : " + JSON.stringify(res))

                          if (res.rfsSubscriptionAck) {
                              rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                              console.log("RFS FwdFwd - Invalid providers - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                          } else if (res.rfsSubscriptionResponses) {
                              rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses)
                              console.log("RFS FwdFwd - Invalid providers - > res.rfsSubscriptionResponses : " + rfsSubscriptionResponses)
                              done()
                          } else if (res.errors) {
                              errors = res.errors
                              console.log("errors : "+ JSON.stringify(errors))
                              //done()
                          }
                      }
                  });

                  it("Invalid Providers", function () {
                      console.log("=============RFS FwdFwd - Invalid providers - > rfsSubscriptionResponses = " + JSON.stringify(rfsSubscriptionResponses))
                    assert.equal("0", JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                      assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventTime))
                      assert.equal('"RFS Failed"', JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventName))
                      expect(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for EUR/USD. Near leg: 2-Way 1,000,000.00. Value Date 1W. Far leg: 2-Way 1,000,000.00. Value Date 2W. RFS submission failed for "');
                      assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                      assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].requestId))
                      assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                      assert.equal('"INTERNAL_SERVER_ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                  });
               });


            describe("Invalid channel ", function () {
          // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS FwdFwd Negative TC - Invalid channel ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS FwdFwd - Invalid channel -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : "abc",
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS FwdFwd - Invalid channel - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("RFS FwdFwd - Invalid channel ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                                //done()
                            } else if (rate.status === "I") {
                                console.log("RFS FwdFwd - Invalid channel ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS FwdFwd - Invalid channel -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("RFS FwdFwd - Invalid channel -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS FwdFwd - Invalid channel -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS FwdFwd - Invalid channel -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }

                });

                it("Invalid channel", function () {
                     console.log("RFS FwdFwd - Invalid channel -> quote : " + JSON.stringify(rfsActiveQuote))
                     let bidsArray = rfsActiveQuote.bids
                     let bidRate = bidsArray[0]
                     let offerArray = rfsActiveQuote.offers
                     let offerRate = offerArray[0]
                     // bid rate validation
                     assert.exists(bidRate.rate)
                     assert.notEqual("0",bidRate.rate, "rate is zero")
                     // offer rate validation
                     assert.exists(offerRate.rate)
                     assert.notEqual("0",offerRate.rate, "rate is zero")
                });
          });

            describe("positive scenario - channel is getting ignored here - without channel tag", function () {
           // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
                 let dealtAmt = '1,000,000.00'

                 before(function (done) {
                     console.log('*************************** RFS FwdFwd Negative TC - without channel tag ************************** ' + new Date());
                     reqId = Math.floor(Math.random() * 100)
                     console.log("RFS FwdFwd - without channel tag -> reqId = " + reqId )
                     var subrequests = [{
                                  symbol : 'EUR/USD',
                                  amount : "1000000.0",
                                  dealtCurrency : "EUR",
                                  expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                  side : rfsData.sideType2Way,
                                  priceType : rfsData.priceTypeFwdFwd,
                                  customerAccount : rfsData.customerAccount,
                                  customerOrg: rfsData.customerOrg,
                                  priceViewType: rfsData.aggregatedView,
                                  depth: 5,
                                  providers: rfsData.providers,
                                  clOrderId: parseInt(reqId)
                      }]
                     var wsreq = { rfsSubscriptions : subrequests }
                     console.log("RFS FwdFwd - without channel tag - > rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                     connection.send(JSON.stringify(wsreq));
                     i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("RFS FwdFwd - without channel tag - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("RFS FwdFwd - without channel tag - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("RFS FwdFwd - without channel tag - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

//No error response received
//RFS FwdFwd - without channel tag - > res.rfsSubscriptionResponses received{"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/23 08:25:55","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"93","status":"ERROR","errorCode":"channel:may not be null"}
                it("without channel tag", function () {
                    console.log("RFS FwdFwd - without channel tag - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('120', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventTime))
                    assert.equal('"RFS Submitted"', JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventName))
                    //expect(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventDetails)).to.have.string('"RFS Submitted for EUR/USD. Near leg:2-Way 1,000,000.00. Value Date 1W. Far leg: 2-Way 1,000,000.00. Value Date 2W. RFS sent to "');
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"OK"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    //assert.equal('"channel:may not be null"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
              });

           });

        });
    };

rfsFwdFwdNegativeTC();
