    const assert = require('chai').assert
    const expect = require('chai').expect
    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let reqId
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let rfsNDFSwapTC = function(){

        describe("rfs NDF-Swap ", function () {

            before(function (done) {
                wsconnect(done)
            });

            after(function () {
                connection.close()
            });


            describe("One-Way-Bid Rate test ", function () {
                let dealtAmt = '1000000'; //'1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF-Swap - One-Way-Bid Rate test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("rfs NDF-Swap - One-Way-Bid Rate test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "3W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeNdfSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("rfs NDF-Swap - One-Way-Bid Rate test  ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("rfs NDF-Swap - One-Way-Bid Rate test  ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("rfs NDF-Swap - One-Way-Bid Rate test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("rfs NDF-Swap - One-Way-Bid Rate test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("rfs NDF-Swap - RateTest -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("rfs NDF-Swap - One-Way-Bid Rate test -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("rfs NDF-Swap - One-Way-Bid Rate test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

// quote : {"requestId":"G4796976d517c49e558edb33","priceType":"Swap","effectiveTime":0,"symbol":"USD/INR","ttl":118,"dealtCurrency":"USD","status":"A","nearValueDate":"10/06/2021","farValueDate":"10/13/2021","bids":[{"legType":0,"quoteId":"G-4796976cf-17c49e560f8-NTFXA-45-pfOrg-NTFX-1633327341822","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,310.00","provider":"NTFX","rate":1.18631,"spotRate":1.18631,"forwardPoint":0,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c49e56986-SGR-19-pfOrg-SG-1633327344013","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,240.00","provider":"SG","rate":1.18624,"spotRate":1.18624,"forwardPoint":0,"midRate":0}],"offers":[{"legType":1,"quoteId":"G-4796976cf-17c49e560f8-NTFXA-45-pfOrg-NTFX-1633327341822","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,210.00","provider":"NTFX","rate":1.18621,"spotRate":1.18631,"forwardPoint":-0.0001,"midRate":0},{"legType":1,"quoteId":"G-4796976cf-17c49e56986-SGR-19-pfOrg-SG-1633327344013","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,140.00","provider":"SG","rate":1.18614,"spotRate":1.18624,"forwardPoint":-0.0001,"midRate":0}],"mids":[]}

               it("Rate test", function () {
                    console.log("rfs NDF-Swap - One-Way-Bid Rate test -> quote : " + JSON.stringify(rfsActiveQuote))
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")
                    assert.exists(rfsActiveQuote.farValueDate, "farValueDate doesnt exist")

                    //assert.lengthOf(rfsActiveQuote.bids,0)
                    //assert.lengthOf(rfsActiveQuote.mids,0)
                    //assert.lengthOf.notEqual(rfsActiveQuote.offers,0,"offer size is zero")

                    // bid rate
                    assert.equal('0',bidRate.legType, "legtype is not zero")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not BID")
                    assert.equal(dealtAmt,bidRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(bidRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    //assert.equal('0',bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")


                    // offer rate
                    assert.equal('1',offerRate.legType, "legtype is not 1")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dealtAmt,offerRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(offerRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    //assert.notEqual('0',offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")
               });


            });

            describe("Matched Swap - 1W-3W-Two-Way Rate test ", function () {
            //{ "rfsSubscriptions" : [ { "clOrderId": "SwapBaseBuyQuote", "symbol": "USD/INR", "amount": "1000000.0", "dealtCurrency": "USD", "expiry": 10, "nearValueDate": "1W", "farDealtAmount" : "1000000.0","farValueDate" : "1M", "fixingDate" : "" , "farFixingDate" : "", "side": "BUY", "priceType": "Swap", "customerAccount": "pfOrgLE", "customerOrg": "pfOrg", "priceViewType": 0, "depth": 1, "channel": "DNET/RFS/BB", "providers": ["JPM"] } ] }
            //// rfsSwapSubscriptions: request : {"rfsSubscriptions":[{"symbol":"USD/INR","amount":"1000000.0","dealtCurrency":"USD","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"TWO_WAY","priceType":"Swap","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":70}]}
                            let dealtAmt = '1000000'; //'1,000,000.00'

                            before(function (done) {
                                console.log('*************************** Matched Swap - 1W-3W rfs NDF-Swap - Two-Way Rate test ************************** ' + new Date());
                                reqId = Math.floor(Math.random() * 100)
                                console.log("rfs NDF-Swap - Two-Way RateTest -> reqId = " + reqId)
                                var subrequests = [{
                                             symbol : rfsData.symbolNdf,
                                             amount : "1000000.0",
                                             dealtCurrency : rfsData.baseCcyNdf,
                                             expiry: rfsData.expiry,
                                             nearValueDate : "1W",
            								 farDealtAmount : "1000000.0",
            								 farValueDate : "3W",
                                             fixingDate : "" ,
            								 farFixingDate : "",
                                             side : rfsData.sideType2Way,
                                             priceType : rfsData.priceTypeNdfSwap,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: rfsData.providers,
                                             clOrderId: parseInt(reqId)
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("rfs NDF-Swap - Two-Way RateTest ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                                connection.send(JSON.stringify(wsreq));
                                i = 1

                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("rfs NDF-Swap - Two-Way RateTest -> res : \n" + JSON.stringify(res))
                                    if (res.rfsRates) {
                                        rate = res.rfsRates[0]
                                        if(i < 3 && rate.status === "A") {
                                            console.log("rfs NDF-Swap - Two-Way RateTest ->  rate response : \n" + JSON.stringify(rate))
                                            rfsActiveQuote = rate
                                            i= i + 1
                                            systemReqId = rate.requestId
                                        } else if (rate.status === "I") {
                                            console.log("rfs NDF-Swap - Two-Way RateTest ->waiting for Inactive quote \n")
                                            rfsInactiveQuote = res.rfsRates[0]
                                            console.log("rfs NDF-Swap - Two-Way RateTest -> rfsInactiveQuote : \n" + JSON.stringify(rfsInactiveQuote))
                                         } else if (i ===3) {
                                            console.log("rfs NDF-Swap - Two-Way RateTest -> systemReqId : \n" + systemReqId)
                                            console.log("rfs NDF-Swap - Two-Way RateTest -> rfsWithdrawRequest : \n" + '{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                            i++
                                         }
                                    } else if (res.rfsWithdrawAck) {
                                        console.log("rfs NDF-Swap - Two-Way RateTest -> rfsWithdrawAck received")
                                    } else if (res.rfsResponses) {
                                        rfsWithdrawResponse = res.rfsResponses[0]
                                        console.log("rfs NDF-Swap - Two-Way RateTest -> rfsWithdrawResponse : \n" + JSON.stringify(rfsWithdrawResponse))
                                        done()
                                    }
                                }
                            //done()
                            });


                            // quote :  rate response : {"requestId":"G4796976d517c3babfdd9223a","priceType":"Swap","effectiveTime":0,"symbol":"USD/INR","ttl":10,"dealtCurrency":"USD","status":"A","nearValueDate":"10/05/2021","farValueDate":"10/12/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0},{"legType":1,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,187,103.00","provider":"WFNA","rate":1.187103,"spotRate":1.18655,"forwardPoint":0.000553,"midRate":0}],"offers":[{"legType":1,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,187,193.00","provider":"WFNA","rate":1.187193,"spotRate":1.18664,"forwardPoint":0.000553,"midRate":0},{"legType":0,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00","provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}

                            it("Matched Swap - 1W-3W - Rate test", function () {
                                console.log("rfs NDF-Swap - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                                let bidsArray = rfsActiveQuote.bids
                                let bidRate = bidsArray[0]
                                let offersArray = rfsActiveQuote.offers
                                let offerRate = offersArray[0]
            //  Take quote from just one provider for validoting quote from bids & offers
                                let GUID = bidRate.quoteId
                                let tmpBidArray = new Array()
                                let j = 0;
                                for (i=0; i<bidsArray.length; i++) {
                                    if(GUID == bidsArray[i].quoteId) {
                                    tmpBidArray[j] = (bidsArray[i])
                                    j++
                                    }
                                }
                                // validate NL & FL for 2 way for the quote from the selected provider
                                for (i=0; i<tmpBidArray.length; i++) {
                                    console.log ("===tmpArray=="  +i+ "======" + JSON.stringify(tmpBidArray[i]))
                                    if (tmpBidArray[i].legType == 0) {
                                        assert.equal(tmpBidArray[i].dealtAmount, dealtAmt)
                                        assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                                    } else if (tmpBidArray[i].legType == 1) {
                                        assert.equal(tmpBidArray[i].dealtAmount, dealtAmt)
                                        assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                                    }
                                }

                                j = 0;
                                let tmpOfferArray = new Array()
                                for (i=0; i<offersArray.length; i++) {
                                    if(GUID == offersArray[i].quoteId) {
                                    tmpOfferArray[j] = (offersArray[i])
                                    j++
                                    }
                                }

                                for (i=0; i<tmpOfferArray.length; i++) {
                                    console.log ("===tmpOfferArray=="  +i+ "======" + JSON.stringify(tmpOfferArray[i]))
                                    if (tmpOfferArray[i].legType == 0) {
                                        assert.equal(tmpOfferArray[i].dealtAmount, dealtAmt)
                                        assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                                    } else if (tmpOfferArray[i].legType == 1) {
                                        assert.equal(tmpOfferArray[i].dealtAmount, dealtAmt)
                                        assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                                    }
                                }

                                assert.equal(tmpBidArray.length,tmpOfferArray.length)

                                assert.exists(rfsActiveQuote.requestId)
            					assert.exists(rfsActiveQuote.nearValueDate)
            					assert.exists(rfsActiveQuote.farValueDate)
                                assert.exists(rfsActiveQuote.ttl)
            					assert.exists(rfsActiveQuote.mids)
                                // bid rate validation
                                assert.exists(bidRate.legType)
            					assert.exists(bidRate.quoteId)
                                assert.exists(bidRate.settledAmount)
                                assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                                assert.exists(bidRate.provider)
                                assert.isNotNull(bidRate.provider)
                                assert.exists(bidRate.rate)
                                assert.notEqual("0",bidRate.rate, "rate is zero")
                                assert.exists(bidRate.spotRate)
                                assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                                assert.exists(bidRate.forwardPoint)
                                assert.exists(bidRate.midRate)
                                // offer rate validation
                                assert.exists(offerRate.legType)
            					assert.exists(offerRate.quoteId)
                                assert.exists(offerRate.settledAmount)
                                assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                                assert.exists(offerRate.provider)
                                assert.isNotNull(offerRate.provider)
                                assert.exists(offerRate.rate)
                                assert.notEqual("0",offerRate.rate, "rate is zero")
                                assert.exists(offerRate.spotRate)
                                assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                                assert.exists(offerRate.forwardPoint)
                                assert.exists(offerRate.midRate)

                                assert.exists(rfsActiveQuote.effectiveTime)
                                assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                                assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                                assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                                assert.equal('A', rfsActiveQuote.status)

                                assert.equal('BID', bidRate.type)
                                assert.equal(dealtAmt, bidRate.dealtAmount)
                                assert.equal('OFFER', offerRate.type)
                                assert.equal(dealtAmt, offerRate.dealtAmount)

                           });

                        });

            describe("One-Way-Offer Rate test ", function () {
                let dealtAmt = '1000000'; //'1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF-Swap - One-Way-Offer Rate test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("rfs NDF-Swap - One-Way-Offer Rate test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "3W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeNdfSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("rfs NDF-Swap - One-Way-Offer Rate test  ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("rfs NDF-Swap - One-Way-Offer Rate test  ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("rfs NDF-Swap - One-Way-Offer Rate test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("rfs NDF-Swap - One-Way-Offer Rate test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("rfs NDF-Swap - One-Way-Offer Rate test -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("rfs NDF-Swap - One-Way-Offer Rate test -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("rfs NDF-Swap - One-Way-Offer Rate test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

                 it("Rate test", function () {
                    console.log("rfs NDF-Swap - One-Way-Offer Rate test -> quote : " + JSON.stringify(rfsActiveQuote))
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")
                    assert.exists(rfsActiveQuote.farValueDate, "farValueDate doesnt exist")

 //                   assert.lengthOf(rfsActiveQuote.offers,0)
//                    assert.lengthOf(rfsActiveQuote.mids,0)

                    // bid rate
                    assert.equal('1',bidRate.legType, "legtype is not 1")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not BID")
                    assert.equal(dealtAmt,bidRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(bidRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual('0',bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")


                    // offer rate
                    assert.equal('0',offerRate.legType, "legtype is not 0")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dealtAmt,offerRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(offerRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual('0',offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")


               });

            });


            describe("Withdraw response test ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF-Swap - Withdraw response test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("rfs NDF-Swap - Withdraw response test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: 50, //rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "3W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdfSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("rfs NDF-Swap - Withdraw response Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("rfs NDF-Swap - Withdraw response Test ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("rfs NDF-Swap - Withdraw response test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("rfs NDF-Swap - Withdraw response Test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("rfs NDF-Swap - Withdraw response Test -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("rfs NDF-Swap - Withdraw response Test -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("rfs NDF-Swap - Withdraw response Test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

                //rfsWithdrawResponse : {"requestId":"G4796976d517c27fc75c2221","rfsEvent":"REQUEST_WITHDRAWN","rfsMessage":{"eventTime":"2021/09/27 16:00:29","eventName":"RFS Withdrawn","eventDetails":"RFS Request Withdrawn for USD/INR with Dealt currency USD. 2-Way 1,000,000.00. Value Date 1W"},"clOrderId":"G4796976d517c27fc75c2221"}
                it("Withdraw request test", function () {
                    console.log("=====rfs NDF-Swap - Withdraw response Test -> withdraw response : " + JSON.stringify(rfsWithdrawResponse))
                    //rfsWithdrawResponse = res.rfsWithdrawAck
                    assert.exists(rfsWithdrawResponse.requestId)
                    assert.equal('REQUEST_WITHDRAWN', rfsWithdrawResponse.rfsEvent)
                    assert.exists(rfsWithdrawResponse.rfsMessage.eventTime)
                    assert.equal('RFS Withdrawn', rfsWithdrawResponse.rfsMessage.eventName)
                    expect(JSON.stringify(rfsWithdrawResponse.rfsMessage.eventDetails)).to.have.string('"RFS Request Withdrawn for USD/INR with Dealt currency USD. 2-Way 1,000,000.00/1,000,000.00. Value Date 1W 3W."');

                    assert.exists(rfsWithdrawResponse.clOrderId)
              });

            });

            describe("Matched swap - 1W-3W-Two-Way Rate TermCcy test ", function () {
           let dealtAmt = '1000000'; //'1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF-Swap - Two-Way Rate TermCcy test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("rfs NDF-Swap - Two-Way RateTest -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "3W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdfSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("rfs NDF-Swap - Two-Way RateTest ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1


                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("rfs NDF-Swap - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("rfs NDF-Swap - Two-Way RateTest ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("rfs NDF-Swap - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("rfs NDF-Swap - Two-Way RateTest -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("rfs NDF-Swap - Two-Way RateTest -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("rfs NDF-Swap - Two-Way RateTest -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });
                // quote :  rate response : {"requestId":"G4796976d517c3babfdd9223a","priceType":"Swap","effectiveTime":0,"symbol":"USD/INR","ttl":10,"dealtCurrency":"USD","status":"A","nearValueDate":"10/05/2021","farValueDate":"10/12/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0},{"legType":1,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,187,103.00","provider":"WFNA","rate":1.187103,"spotRate":1.18655,"forwardPoint":0.000553,"midRate":0}],"offers":[{"legType":1,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,187,193.00","provider":"WFNA","rate":1.187193,"spotRate":1.18664,"forwardPoint":0.000553,"midRate":0},{"legType":0,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00","provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}

                it("Matched swap - 1W-3W - Rate test", function (done) {
                    console.log("rfs NDF-Swap - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offersArray = rfsActiveQuote.offers
                    let offerRate = offersArray[0]

//  Take quote from just one provider for validoting quote from bids & offers
                    let GUID = bidRate.quoteId

                    let tmpBidArray = new Array()
                    let j = 0;
                    for (i=0; i<bidsArray.length; i++) {
                        if(GUID == bidsArray[i].quoteId) {
                        tmpBidArray[j] = (bidsArray[i])
                        j++
                        }
                    }
                    // validate NL & FL for 2 way for the quote from the selected provider
                    for (i=0; i<tmpBidArray.length; i++) {
                        console.log ("===tmpArray=="  +i+ "======" + JSON.stringify(tmpBidArray[i]))
                        if (tmpBidArray[i].legType == 0) {
                            assert.equal(tmpBidArray[i].dealtAmount, dealtAmt)
                            //assert.equal(tmpBidArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        } else if (tmpBidArray[i].legType == 1) {
                            assert.equal(tmpBidArray[i].dealtAmount, dealtAmt)
                            //assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    j = 0;
                    let tmpOfferArray = new Array()
                    for (i=0; i<offersArray.length; i++) {
                        if(GUID == offersArray[i].quoteId) {
                        tmpOfferArray[j] = (offersArray[i])
                        j++
                        }
                    }

                    for (i=0; i<tmpOfferArray.length; i++) {
                        console.log ("===tmpOfferArray=="  +i+ "======" + JSON.stringify(tmpOfferArray[i]))
                        if (tmpOfferArray[i].legType == 0) {
                            assert.equal(tmpOfferArray[i].dealtAmount, dealtAmt)
                            assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        } else if (tmpOfferArray[i].legType == 1) {
                            assert.equal(tmpOfferArray[i].dealtAmount, dealtAmt)
                            assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    assert.equal(tmpBidArray.length,tmpOfferArray.length)

                    assert.exists(rfsActiveQuote.requestId)
					assert.exists(rfsActiveQuote.nearValueDate)
					assert.exists(rfsActiveQuote.farValueDate)
                    assert.exists(rfsActiveQuote.ttl)
					assert.exists(rfsActiveQuote.mids)
                    // bid rate validation
                    assert.exists(bidRate.legType)
					assert.exists(bidRate.quoteId)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.exists(offerRate.legType)
					assert.exists(offerRate.quoteId)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.exists(offerRate.midRate)

                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

                    assert.equal('BID', bidRate.type)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.equal('OFFER', offerRate.type)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
                    done()
               });

            });

            describe("Mismatch Swap 1W-3W -Two-Way Rate test 1W-3W", function () {
                let NLDealtAmt = '1000000'; //'1,000,000.00'
				let FLDealtAmt = '2000000'; //'2,000,000.00'

				before(function (done) {
                    console.log('***************************Mismatch Swap 1W-3W - rfs Mismatch Swap - Two-Way Rate test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("rfs Mismatch Swap - Two-Way RateTest -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "2000000.0",
								 farValueDate : "3W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdfSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("rfs Mismatch Swap - Two-Way RateTest ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("rfs Mismatch Swap - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("rfs Mismatch Swap - Two-Way RateTest ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("rfs Mismatch Swap - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("rfs Mismatch Swap - Two-Way RateTest -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("rfs Mismatch Swap - Two-Way RateTest -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("rfs Mismatch Swap - Two-Way RateTest -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });
                // quote :  rate response : {"requestId":"G4796976d517c3babfdd9223a","priceType":"Swap","effectiveTime":0,"symbol":"USD/INR","ttl":10,"dealtCurrency":"USD","status":"A","nearValueDate":"10/05/2021","farValueDate":"10/12/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0},{"legType":1,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,187,103.00","provider":"WFNA","rate":1.187103,"spotRate":1.18655,"forwardPoint":0.000553,"midRate":0}],"offers":[{"legType":1,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,187,193.00","provider":"WFNA","rate":1.187193,"spotRate":1.18664,"forwardPoint":0.000553,"midRate":0},{"legType":0,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00","provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}

                it("Mismatch Swap 1W-3W -Rate test", function () {
                    console.log("Rfs Mismatch Swap - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[1]
                    let offersArray = rfsActiveQuote.offers
                    let offerRate = offersArray[0]

//  Take quote from just one provider for validoting quote from bids & offers
                    let GUID = bidRate.quoteId

                    let tmpBidArray = new Array()
                    let j = 0;
                    for (i=0; i<bidsArray.length; i++) {
                        if(GUID == bidsArray[i].quoteId) {
                        tmpBidArray[j] = (bidsArray[i])
                        j++
                        }
                    }
                    // validate NL & FL for 2 way for the quote from the selected provider
                    for (i=0; i<tmpBidArray.length; i++) {
                        console.log ("===tmpArray=="  +i+ "======" + JSON.stringify(tmpBidArray[i]))
                        if (tmpBidArray[i].legType == 0) {
                            assert.equal(tmpBidArray[i].dealtAmount, NLDealtAmt,"NLDealtAmt is not matching")
                            assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is  equal to 0")
                        } else if (tmpBidArray[i].legType == 1) {
                            assert.equal(tmpBidArray[i].dealtAmount, FLDealtAmt, "FLDealtAmt is not matching")
                            assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    j = 0;
                    let tmpOfferArray = new Array()
                    for (i=0; i<offersArray.length; i++) {
                        if(GUID == offersArray[i].quoteId) {
                        tmpOfferArray[j] = (offersArray[i])
                        j++
                        }
                    }

                    for (i=0; i<tmpOfferArray.length; i++) {
                        console.log ("===tmpOfferArray=="  +i+ "======" + JSON.stringify(tmpOfferArray[i]))
                        if (tmpOfferArray[i].legType == 0) {
                            assert.equal(tmpOfferArray[i].dealtAmount, NLDealtAmt, "NLDealtAmt is not matching")
                            assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        } else if (tmpOfferArray[i].legType == 1) {
                            assert.equal(tmpOfferArray[i].dealtAmount, FLDealtAmt, "FLDealtAmt is not matching")
                            assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    assert.equal(tmpBidArray.length,tmpOfferArray.length)

                    assert.exists(rfsActiveQuote.requestId)
					assert.exists(rfsActiveQuote.nearValueDate)
					assert.exists(rfsActiveQuote.farValueDate)
                    assert.exists(rfsActiveQuote.ttl)
					assert.exists(rfsActiveQuote.mids)
                    // bid rate validation

					assert.exists(bidRate.legType)
					assert.exists(bidRate.quoteId)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.exists(bidRate.midRate)

                    // offer rate validation
                    assert.exists(offerRate.legType)
					assert.exists(offerRate.quoteId)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.exists(offerRate.midRate)

                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                    assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

                    assert.equal('BID', bidRate.type)
                    assert.equal('OFFER', offerRate.type)

               });
            });


        });
    };

rfsNDFSwapTC();
