    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')

    //const login = require('../login').login
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let reqId = Math.floor(Math.random() * 100)
    let dltAmtInQuote = 1000000
    let flag

    // Login credentials should be for MDF enabled org
    // For marketdata scripts, org should be getting rates in MDF
    // Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let rfsSwapTC = function(){

        describe("RFS Swap ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });

            beforeEach(function () {
                console.log('about to run a test');
                systemReqId = ""
                flag = false
            });

            describe("Two-Way Rate test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - Two-Way Rate test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_TwoWay_Rate_" + reqId
                    console.log("Rfs Swap - Two-Way RateTest -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Two-Way RateTest ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    //systemReqId = ""

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Two-Way RateTest -> res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });

                it("Rate test", function () {
                    console.log("Rfs Swap - Two-Way RateTest -> quote : " )
                    console.log(rfsActiveQuote)
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offersArray = rfsActiveQuote.offers
                    let offerRate = offersArray[0]

//  Take quote from just one provider for validoting quote from bids & offers
                    let GUID = bidRate.quoteId

                    let tmpBidArray = new Array()
                    let j = 0;
                    for (i=0; i<bidsArray.length; i++) {
                        if(GUID == bidsArray[i].quoteId) {
                            tmpBidArray[j] = (bidsArray[i])
                            j++
                        }
                    }
                    // validate NL & FL for 2 way for the quote from the selected provider
                    for (i=0; i<tmpBidArray.length; i++) {
                        if (tmpBidArray[i].legType == 0) {
                            assert.equal(tmpBidArray[i].dealtAmount, "1000000")
                            assert.equal(tmpBidArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        } else if (tmpBidArray[i].legType == 1) {
                            assert.equal(tmpBidArray[i].dealtAmount, "1000000")
                            assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    j = 0;
                    let tmpOfferArray = new Array()
                    for (i=0; i<offersArray.length; i++) {
                        if(GUID == offersArray[i].quoteId) {
                            tmpOfferArray[j] = (offersArray[i])
                            j++
                        }
                    }

                    for (i=0; i<tmpOfferArray.length; i++) {
                        if (tmpOfferArray[i].legType == 0) {
                            assert.equal(tmpOfferArray[i].dealtAmount, "1000000")
                            assert.equal(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        } else if (tmpOfferArray[i].legType == 1) {
                            assert.equal(tmpOfferArray[i].dealtAmount, "1000000")
                            assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    assert.equal(tmpBidArray.length,tmpOfferArray.length)

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSwap, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

					assert.exists(rfsActiveQuote.nearValueDate)
					assert.exists(rfsActiveQuote.farValueDate)
					assert.exists(rfsActiveQuote.mids)
                    // bid rate validation
                    assert.exists(bidRate.legType)
					assert.exists(bidRate.quoteId)
                    assert.equal('BID', bidRate.type)
                    assert.equal(dltAmtInQuote, bidRate.dealtAmount)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.exists(offerRate.legType)
					assert.exists(offerRate.quoteId)
                    assert.equal('OFFER', offerRate.type)
                    assert.equal(dltAmtInQuote, offerRate.dealtAmount)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.exists(offerRate.midRate)
               });

            });

            describe("One-Way-Bid Rate test ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** Rfs Swap - One-Way-Bid Rate test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_OneWay_Bid_Rate_" + reqId
                    console.log("Rfs Swap - One-Way-Bid Rate test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                    }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - One-Way-Bid Rate test  ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - One-Way-Bid Rate test  ->  res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                             if (res.rfsRates[0].requestId === systemReqId)
                             {
                                rate = res.rfsRates[0]
                                if(i < 3 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===3) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                             }
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck
                        } else if (res.rfsResponses && res.rfsResponses[0].requestId === systemReqId) {
                                 rfsWithdrawResponse = res.rfsResponses[0]
                                 done()
                        } else if (res.rfsSubscriptionResponses ) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                       }
                    }
                });
 /*               after(function (done) {
                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                done()
                });
*/
               it("Rate test", function () {
                    console.log("Rfs Swap - One-Way-Bid Rate test -> quote : " )
                    console.log(rfsActiveQuote)

                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSwap, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")
                    assert.exists(rfsActiveQuote.farValueDate, "farValueDate doesnt exist")

                    //assert.lengthOf(rfsActiveQuote.bids,0)
                    //assert.lengthOf(rfsActiveQuote.mids,0)
                    //assert.lengthOf.notEqual(rfsActiveQuote.offers,0,"offer size is zero")

                    // bid rate
                    assert.equal('0',bidRate.legType, "legtype is not zero")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not BID")
                    assert.equal(dltAmtInQuote,bidRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(bidRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.equal('0',bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")


                    // offer rate
                    assert.equal('1',offerRate.legType, "legtype is not 1")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dltAmtInQuote,offerRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(offerRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual('0',offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")

               });

            });

            describe("One-Way-Offer Rate test ", function () {
                let dealtAmt = '1,000,000.00'
                before(function (done) {
                    console.log('*************************** rfs swap - One-Way-Offer Rate test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_OneWay_Offer_Rate_" + reqId
                    console.log("rfs swap - One-Way-Offer Rate test -> reqId = " + reqId)

                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("rfs swap - One-Way-Offer Rate test  ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("rfs swap - One-Way-Offer Rate test  ->  res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            if (res.rfsRates[0].requestId === systemReqId)
                            {
                                rate = res.rfsRates[0]
                                if(i < 3 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===3) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                            }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true

                        }
                    }
                });

                it("Rate test", function () {
                    console.log("rfs swap - One-Way-Offer Rate test -> quote : " )
                    console.log(rfsActiveQuote)

                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSwap, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearSettleDate doesnt exist")
                    assert.exists(rfsActiveQuote.farValueDate, "farValueDate doesnt exist")

 //                   assert.lengthOf(rfsActiveQuote.offers,0)
                    assert.lengthOf(rfsActiveQuote.mids,0)

                    // bid rate
                    assert.equal('1',bidRate.legType, "legtype is not 1")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not BID")
                    assert.equal(dltAmtInQuote,bidRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(bidRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual('0',bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")


                    // offer rate
                    assert.equal('0',offerRate.legType, "legtype is not 0")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dltAmtInQuote,offerRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(offerRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.equal('0',offerRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")


               });

            });

            describe("Withdraw response test ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - Withdraw response test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_Withdraw_" + reqId
                    console.log("Rfs Swap - Withdraw response test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: "60", // rfsData.expiry, give expiry lil longer to have enough time to withdraw
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Withdraw response Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Withdraw response test ->  res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 2 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            } else if (i ===2) {
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                            }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true

                        }
                    }
                });

                //rfsWithdrawResponse : {"requestId":"G4796976d517c27fc75c2221","rfsEvent":"REQUEST_WITHDRAWN","rfsMessage":{"eventTime":"2021/09/27 16:00:29","eventName":"RFS Withdrawn","eventDetails":"RFS Request Withdrawn for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date 1W"},"clOrderId":"G4796976d517c27fc75c2221"}
                it("Withdraw request test", function () {
                    console.log("Rfs Swap - Withdraw response Test -> withdraw response : ")
                     console.log(rfsWithdrawResponse)
                    //rfsWithdrawResponse = res.rfsWithdrawAck
                    assert.exists(rfsWithdrawResponse.requestId)
                    assert.equal('REQUEST_WITHDRAWN', rfsWithdrawResponse.rfsEvent)
                    assert.exists(rfsWithdrawResponse.rfsMessage.eventTime)
                    assert.equal('RFS Withdrawn', rfsWithdrawResponse.rfsMessage.eventName)
                    assert.equal(rfsWithdrawResponse.rfsMessage.eventDetails,'RFS Request Withdrawn for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00/1,000,000.00. Value Date SPOT 1W.');
                    assert.exists(rfsWithdrawResponse.clOrderId)
              });

            });

            describe("Mismatch Swap Two-Way Rate test ", function () {

                let NLDealtAmt = '1,000,000.00'
				let FLDealtAmt = '2,000,000.00'

				before(function (done) {
                    console.log('*************************** rfs Mismatch Swap - Two-Way Rate test ************************** ' + new Date());
                    tempReqId = "RFS_MismatchSwap_TwoWay_Rate_" + reqId
                    console.log("rfs Mismatch Swap - Two-Way RateTest -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "2000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("rfs Mismatch Swap - Two-Way RateTest ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("rfs Mismatch Swap - Two-Way RateTest ->  res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            if (res.rfsRates[0].requestId === systemReqId)
                            {
                                rate = res.rfsRates[0]
                                if(i < 3 && rate.status === "A") {
                                    console.log("rfs Mismatch Swap - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                    rfsActiveQuote = rate
                                    i= i + 1
                                } else if (rate.status === "I") {
                                    console.log("rfs Mismatch Swap - Two-Way RateTest ->waiting for Inactive quote")
                                    rfsInactiveQuote = res.rfsRates[0]
                                    console.log("rfs Mismatch Swap - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                                } else if (i ===3) {
                                    console.log("rfs Mismatch Swap - Two-Way RateTest -> systemReqId : " + systemReqId)
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                            }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });
                // quote :  rate response : {"requestId":"G4796976d517c3babfdd9223a","priceType":"Swap","effectiveTime":0,"symbol":"EUR/USD","ttl":10,"dealtCurrency":"EUR","status":"A","nearSettleDate":"10/05/2021","farSettleDate":"10/12/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0},{"legType":1,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,187,103.00","provider":"WFNA","rate":1.187103,"spotRate":1.18655,"forwardPoint":0.000553,"midRate":0}],"offers":[{"legType":1,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,187,193.00","provider":"WFNA","rate":1.187193,"spotRate":1.18664,"forwardPoint":0.000553,"midRate":0},{"legType":0,"quoteId":"G-4796976e3-17c3bac0076-WFNA-2-pfOrg-WFNA-1633088700536","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00","provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}

                it("Rate test", function () {
                    console.log("Rfs Mismatch Swap - Two-Way RateTest -> quote : " )
                    console.log(rfsActiveQuote)

                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[1]
                    let offersArray = rfsActiveQuote.offers
                    let offerRate = offersArray[0]

//  Take quote from just one provider for validoting quote from bids & offers
                    let GUID = bidRate.quoteId

                    let tmpBidArray = new Array()
                    let j = 0;
                    for (i=0; i<bidsArray.length; i++) {
                        if(GUID == bidsArray[i].quoteId) {
                        tmpBidArray[j] = (bidsArray[i])
                        j++
                        }
                    }
                    // validate NL & FL for 2 way for the quote from the selected provider
                    for (i=0; i<tmpBidArray.length; i++) {
                        if (tmpBidArray[i].legType == 0) {
                            assert.equal(tmpBidArray[i].dealtAmount, dltAmtInQuote,"NLDealtAmt is not matching")
                            assert.equal(tmpBidArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        } else if (tmpBidArray[i].legType == 1) {
                            assert.equal(tmpBidArray[i].dealtAmount, 2000000, "FLDealtAmt is not matching")
                            console.log ("===tmpBidArray[i].dealtAmount=="+ JSON.stringify(tmpBidArray[i].dealtAmount))
                            assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    j = 0;
                    let tmpOfferArray = new Array()
                    for (i=0; i<offersArray.length; i++) {
                        if(GUID == offersArray[i].quoteId) {
                        tmpOfferArray[j] = (offersArray[i])
                        j++
                        }
                    }

                    for (i=0; i<tmpOfferArray.length; i++) {
                        if (tmpOfferArray[i].legType == 0) {
                            assert.equal(tmpOfferArray[i].dealtAmount, 1000000, "NLDealtAmt is not matching")
                            assert.equal(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        } else if (tmpOfferArray[i].legType == 1) {
                            assert.equal(tmpOfferArray[i].dealtAmount, 2000000, "FLDealtAmt is not matching")

                            assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    assert.equal(tmpBidArray.length,tmpOfferArray.length)

                    assert.exists(rfsActiveQuote.requestId)
					assert.exists(rfsActiveQuote.nearValueDate)
					assert.exists(rfsActiveQuote.farValueDate)
                    assert.exists(rfsActiveQuote.ttl)
					assert.exists(rfsActiveQuote.mids)
                    // bid rate validation

					assert.exists(bidRate.legType)
					assert.exists(bidRate.quoteId)
                    assert.exists(bidRate.dealtAmount)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.exists(bidRate.midRate)

                    // offer rate validation
                    assert.exists(offerRate.legType)
					assert.exists(offerRate.quoteId)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.exists(offerRate.midRate)

                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.equal(rfsData.priceTypeSwap, rfsActiveQuote.priceType)
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

                    assert.equal('BID', bidRate.type)
                    assert.equal('OFFER', offerRate.type)

               });
            });

            describe("Two-Way Rate TermCcy test ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - Two-Way Rate TermCcy test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_TwoWay_TermCcy_Rate_" + reqId
                    console.log("Rfs Swap - Two-Way RateTest -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Two-Way Rate TermCcy Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Two-Way TermCcy RateTest ->  res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            if (res.rfsRates[0].requestId === systemReqId)
                            {
                                rate = res.rfsRates[0]
                                if(i < 3 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===3) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                            }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });
                it("Rate test", function () {
                    console.log("Rfs Swap - Two-Way RateTest -> quote : " )
                    console.log(rfsActiveQuote)
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offersArray = rfsActiveQuote.offers
                    let offerRate = offersArray[0]

//  Take quote from just one provider for validoting quote from bids & offers
                    let GUID = bidRate.quoteId

                    let tmpBidArray = new Array()
                    let j = 0;
                    for (i=0; i<bidsArray.length; i++) {
                        if(GUID == bidsArray[i].quoteId) {
                        tmpBidArray[j] = (bidsArray[i])
                        j++
                        }
                    }
                    // validate NL & FL for 2 way for the quote from the selected provider
                    for (i=0; i<tmpBidArray.length; i++) {
                        if (tmpBidArray[i].legType == 0) {
                            assert.equal(tmpBidArray[i].dealtAmount, "1000000")
                            //assert.equal(tmpBidArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        } else if (tmpBidArray[i].legType == 1) {
                            assert.equal(tmpBidArray[i].dealtAmount, 1000000)
                            //assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    j = 0;
                    let tmpOfferArray = new Array()
                    for (i=0; i<offersArray.length; i++) {
                        if(GUID == offersArray[i].quoteId) {
                        tmpOfferArray[j] = (offersArray[i])
                        j++
                        }
                    }

                    for (i=0; i<tmpOfferArray.length; i++) {
                        if (tmpOfferArray[i].legType == 0) {
                            assert.equal(tmpOfferArray[i].dealtAmount, dltAmtInQuote)
                            assert.equal(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        } else if (tmpOfferArray[i].legType == 1) {
                            assert.equal(tmpOfferArray[i].dealtAmount, dltAmtInQuote)
                            assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        }
                    }

                    assert.equal(tmpBidArray.length,tmpOfferArray.length)
                    assert.exists(rfsActiveQuote.requestId)
					assert.exists(rfsActiveQuote.nearValueDate)
					assert.exists(rfsActiveQuote.farValueDate)
                    assert.exists(rfsActiveQuote.ttl)
					assert.exists(rfsActiveQuote.mids)
                    // bid rate validation
                    assert.exists(bidRate.legType)
					assert.exists(bidRate.quoteId)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.exists(offerRate.legType)
					assert.exists(offerRate.quoteId)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.exists(offerRate.midRate)

                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.equal(rfsData.priceTypeSwap, rfsActiveQuote.priceType)
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

                    assert.equal('BID', bidRate.type)
                    assert.equal(dltAmtInQuote, bidRate.dealtAmount)
                    assert.equal('OFFER', offerRate.type)
                    assert.equal(dltAmtInQuote, offerRate.dealtAmount)
               });

            });

            describe("One-Way-Bid Rate Term test ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** Rfs Swap - One-Way-Bid Rate Term test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_OneWay_Bid_Rate_Term_" + reqId
                    console.log("Rfs Swap - One-Way-Bid Rate Term test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - One-Way-Bid Rate Term test  ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    systemReqId = ""

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - One-Way-Bid Rate Term test  ->  res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                             if (res.rfsRates[0].requestId === systemReqId)
                             {
                                rate = res.rfsRates[0]
                                if(i < 3 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===3) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                             }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });
/*
                after(function (done) {
                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                done()
                });
*/
               it("Rate test", function () {
                    console.log("Rfs Swap - One-Way-Bid Rate Term test -> quote : " )
                    console.log(rfsActiveQuote)

                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSwap, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")
                    assert.exists(rfsActiveQuote.farValueDate, "farValueDate doesnt exist")

                    //assert.lengthOf(rfsActiveQuote.bids,0)
                    //assert.lengthOf(rfsActiveQuote.mids,0)
                    //assert.lengthOf.notEqual(rfsActiveQuote.offers,0,"offer size is zero")

                    // bid rate
                    assert.equal('1',bidRate.legType, "legtype is not zero")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not BID")
                    assert.equal(dltAmtInQuote,bidRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(bidRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual('0',bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")


                    // offer rate
                    assert.equal('0',offerRate.legType, "legtype is not 1")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dltAmtInQuote,offerRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(offerRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.equal('0',offerRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")

               });

            });

            describe("One-Way-Offer Rate Term test ", function () {
                let dealtAmt = '1,000,000.00'
                before(function (done) {
                    console.log('*************************** rfs swap - One-Way-Offer Rate Term test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_OneWay_Offer_Rate_Term_" + reqId
                    console.log("rfs swap - One-Way-Offer Rate Term test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("rfs swap - One-Way-Offer Rate Term test  ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("rfs swap - One-Way-Offer Rate Term test  ->  res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            if (res.rfsRates[0].requestId === systemReqId)
                            {
                                rate = res.rfsRates[0]
                                if(i < 3 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===3) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                            }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });

                it("Rate test", function () {
                    console.log("rfs swap - One-Way-Offer Rate Term test -> quote : " )
                    console.log(rfsActiveQuote)

                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSwap, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearSettleDate doesnt exist")
                    assert.exists(rfsActiveQuote.farValueDate, "farValueDate doesnt exist")

 //                   assert.lengthOf(rfsActiveQuote.offers,0)
                    assert.lengthOf(rfsActiveQuote.mids,0)

                    // bid rate
                    assert.equal('0',bidRate.legType, "legtype is not 0")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not BID")
                    assert.equal(dltAmtInQuote,bidRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(bidRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.equal('0',bidRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")


                    // offer rate
                    assert.equal('1',offerRate.legType, "legtype is not 0")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dltAmtInQuote,offerRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(offerRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual('0',offerRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")


               });

            });

        });
    };

rfsSwapTC();
