    const assert = require('chai').assert
    const expect = require('chai').expect
    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let flag

    let reqId = Math.floor(Math.random() * 100)

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

    let rfsSpotTC = function(){

        describe("RFS Spot ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });

            beforeEach(function () {
                console.log('about to run a test');
                systemReqId = ""
                flag = false
            });

            describe("Two-Way Rate test ", function () {
                let dealtAmt = '1000000'

                before(function (done) {
                    console.log('*************************** rfs spot - Two-Way Rate test ************************** ' + new Date());
                    tempReqId = "RFS_Spot_TwoWay_RateTest_" + reqId
                    console.log("Rfs spot - Two-Way RateTest -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Two-Way RateTest ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Two-Way RateTest -> res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            if (res.rfsRates[0].requestId === systemReqId) {
                                rate = res.rfsRates[0]
                                if(i < 2 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                    systemReqId = rate.requestId
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i === 2) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                            }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });
// quote : {"requestId":"G4796976d517c0c3da847340","priceType":"Spot","effectiveTime":0,"symbol":"EUR/USD","ttl":10,"dealtCurrency":"EUR","status":"A","nearSettleDate":"09/24/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c0c3da8d4-WFNA-7e-pfOrg-WFNA-1632292939992","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c0c3da8b6-UBSA-305-pfOrg-UBS-1632292939968","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,200.00","provider":"UBS","rate":1.1862,"spotRate":1.1862,"forwardPoint":0,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c0c3da8b6-UBSA-305-pfOrg-UBS-1632292939968","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,530.00","provider":"UBS","rate":1.18653,"spotRate":1.18653,"forwardPoint":0,"midRate":0},{"legType":0,"quoteId":"G-4796976e3-17c0c3da8d4-WFNA-7e-pfOrg-WFNA-1632292939992","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00","provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}

                it("Rate test", function () {
                    console.log("Rfs spot - Two-Way RateTest -> rfsActiveQuote : " )
                    console.log(rfsActiveQuote)
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSpot, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)
                    assert.exists(rfsActiveQuote.nearValueDate)

                    // bid rate validation
                    assert.equal('0',bidRate.legType, "legtype is not zero")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                   // tmp = bidRate.dealtAmount/bidRate.rate
                    sAmt = JSON.stringify(bidRate.dealtAmount*bidRate.rate).split(".")
                    expect(JSON.stringify(bidRate.settledAmount)).to.have.string(sAmt[0])
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.equal("0",bidRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.equal('0',offerRate.legType, "legtype is not zero")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
                    sAmt = JSON.stringify(offerRate.dealtAmount*offerRate.rate).split(".")
                    expect(JSON.stringify(offerRate.settledAmount)).to.have.string(sAmt[0])
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.equal("0",offerRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(offerRate.midRate)

               });

            });

            describe("One-Way-Bid Rate test ", function () {
                let dealtAmt = '1000000'

                before(function (done) {
                    console.log('*************************** rfs spot - One-Way-Bid Rate test ************************** ' + new Date());
                    tempReqId = "RFS_Spot_OneWay_BidRateTest_" + reqId
                    console.log("Rfs spot - One-Way-Bid Rate test -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - One-Way-Bid Rate test  ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - One-Way-Bid Rate test  ->  res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                           if (res.rfsRates[0].requestId === systemReqId) {
                                if(i < 2 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                    systemReqId = rate.requestId
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===2) {
                                      connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                      i++
                                }
                           }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });

// quote : {"requestId":"G4796976d517c0c070462318","priceType":"Spot","effectiveTime":0,"symbol":"EUR/USD","ttl":119,"dealtCurrency":"EUR","status":"A",
// "nearSettleDate":"09/24/2021","bids":[],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c0c0704e3-SGR-b6-pfOrg-SG-1632289359076","type":"OFFER",
//"dealtAmount":"1,000,000.00","settledAmount":"1,186,540.00","provider":"SG","rate":1.18654,"spotRate":1.18654,"forwardPoint":0,"midRate":0},
//{"legType":0,"quoteId":"G-4796976e3-17c0c0704e1-WFNA-6a-pfOrg-WFNA-1632289359077","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00",
//"provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}
                it("Rate test", function () {
                    console.log("Rfs spot - One-Way-Bid Rate test -> quote : " )
                    console.log(rfsActiveQuote)

                    let bidsArray = rfsActiveQuote.bids
                    assert.equal(0,bidsArray.length)
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSpot, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")

                    assert.lengthOf(rfsActiveQuote.bids,0)
                    assert.lengthOf(rfsActiveQuote.mids,0)
                    //assert.lengthOf.notEqual(rfsActiveQuote.offers,0,"offer size is zero")

                    // offer rate
                    assert.equal('0',offerRate.legType, "legtype is not zero")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dealtAmt,offerRate.dealtAmount, "dealtAmount is not correct")
                    sAmt = JSON.stringify(offerRate.dealtAmount*offerRate.rate).split(".")
                    expect(JSON.stringify(offerRate.settledAmount)).to.have.string(sAmt[0])
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.equal('0',offerRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")

               });

            });

            describe("One-Way-Offer Rate test ", function () {
                let dealtAmt = '1000000'

                before(function (done) {
                    console.log('*************************** rfs spot - One-Way-Offer Rate test ************************** ' + new Date());
                    tempReqId = "RFS_Spot_OneWay_OfferRateTest_" + reqId
                    console.log("Rfs spot - One-Way-Offer Rate test -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - One-Way-Offer Rate test  ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - One-Way-Offer Rate test  ->  res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                           if (res.rfsRates[0].requestId === systemReqId) {
                                if(i < 2 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                    systemReqId = rate.requestId
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===2) {
                                      connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                      i++
                                }
                           }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });

// quote : {"requestId":"G4796976d517c0c866d7f6ec","priceType":"Spot","effectiveTime":0,"symbol":"EUR/USD","ttl":10,"dealtCurrency":"EUR","status":"A",
// "nearSettleDate":"09/24/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c0c866ec2-WFNA-a5-pfOrg-WFNA-1632297709254","type":"BID","dealtAmount":"1,000,000.00",
// "settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0}],"offers":[],"mids":[]}
                it("Rate test", function () {
                    console.log("Rfs spot - One-Way-Offer Rate test -> quote : " )
                    console.log(rfsActiveQuote)
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSpot, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesn't exist")
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")

                    assert.lengthOf(rfsActiveQuote.offers,0)
                    assert.lengthOf(rfsActiveQuote.mids,0)

                    // bid rate
                    assert.equal('0',bidRate.legType, "legtype is not zero")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not OFFER")
                    assert.equal(dealtAmt,bidRate.dealtAmount, "dealtAmount is not correct")

                    sAmt = JSON.stringify(bidRate.dealtAmount*bidRate.rate).split(".")
                    expect(JSON.stringify(bidRate.settledAmount)).to.have.string(sAmt[0])
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.equal('0',bidRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")

               });
            });

            describe("Two-Way Rate Term test ", function () {
                let dealtAmt = '1000000'

                before(function (done) {
                    console.log('*************************** rfs spot - Two-Way Rate Term test ************************** ' + new Date());
                    tempReqId = "RFS_Spot_TwoWay_RateTerm_Test_" + reqId
                    console.log("Rfs spot - Two-Way Rate Term Test -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Two-Way Rate Term Test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Two-Way Rate Term Test -> res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                           if (res.rfsRates[0].requestId === systemReqId) {
                                rate = res.rfsRates[0]
                                if(i < 2 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                    systemReqId = rate.requestId
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===2) {
                                      connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                      i++
                                }
                           }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });
// quote : {"requestId":"G4796976d517c0c3da847340","priceType":"Spot","effectiveTime":0,"symbol":"EUR/USD","ttl":10,"dealtCurrency":"EUR","status":"A","nearSettleDate":"09/24/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c0c3da8d4-WFNA-7e-pfOrg-WFNA-1632292939992","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c0c3da8b6-UBSA-305-pfOrg-UBS-1632292939968","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,200.00","provider":"UBS","rate":1.1862,"spotRate":1.1862,"forwardPoint":0,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c0c3da8b6-UBSA-305-pfOrg-UBS-1632292939968","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,530.00","provider":"UBS","rate":1.18653,"spotRate":1.18653,"forwardPoint":0,"midRate":0},{"legType":0,"quoteId":"G-4796976e3-17c0c3da8d4-WFNA-7e-pfOrg-WFNA-1632292939992","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00","provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}

            it("Two-Way Rate Term test", function () {
                    console.log("Rfs spot - Two-Way RateTest -> rfsActiveQuote : " )
                    console.log(rfsActiveQuote)
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSpot, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)
                    assert.exists(rfsActiveQuote.nearValueDate)

                    // bid rate validation
                    assert.equal('0',bidRate.legType, "legtype is not zero")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    console.log("===========bidRate.settledAmount=" + bidRate.settledAmount)
                    console.log("===========bidRate.settledAmount=" + JSON.stringify(bidRate.dealtAmount/bidRate.rate).split(".")[0])
                    bidRatesAmt = JSON.stringify(bidRate.dealtAmount/bidRate.rate).split(".")
                    expect(JSON.stringify(bidRate.settledAmount)).to.have.string(bidRatesAmt[0])
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.equal("0",bidRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.equal('0',bidRate.legType, "legtype is not zero")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
                    sAmt = JSON.stringify(offerRate.dealtAmount/offerRate.rate).split(".")
                    expect(JSON.stringify(offerRate.settledAmount)).to.have.string(sAmt[0])
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.equal("0",offerRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(offerRate.midRate)
               });

            });

            describe("One-Way-Bid Rate Term test ", function () {
                let dealtAmt = '1000000'

                before(function (done) {
                    console.log('*************************** rfs spot - One-Way-Bid Rate Term test ************************** ' + new Date());
                    tempReqId = "RFS_Spot_OneWay_BidRateTerm_Test_" + reqId
                    console.log("Rfs spot - One-Way-Bid Rate Term test -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - One-Way-Bid Rate Term test  ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - One-Way-Bid Rate Term test  ->  res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                           if (res.rfsRates[0].requestId === systemReqId) {
                                if(i < 2 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                    systemReqId = rate.requestId
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i === 2) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                           }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });

// quote : {"requestId":"G4796976d517c0c070462318","priceType":"Spot","effectiveTime":0,"symbol":"EUR/USD","ttl":119,"dealtCurrency":"EUR","status":"A",
// "nearSettleDate":"09/24/2021","bids":[],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c0c0704e3-SGR-b6-pfOrg-SG-1632289359076","type":"OFFER",
//"dealtAmount":"1,000,000.00","settledAmount":"1,186,540.00","provider":"SG","rate":1.18654,"spotRate":1.18654,"forwardPoint":0,"midRate":0},
//{"legType":0,"quoteId":"G-4796976e3-17c0c0704e1-WFNA-6a-pfOrg-WFNA-1632289359077","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00",
//"provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}
                it("One-Way-Bid Rate Term test ", function () {
                    console.log("Rfs spot - One-Way-Bid Rate Term test -> quote : " )
                    console.log(rfsActiveQuote)

                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.notEqual((rfsActiveQuote.bids.length),0,"bid size is zero")
                    assert.lengthOf(rfsActiveQuote.mids,0)
                    assert.lengthOf(rfsActiveQuote.offers,0)

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSpot, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")

                    //assert.lengthOf.notEqual(rfsActiveQuote.offers,0,"offer size is zero")

                    // offer rate
                    assert.equal('0',bidRate.legType, "legtype is not zero")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not OFFER")
                    assert.equal(dealtAmt,bidRate.dealtAmount, "dealtAmount is not correct")
                    sAmt = JSON.stringify(bidRate.dealtAmount/bidRate.rate).split(".")
                    expect(JSON.stringify(bidRate.settledAmount)).to.have.string(sAmt[0])

//                    expect(JSON.stringify(bidRate.dealtAmount/bidRate.rate)).to.have.string(bidRate.settledAmount)
//                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.equal('0',bidRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")

               });

            });

            describe("One-Way-Offer Rate Term test ", function () {
                let dealtAmt = '1000000'

                before(function (done) {
                    console.log('*************************** rfs spot - One-Way-Offer Rate Term test ************************** ' + new Date());
                    tempReqId = "RFS_Spot_OneWay_OfferdRate_Term_Test_" + reqId
                    console.log("Rfs spot - One-Way-Offer Rate Term test -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - One-Way-Offer Rate Term test  ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - One-Way-Offer Rate Term test  ->  res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                           if (res.rfsRates[0].requestId === systemReqId) {
                                rate = res.rfsRates[0]
                                if(i < 2 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                    systemReqId = rate.requestId
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i === 2) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                           }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });

// quote : {"requestId":"G4796976d517c0c866d7f6ec","priceType":"Spot","effectiveTime":0,"symbol":"EUR/USD","ttl":10,"dealtCurrency":"EUR","status":"A",
// "nearSettleDate":"09/24/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c0c866ec2-WFNA-a5-pfOrg-WFNA-1632297709254","type":"BID","dealtAmount":"1,000,000.00",
// "settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0}],"offers":[],"mids":[]}
                it("One-Way-Offer Rate Term test", function () {
                    console.log("Rfs spot - One-Way-Bid Rate Term test -> quote : " )
                    console.log(rfsActiveQuote)
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]

                    assert.notEqual((rfsActiveQuote.offers.length),0,"offers size is zero")
                    assert.lengthOf(rfsActiveQuote.mids,0)
                    assert.lengthOf(rfsActiveQuote.bids,0)

                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeSpot, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")

                    // bid rate
                    assert.equal('0',offerRate.legType, "legtype is not zero")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dealtAmt,offerRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(offerRate.settledAmount, "settledAmount doesnt exist")
                    tmp = offerRate.dealtAmount/offerRate.rate
                    sAmt = JSON.stringify(tmp).split(".")
                    expect(JSON.stringify(offerRate.settledAmount)).to.have.string(sAmt[0])
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.equal('0',offerRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")

               });

            });

            describe("Withdraw response test ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot - Withdraw response test ************************** ' + new Date());
                    tempReqId = "RFS_Spot_Withdraw_ResponseTest_" + reqId
                    console.log("Rfs spot - Withdraw response test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: "60", //rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Withdraw response Test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Withdraw response Test  ->  res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                           if (res.rfsRates[0].requestId === systemReqId) {
                                rate = res.rfsRates[0]
                                if(i < 2 && rate.status === "A") {
                                    rfsActiveQuote = rate
                                    i= i + 1
                                    systemReqId = rate.requestId
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                } else if (i ===2) {
                                    connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                    i++
                                }
                           }
                        } else if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            systemReqId = rfsSubscriptionResponses.requestId
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            if (flag) { done() }
                            flag = true
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            if(flag) { done()}
                            flag = true
                        }
                    }
                });

//{"requestId":"G4796976d517c11e44087af51","rfsEvent":"REQUEST_WITHDRAWN","rfsMessage":{"eventTime":"2021/09/23 09:02:24","eventName":"RFS Withdrawn","eventDetails":"RFS Request Withdrawn for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT"},"clOrderId":"G4796976d517c11e44087af51"}
                it("Withdraw request test", function () {
                    console.log("Rfs spot - Withdraw response Test -> withdraw response : " )
                    console.log(rfsWithdrawResponse)
                    //rfsWithdrawResponse = res.rfsWithdrawAck
                    assert.exists(rfsWithdrawResponse.requestId)
                    assert.equal('REQUEST_WITHDRAWN', rfsWithdrawResponse.rfsEvent)
                    assert.exists(rfsWithdrawResponse.rfsMessage.eventTime)
                    assert.equal('RFS Withdrawn', rfsWithdrawResponse.rfsMessage.eventName)
                    expect(JSON.stringify(rfsWithdrawResponse.rfsMessage.eventDetails)).to.have.string('"RFS Request Withdrawn for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT');
                    assert.exists(rfsWithdrawResponse.clOrderId)
              });

            });

        });
    };

   rfsSpotTC();
