    const assert = require('chai').assert
    const expect = require('chai').expect
    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let reqId, reqId1
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let GUID
    let res, traderes
    let errors
    let bidsArray, bidRate, offerArray, offerRate
    let rfsTradeAckMSG, rfsTradeResponsesMSG, rfsTradesMSG, rfsRejectResponseMSG, errorMSG

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done() }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let rfsNDFSwapTradeTC = function(){

        describe("RFS NDF trade", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
                //done()
            });

            describe("NDF Swap Two-Way Rate function, Buy trade", function () {

                let dealtAmt = '1000000'; //'1,000,000.00'
                before(function (done) {
                    console.log('*************************** rfs NDF - Two-Way Rate test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Two-Way RateTest -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: 150, //rfsData.expiry,
                                 nearValueDate : "1W",
                                 farDealtAmount : "1000000.0",
                                 farValueDate : "3W",
                                 farFixingDate : "",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdfSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - Two-Way RateTest ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - Two-Way RateTest -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                          done()
                        }  //res.rfsRates
                    } //on message

                  }); //before close

                it("NDF Rate test, Buy trade", function () {
                    console.log("Rfs NDF - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                    bidsArray = rfsActiveQuote.bids
                    bidRate = bidsArray[0]
                    offerArray = rfsActiveQuote.offers
                    offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
					assert.exists(rfsActiveQuote.nearValueDate)
                    assert.exists(rfsActiveQuote.ttl)
					assert.exists(rfsActiveQuote.mids)
                    // bid rate validation
                    assert.exists(bidRate.quoteId)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.exists(offerRate.quoteId)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate)

                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                    assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

                    assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
               }); //it rate test

               describe ("NDF Trade function, Buy ", function() {

                      before(function(done){

                        var tradereq = [{
                                        quoteId: offerRate.quoteId,
                                        side: "BUY",
                                        symbol: rfsData.symbolNdf,
                                        dealtCurrency: rfsData.baseCcyNdf,
                                        tradeChannel: "DNET/RFS/BB",
                                        clOrderId: rfsActiveQuote.requestId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                connection.send(JSON.stringify(wstradereq));

                                connection.onmessage = (e1) => {
                                    traderes = JSON.parse(e1.data)
                                    console.log("Trade response is \n"+JSON.stringify(traderes));
                                    if(traderes.rfsTradeAck)
                                     {
                                        rfsTradeAckMSG = traderes.rfsTradeAck[0];
                                        console.log("First trade response is \n" +JSON.stringify(rfsTradeAckMSG));
                                    } else if(traderes.rfsTradeResponses)
                                        {
                                            rfsTradeResponsesMSG = traderes.rfsTradeResponses[0];
                                            console.log("Second trade response is \n" +JSON.stringify(rfsTradeResponsesMSG));
                                             done()
                                        }
//                                        else if(traderes.trades)
//                                    {
//                                        rfsTradesMSG = traderes.rfsTradesMSG[0];
//                                        console.log("Order-trade response is \n" +JSON.stringify(rfsTradesMSG));
//                                              done()
//                                    }

                                } //on message


                                }); //before

                       it("NDF Trade it test, buy", function(){
                                //{"orderId":"FXI9200695110","tradeId":"FXI9200695110","tradeType":"NDFSwap","tenor":"1W","tradeDate":"10/25/2021","valueDate":"11/03/2021","fixingDate":"11/01/2021","executionTime":"10/25/2021 12:22:05:562 GMT","maker":false,"orderSide":"Buy","status":"Verified","symbol":"USD/INR","dealtIns":"USD","dealtAmount":"1000000.00","dAmt":0,"settledAmount":"********.00","sAmt":0,"baseAmount":"1000000.00","bAmt":0,"termAmount":"********.00","tAmt":0,"spotRate":"75.104000","rate":"75.104200","dRate":0,"forwardPoints":"0.0002","customerAccount":"pfOrg","customerOrg":"pfOrg","trader":"user1","counterParty":"NTFX","cptyLongName":"NATIXIS PARIS","cptyTradeId":"FIXProvider-Exec-b40c7d9cce0e3a","counterPartyAccount":"NTFXle","fRate":0,"fDAmt":0,"fSAmt":0,"fBAmt":0,"fTAmt":0,"UPI":"USD_INR_FXNDFSwap","UTI":"MCQRZU5ST1INP9200695110","SEF":false,"externalRequestId":"54","requestId":"G4796976d517cb7667dcc1704","maskedLP":"NTFX","isnet":false,"isMidMarket":false,"mifidFields":{"isin":"EZBW6KRJ3F26"},"sefFields":{"upi":"USD_INR_FXNDFSwap"},"swapTrade":{},"brokerCDQ":{},"benchMarkRate":0,"cptyB":{},"outright":{"fixingDate":"11/01/2021"},"nearLegISIN":"EZBW6KRJ3F26","channel":"DNET/RFS/BB","pricingType":"RFS","header":{"referenceId":"54","customerOrg":"pfOrg","customerId":"user1","customerAccount":"pfOrg"}}
                                    console.log("Trade test passed \n" +JSON.stringify(rfsTradeResponsesMSG));
                                    console.log("Trade response now \n" +JSON.stringify(rfsTradeResponsesMSG.trades[0]));
                                    //Important NDF fields
                                    assert.exists(rfsTradeResponsesMSG.trades[0].orderId);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].tradeId);
                                    assert.equal(rfsTradeResponsesMSG.trades[0].tradeType,"NDFSwap","This is not an NDF trade");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].tenor,"1W","Tenor is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].status,"Verified","The trade is not in Verified state");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].symbol,"USD/INR","The ccy is not USD/INR");
                                    assert.exists(rfsTradeResponsesMSG.trades[0].fixingDate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].forwardPoints);
                                    assert.equal(rfsTradeResponsesMSG.trades[0].UPI,"USD_INR_FXNDFSwap","UPI is incorrect");
                                    //common fields
                                    assert.equal(rfsTradeResponsesMSG.trades[0].dealtIns,"USD","The deal ccy is not USD");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].maker,false,"This is not a taker trade");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].orderSide,"Sell","The side is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].dealtAmount,"1000000.00","The dealt amount is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].baseAmount,"1000000.00","The base amount is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].customerOrg,rfsData.customerOrg,"The customer org is incorrect");
                                    //assert.equal(rfsTradeResponsesMSG.trades[0].SEF,false,"This is a SEF trade");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].requestId,rfsActiveQuote.requestId,"The request Id is incorrect");
                                    //assert.equal(rfsTradeResponsesMSG.trades[0].externalRequestId,systemReqId,"The externalRequestId is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].channel,"DNET/RFS/BB","The channel is incorrect");
                                    assert.exists(rfsTradeResponsesMSG.trades[0].tradeDate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].valueDate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].executionTime);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].settledAmount);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].trader);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].counterParty);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].cptyTradeId);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].counterPartyAccount);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].UTI);
                                    //assert.exists(rfsTradeResponsesMSG.trades[0].maskedLP);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].isnet);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].isMidMarket);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].mifidFields);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].sefFields);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].swapTrade);                                                                                                assert.exists(rfsTradeResponsesMSG.trades[0].brokerCDQ);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].benchMarkRate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].cptyB);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].outright);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].nearLegISIN);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].header);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].externalRequestId);
                                    //Swap fields
                                    //farTenor":"3W","farFixingDate":"11/16/2021","farValueDate":"11/19/2021","farSide":"Sell","farRate":"75.103800","fRate":0,"farDealtAmount":"1000000.00","fDAmt":0,"farSettledAmount":"13314.91","fSAmt":0,"farBaseAmount":"13314.91","fBAmt":0,"farTermAmount":"1000000.00","fTAmt":0,"farForwardPoints":"-0.0002"
                                    //"farLegISIN":"EZV3BV1B5Q17","nearLegISIN":"EZ1T4V3N8CF6"
                                    assert.equal(rfsTradeResponsesMSG.trades[0].farTenor,"3W","Tenor is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].farSide,"Buy","farSide is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].farDealtAmount,"1000000.00","far dealt amount is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].farBaseAmount,"1000000.00","farBarAmount is incorrect");
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farForwardPoints);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farFixingDate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farValueDate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farRate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farSettledAmount);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farTermAmount);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].nearLegISIN);
                                });//it trade test

                        }); //describe trade function close
           });  //decribe 2-way rate test

            describe("NDF Swap Two-Way Rate function, Sell term trade", function () {

                           let dealtAmt = '1000000';  // '1,000,000.00'
                           before(function (done) {
                               console.log('*************************** rfs NDF - Two-Way Rate test ************************** ' + new Date());
                               reqId = Math.floor(Math.random() * 100)
                               console.log("Rfs NDF - Two-Way RateTest -> reqId = " + reqId)
                               var subrequests = [{
                                            symbol : rfsData.symbolNdf,
                                            amount : "1000000.0",
                                            dealtCurrency : rfsData.termCcyNdf,
                                            expiry: 150, //rfsData.expiry,
                                            nearValueDate : "1W",
                                             farDealtAmount : "1000000.0",
                                             farValueDate : "3W",
                                             farFixingDate : "",
                                             fixingDate : "" ,
                                            side : rfsData.sideType2Way,
                                            priceType : rfsData.priceTypeNdfSwap,
                                            customerAccount : rfsData.customerAccount,
                                            customerOrg: rfsData.customerOrg,
                                            priceViewType: rfsData.aggregatedView,
                                            depth: 5,
                                            channel : rfsData.channel,
                                            providers: rfsData.providers,
                                            clOrderId: parseInt(reqId)
                                }]
                               var wsreq = { rfsSubscriptions : subrequests }
                               console.log("Rfs NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                               connection.send(JSON.stringify(wsreq));
                               i = 1

                               connection.onmessage = (e) => {
                                   res = JSON.parse(e.data)
                                   if (res.rfsRates) {
                                       rate = res.rfsRates[0]
                                       if(i < 3 && rate.status === "A") {
                                           console.log("Rfs NDF - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                           rfsActiveQuote = rate
                                           i= i + 1
                                           systemReqId = rate.requestId
                                       } else if (rate.status === "I") {
                                           console.log("Rfs NDF - Two-Way RateTest ->waiting for Inactive quote")
                                           rfsInactiveQuote = res.rfsRates[0]
                                           console.log("Rfs NDF - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                                        } else if (i ===3) {
                                           console.log("Rfs NDF - Two-Way RateTest -> systemReqId : " + systemReqId)
                                           connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                           i++
                                        }
                                     done()
                                   }  //res.rfsRates
                               } //on message

                             }); //before close

                           it("NDF Rate test, Sell term trade", function () {
                               console.log("Rfs NDF - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                               bidsArray = rfsActiveQuote.bids
                               bidRate = bidsArray[0]
                               offerArray = rfsActiveQuote.offers
                               offerRate = offerArray[0]
                               assert.exists(rfsActiveQuote.requestId)
           					assert.exists(rfsActiveQuote.nearValueDate)
                               assert.exists(rfsActiveQuote.ttl)
           					assert.exists(rfsActiveQuote.mids)
                               // bid rate validation
                               assert.exists(bidRate.quoteId)
                               assert.exists(bidRate.settledAmount)
                               assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                               assert.exists(bidRate.provider)
                               assert.isNotNull(bidRate.provider)
                               assert.exists(bidRate.rate)
                               assert.notEqual("0",bidRate.rate, "rate is zero")
                               assert.exists(bidRate.spotRate)
                               assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                               assert.exists(bidRate.forwardPoint)
                               assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                               assert.exists(bidRate.midRate)
                               // offer rate validation
                               assert.exists(offerRate.quoteId)
                               assert.exists(offerRate.settledAmount)
                               assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                               assert.exists(offerRate.provider)
                               assert.isNotNull(offerRate.provider)
                               assert.exists(offerRate.rate)
                               assert.notEqual("0",offerRate.rate, "rate is zero")
                               assert.exists(offerRate.spotRate)
                               assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                               assert.exists(offerRate.forwardPoint)
                               assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                               assert.exists(offerRate.midRate)

                               assert.exists(rfsActiveQuote.effectiveTime)
                               assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                               assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                               assert.equal(rfsData.termCcyNdf, rfsActiveQuote.dealtCurrency)
                               assert.equal('A', rfsActiveQuote.status)

                               assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                               assert.equal(dealtAmt, bidRate.dealtAmount)
                               assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                               assert.equal(dealtAmt, offerRate.dealtAmount)
                          }); //it rate test

                          describe ("NDF Trade function, Sell trade ", function() {

                                        before(function(done){

                                                var tradereq = [{
                                                   quoteId: offerRate.quoteId,
                                                   side: "SELL",
                                                   symbol: rfsData.symbolNdf,
                                                   dealtCurrency: rfsData.termCcyNdf,
                                                   tradeChannel: "DNET/RFS/BB",
                                                   clOrderId: rfsActiveQuote.requestId
                                           }]
                                           var wstradereq = { rfsTrades : tradereq }
                                           console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                           connection.send(JSON.stringify(wstradereq));

                                           connection.onmessage = (e1) => {
                                               traderes = JSON.parse(e1.data)
                                               console.log("Trade response is \n"+JSON.stringify(traderes));
                                               if(traderes.rfsTradeAck)
                                                {
                                                   rfsTradeAckMSG = traderes.rfsTradeAck[0];
                                                   console.log("First trade response is \n" +JSON.stringify(rfsTradeAckMSG));
                                               } else if(traderes.rfsTradeResponses)
                                                   {
                                                       rfsTradeResponsesMSG = traderes.rfsTradeResponses[0];
                                                       console.log("Second trade response is \n" +JSON.stringify(rfsTradeResponsesMSG));
                                                        done()
                                                   }
           //                                        else if(traderes.trades)
           //                                    {
           //                                        rfsTradesMSG = traderes.rfsTradesMSG[0];
           //                                        console.log("Order-trade response is \n" +JSON.stringify(rfsTradesMSG));
           //                                              done()
           //                                    }

                                           } //on message


                                           }); //before

                                           it("NDF Trade test it, Sell trade", function(){
                                               console.log("Trade test passed \n" +JSON.stringify(rfsTradeResponsesMSG));
                                               console.log("Trade response now \n" +JSON.stringify(rfsTradeResponsesMSG.trades[0]));
                                               //Important NDF fields
                                               assert.exists(rfsTradeResponsesMSG.trades[0].orderId);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].tradeId);
                                               assert.equal(rfsTradeResponsesMSG.trades[0].tradeType,"NDFSwap","This is not an NDF trade");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].tenor,"1W","Tenor is incorrect");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].status,"Verified","The trade is not in Verified state");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].symbol,"USD/INR","The ccy is not USD/INR");
                                               assert.exists(rfsTradeResponsesMSG.trades[0].fixingDate);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].forwardPoints);
                                               assert.equal(rfsTradeResponsesMSG.trades[0].UPI,"USD_INR_FXNDFSwap","UPI is incorrect");
                                               //common fields
                                               assert.equal(rfsTradeResponsesMSG.trades[0].dealtIns,"INR","The deal ccy is not INR");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].maker,false,"This is not a taker trade");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].orderSide,"Buy","The side is incorrect");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].dealtAmount,"1000000.00","The dealt amount is incorrect");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].termAmount,"1000000.00","The term amount is incorrect");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].customerOrg,rfsData.customerOrg,"The customer org is incorrect");
                                               //assert.equal(rfsTradeResponsesMSG.trades[0].SEF,false,"This is a SEF trade");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].requestId,rfsActiveQuote.requestId,"The request Id is incorrect");
                                               //assert.equal(rfsTradeResponsesMSG.trades[0].externalRequestId,systemReqId,"The externalRequestId is incorrect");
                                               assert.equal(rfsTradeResponsesMSG.trades[0].channel,"DNET/RFS/BB","The channel is incorrect");
                                               assert.exists(rfsTradeResponsesMSG.trades[0].tradeDate);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].valueDate);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].executionTime);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].settledAmount);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].trader);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].counterParty);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].cptyTradeId);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].counterPartyAccount);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].UTI);
                                               //assert.exists(rfsTradeResponsesMSG.trades[0].maskedLP);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].isnet);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].isMidMarket);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].mifidFields);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].sefFields);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].swapTrade);                                                                                                assert.exists(rfsTradeResponsesMSG.trades[0].brokerCDQ);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].benchMarkRate);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].cptyB);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].outright);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].nearLegISIN);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].header);
                                               assert.exists(rfsTradeResponsesMSG.trades[0].externalRequestId);
 //Swap fields
                                    //farTenor":"3W","farFixingDate":"11/16/2021","farValueDate":"11/19/2021","farSide":"Sell","farRate":"75.103800","fRate":0,"farDealtAmount":"1000000.00","fDAmt":0,"farSettledAmount":"13314.91","fSAmt":0,"farBaseAmount":"13314.91","fBAmt":0,"farTermAmount":"1000000.00","fTAmt":0,"farForwardPoints":"-0.0002"
                                    //"farLegISIN":"EZV3BV1B5Q17","nearLegISIN":"EZ1T4V3N8CF6"
                                    assert.equal(rfsTradeResponsesMSG.trades[0].farTenor,"3W","Tenor is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].farSide,"Sell","farSide is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].farDealtAmount,"1000000.00","far dealt amount is incorrect");
                                    assert.equal(rfsTradeResponsesMSG.trades[0].farTermAmount,"1000000.00","farBarAmount is incorrect");
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farForwardPoints);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farFixingDate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farValueDate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farRate);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farSettledAmount);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].farBaseAmount);
                                    assert.exists(rfsTradeResponsesMSG.trades[0].nearLegISIN);
                                           });//it trade test

                                   }); //describe trade function close
                      });  //decribe 2-way rate test

        });  //describe rfs NDF Trade close

    };  //let rfsNDFTC close

let rfsNDFSwapTradeNegTC = function(){

     describe("RFS NDF Neg TCs for NDF Swap trade ", function () {

                       before(function (done) {
                           wsconnect(done);
                       });

                       after(function () {
                           connection.close()
                           //done()
                       });

        describe("NDF Swap Two-Way Rate function, Trade reject, Quote not available", function () {

                            let dealtAmt = '1000000'; //'1,000,000.00'
                            before(function (done) {
                                console.log('*************************** rfs NDF - Two-Way Rate test ************************** ' + new Date());
                                reqId = Math.floor(Math.random() * 100)
                                console.log("Rfs NDF - Two-Way RateTest -> reqId = " + reqId)
                                var subrequests = [{
                                             symbol : rfsData.symbolNdf,
                                             amount : "1000000.0",
                                             dealtCurrency : rfsData.baseCcyNdf,
                                             expiry: 150, //rfsData.expiry,
                                             nearValueDate : "1W",
                                             farDealtAmount : "1000000.0",
                                             farValueDate : "3W",
                                             farFixingDate : "",
                                             fixingDate : "" ,
                                             side : rfsData.sideType2Way,
                                             priceType : rfsData.priceTypeNdfSwap,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: rfsData.providers,
                                             clOrderId: parseInt(reqId)
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("Rfs NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                                connection.send(JSON.stringify(wsreq));
                                i = 1

                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    if (res.rfsRates) {
                                        rate = res.rfsRates[0]
                                        if(i < 3 && rate.status === "A") {
                                            console.log("Rfs NDF - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                            rfsActiveQuote = rate
                                            i= i + 1
                                            systemReqId = rate.requestId
                                        } else if (rate.status === "I") {
                                            console.log("Rfs NDF - Two-Way RateTest ->waiting for Inactive quote")
                                            rfsInactiveQuote = res.rfsRates[0]
                                            console.log("Rfs NDF - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                                         } else if (i ===3) {
                                            console.log("Rfs NDF - Two-Way RateTest -> systemReqId : " + systemReqId)
                                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                            i++
                                         }
                                      done()
                                    }  //res.rfsRates
                                } //on message

                              }); //before close

                            it("NDF Rate test, Buy trade, trade reject", function () {
                                console.log("Rfs NDF - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                                bidsArray = rfsActiveQuote.bids
                                bidRate = bidsArray[0]
                                offerArray = rfsActiveQuote.offers
                                offerRate = offerArray[0]
                                assert.exists(rfsActiveQuote.requestId)
            					assert.exists(rfsActiveQuote.nearValueDate)
                                assert.exists(rfsActiveQuote.ttl)
            					assert.exists(rfsActiveQuote.mids)
                                // bid rate validation
                                assert.exists(bidRate.quoteId)
                                assert.exists(bidRate.settledAmount)
                                assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                                assert.exists(bidRate.provider)
                                assert.isNotNull(bidRate.provider)
                                assert.exists(bidRate.rate)
                                assert.notEqual("0",bidRate.rate, "rate is zero")
                                assert.exists(bidRate.spotRate)
                                assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                                assert.exists(bidRate.forwardPoint)
                                assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                                assert.exists(bidRate.midRate)
                                // offer rate validation
                                assert.exists(offerRate.quoteId)
                                assert.exists(offerRate.settledAmount)
                                assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                                assert.exists(offerRate.provider)
                                assert.isNotNull(offerRate.provider)
                                assert.exists(offerRate.rate)
                                assert.notEqual("0",offerRate.rate, "rate is zero")
                                assert.exists(offerRate.spotRate)
                                assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                                assert.exists(offerRate.forwardPoint)
                                assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                                assert.exists(offerRate.midRate)

                                assert.exists(rfsActiveQuote.effectiveTime)
                                assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                                assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                                assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                                assert.equal('A', rfsActiveQuote.status)

                                assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                                assert.equal(dealtAmt, bidRate.dealtAmount)
                                assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                                assert.equal(dealtAmt, offerRate.dealtAmount)
                           }); //it rate test


                      describe ("NDF Trade function, Buy, trade reject, Quote not available ", function() {

                                                            before(function(done){

                                                              var tradereq = [{
                                                                              quoteId: systemReqId,
                                                                              side: "BUY",
                                                                              symbol: rfsData.symbolNdf,
                                                                              dealtCurrency: rfsData.baseCcyNdf,
                                                                              tradeChannel: "DNET/RFS/BB",
                                                                              clOrderId: rfsActiveQuote.systemReqId
                                                                      }]
                                                                      var wstradereq = { rfsTrades : tradereq }
                                                                      console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                                                      connection.send(JSON.stringify(wstradereq));

                                                                      connection.onmessage = (e1) => {
                                                                      //{"rfsResponses":[{"rfsEvent":"TRADE_REQUEST_REJECTED","rfsMessage":{"eventTime":"2021/10/25 13:56:42"},"errorCode":"QUOTE_NOT_AVAILABLE"}]}
                                                                          traderes = JSON.parse(e1.data)
                                                                          console.log("Trade response is \n"+JSON.stringify(traderes));
                                                                          if(traderes.rfsTradeAck)
                                                                           {
                                                                              rfsTradeAckMSG = traderes.rfsTradeAck[0];
                                                                              console.log("First trade response is \n" +JSON.stringify(rfsTradeAckMSG));
                                                                          } else if(traderes.rfsResponses)
                                                                              {
                                                                                  rfsRejectResponseMSG = traderes.rfsResponses;
                                                                                  console.log("Second reject response is \n" +JSON.stringify(rfsRejectResponseMSG));
                                                                                   done()
                                                                              }
                                                                      } //on message


                                                                      }); //before

                                               it("NDF Trade it test, trade reject, Quote not available", function(){

                                                                console.log("Trade reject test passed \n" +JSON.stringify(traderes));
                                                               //{"rfsResponses":[{"rfsEvent":"TRADE_REQUEST_REJECTED","rfsMessage":{"eventTime":"2021/10/25 13:56:42"},"errorCode":"QUOTE_NOT_AVAILABLE"}]}
                                                                assert.equal(JSON.stringify(traderes.rfsResponses[0].rfsEvent),'"TRADE_REQUEST_REJECTED"',"The event message is incorrect");
                                                                assert.equal(JSON.stringify(traderes.rfsResponses[0].errorCode),'"QUOTE_NOT_AVAILABLE"',"The error message is incorrect");
                                                                assert.exists(JSON.stringify(traderes.rfsResponses[0].rfsMessage));
                                                        });//it trade reject test

                            }); //describe trade reject function close

              });  //decribe 2-way rate test, trade reject TC


             describe("NDF Swap Two-Way Rate function, Trade reject, incorrect side in trade req message", function () {

                   let dealtAmt = '1000000'; //'1,000,000.00'
                   before(function (done) {
                                console.log('*************************** rfs NDF - Two-Way Rate test ************************** ' + new Date());
                                reqId = Math.floor(Math.random() * 100)
                                console.log("Rfs NDF - Two-Way RateTest -> reqId = " + reqId)
                                var subrequests = [{
                                             symbol : rfsData.symbolNdf,
                                             amount : "1000000.0",
                                             dealtCurrency : rfsData.baseCcyNdf,
                                             expiry: 150, //rfsData.expiry,
                                             nearValueDate : "1W",
                                             farDealtAmount : "1000000.0",
                                             farValueDate : "3W",
                                             farFixingDate : "",
                                                 fixingDate : "" ,
                                             side : rfsData.sideType2Way,
                                             priceType : rfsData.priceTypeNdfSwap,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: rfsData.providers,
                                             clOrderId: parseInt(reqId)
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("Rfs NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                                connection.send(JSON.stringify(wsreq));
                                i = 1

                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    if (res.rfsRates) {
                                        rate = res.rfsRates[0]
                                        if(i < 3 && rate.status === "A") {
                                            console.log("Rfs NDF - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                            rfsActiveQuote = rate
                                            i= i + 1
                                            systemReqId = rate.requestId
                                                done()
                                             }
                                    }  //res.rfsRates
                                } //on message

                              }); //before close

                   it("NDF Rate test, Buy trade, trade reject", function () {
                                console.log("Rfs NDF - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                                bidsArray = rfsActiveQuote.bids
                                bidRate = bidsArray[0]
                                offerArray = rfsActiveQuote.offers
                                offerRate = offerArray[0]
                                assert.exists(rfsActiveQuote.requestId)
            					assert.exists(rfsActiveQuote.nearValueDate)
                                assert.exists(rfsActiveQuote.ttl)
            					assert.exists(rfsActiveQuote.mids)
                                // bid rate validation
                                assert.exists(bidRate.quoteId)
                                assert.exists(bidRate.settledAmount)
                                assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                                assert.exists(bidRate.provider)
                                assert.isNotNull(bidRate.provider)
                                assert.exists(bidRate.rate)
                                assert.notEqual("0",bidRate.rate, "rate is zero")
                                assert.exists(bidRate.spotRate)
                                assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                                assert.exists(bidRate.forwardPoint)
                                assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                                assert.exists(bidRate.midRate)
                                // offer rate validation
                                assert.exists(offerRate.quoteId)
                                assert.exists(offerRate.settledAmount)
                                assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                                assert.exists(offerRate.provider)
                                assert.isNotNull(offerRate.provider)
                                assert.exists(offerRate.rate)
                                assert.notEqual("0",offerRate.rate, "rate is zero")
                                assert.exists(offerRate.spotRate)
                                assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                                assert.exists(offerRate.forwardPoint)
                                assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                                assert.exists(offerRate.midRate)

                                assert.exists(rfsActiveQuote.effectiveTime)
                                assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                                assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                                assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                                assert.equal('A', rfsActiveQuote.status)

                                assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                                assert.equal(dealtAmt, bidRate.dealtAmount)
                                assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                                assert.equal(dealtAmt, offerRate.dealtAmount)
                           }); //it rate test


                   describe ("NDF Trade function, Buy, trade reject, incorrect side in trade request message", function() {

                            before(function(done){

                                                              var tradereq = [{
                                                                              quoteId: offerRate.quoteId,
                                                                              side: "",
                                                                              symbol: rfsData.symbolNdf,
                                                                              dealtCurrency: rfsData.baseCcyNdf,
                                                                              tradeChannel: "DNET/RFS/BB",
                                                                              clOrderId: rfsActiveQuote.systemReqId
                                                                      }]
                                                                      var wstradereq = { rfsTrades : tradereq }
                                                                      console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                                                      connection.send(JSON.stringify(wstradereq));

                                                                      connection.onmessage = (e1) => {
                                                                      //{"rfsResponses":[{"rfsEvent":"TRADE_REQUEST_REJECTED","rfsMessage":{"eventTime":"2021/10/25 13:56:42"},"errorCode":"QUOTE_NOT_AVAILABLE"}]}
                                                                          traderes = JSON.parse(e1.data)
                                                                          console.log("Error response is \n"+JSON.stringify(traderes));
                                                                          if(traderes.errors)
                                                                           {
                                                                              errorMSG = traderes.errors[0];
                                                                              console.log("Error response is \n" +JSON.stringify(rfsTradeAckMSG));
                                                                              done()
                                                                          }
                                                                      } //on message

                                                                      }); //before

                                               it("NDF Trade it test, trade reject, incorrect trade req msg", function(){
                                                       //[{"errorCode":1,"errorMessage":"Not a valid request."}]
                                                                console.log("Trade reject test passed \n" +JSON.stringify(traderes));
                                                                assert.equal(JSON.stringify(traderes.errors[0].errorMessage),'"Not a valid request."',"The error message is incorrect");
                                                                assert.equal(JSON.stringify(traderes.errors[0].errorCode),1,"The error code is incorrect");
                                                        });//it trade reject test

                            }); //describe trade reject function close

              });  //decribe 2-way rate test, trade reject TC


          describe("NDF SWap Two-Way Rate function, Mismatch amt Buy trade", function () {

                        let dealtAmt = '1,000,000.00';
                        let NLDealtAmt = '2,000,000.00';
                       let FLDealtAmt = '1,000,000.00';
                      before(function (done) {
                                                                    console.log('*************************** rfs NDF - Two-Way Rate test ************************** ' + new Date());
                                                                    reqId1 = Math.floor(Math.random() * 100)
                                                                    console.log("Rfs NDF - Two-Way RateTest -> reqId = " + reqId1)
                                                                    var subrequests = [{
                                                                                 symbol : rfsData.symbolNdf,
                                                                                 amount : "2000000.0",
                                                                                 dealtCurrency : rfsData.baseCcyNdf,
                                                                                 expiry: 150, //rfsData.expiry,
                                                                                 nearValueDate : "1W",
                                                                                 //nearDealtAmount: "2000000.0",
                                                                                 farDealtAmount : "1000000.0",
                                                                                 farValueDate : "3W",
                                                                                 farFixingDate : "",
                                                                                 fixingDate : "" ,
                                                                                 side : rfsData.sideType2Way,
                                                                                 priceType : rfsData.priceTypeNdfSwap,
                                                                                 customerAccount : rfsData.customerAccount,
                                                                                 customerOrg: rfsData.customerOrg,
                                                                                 priceViewType: rfsData.aggregatedView,
                                                                                 depth: 5,
                                                                                 channel : rfsData.channel,
                                                                                 providers: rfsData.providers,
                                                                                 clOrderId: parseInt(reqId1)
                                                                     }]
                                                                    var wsreq = { rfsSubscriptions : subrequests }
                                                                    console.log("Rfs NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                                                                    connection.send(JSON.stringify(wsreq));
                                                                    i = 1

                                                                    connection.onmessage = (e) => {
                                                                        res = JSON.parse(e.data)
                                                                        if (res.rfsRates) {
                                                                            rate = res.rfsRates[0]
                                                                            if(i < 3 && rate.status === "A") {
                                                                                console.log("Rfs NDF - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                                                                rfsActiveQuote = rate
                                                                                i= i + 1
                                                                                systemReqId = rate.requestId
                                                                                done()
                                                                               }
//                                                                             else if (rate.status === "I") {
//                                                                                console.log("Rfs NDF - Two-Way RateTest ->waiting for Inactive quote")
//                                                                                rfsInactiveQuote = res.rfsRates[0]
//                                                                                console.log("Rfs NDF - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
//                                                                             } else if (i ===3) {
//                                                                                console.log("Rfs NDF - Two-Way RateTest -> systemReqId : " + systemReqId)
//                                                                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
//                                                                                i++
//                                                                             }
                                                                         }  //res.rfsRates
                                                                    } //on message

                                                                  }); //before close

                 it("Rate test, mismatch swap, buy trade", function () {
                                            console.log("Rfs Mismatch Swap - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                                            let bidsArray = rfsActiveQuote.bids
                                            let bidRate = bidsArray[1]
                                            let offersArray = rfsActiveQuote.offers
                                            let offerRate = offersArray[0]

                        //  Take quote from just one provider for validoting quote from bids & offers
                                           GUID = bidRate.quoteId

                        //                    let tmpBidArray = new Array()
                        //                    let j = 0;
                        //                    for (i=0; i<bidsArray.length; i++) {
                        //                        if(GUID == bidsArray[i].quoteId) {
                        //                        tmpBidArray[j] = (bidsArray[i])
                        //                        j++
                        //                        }
                        //                    }
                        //                    // validate NL & FL for 2 way for the quote from the selected provider
                        //                    for (i=0; i<tmpBidArray.length; i++) {
                        //                        console.log ("===tmpArray=="  +i+ "======" + JSON.stringify(tmpBidArray[i]))
                        //                        if (tmpBidArray[i].legType == 0) {
                        //                            assert.equal(tmpBidArray[i].dealtAmount, dealtAmt,"NLDealtAmt is not matching")
                        //                            assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        //                        } else if (tmpBidArray[i].legType == 1) {
                        //                            assert.equal(tmpBidArray[i].dealtAmount, dealtAmt, "FLDealtAmt is not matching")
                        //                            assert.notEqual(tmpBidArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        //                        }
                        //                    }
                        //
                        //                    j = 0;
                        //                    let tmpOfferArray = new Array()
                        //                    for (i=0; i<offersArray.length; i++) {
                        //                        if(GUID == offersArray[i].quoteId) {
                        //                        tmpOfferArray[j] = (offersArray[i])
                        //                        j++
                        //                        }
                        //                    }
                        //
                        //                    for (i=0; i<tmpOfferArray.length; i++) {
                        //                        console.log ("===tmpOfferArray=="  +i+ "======" + JSON.stringify(tmpOfferArray[i]))
                        //                        if (tmpOfferArray[i].legType == 0) {
                        //                            assert.equal(tmpOfferArray[i].dealtAmount, NLDealtAmt, "NLDealtAmt is not matching")
                        //                            assert.equal(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is not equal to 0")
                        //                        } else if (tmpOfferArray[i].legType == 1) {
                        //                            assert.equal(tmpOfferArray[i].dealtAmount, FLDealtAmt, "FLDealtAmt is not matching")
                        //                            assert.notEqual(tmpOfferArray[i].forwardPoint, 0, "forwardpoint is equal to 0")
                        //                        }
                        //                    }
                        //
                        //                    assert.equal(tmpBidArray.length,tmpOfferArray.length)

                                            assert.exists(rfsActiveQuote.requestId)
                        					assert.exists(rfsActiveQuote.nearValueDate)
                        					assert.exists(rfsActiveQuote.farFixingDate)
                                            assert.exists(rfsActiveQuote.ttl)
                        					assert.exists(rfsActiveQuote.mids)
                                            // bid rate validation

                        //					assert.exists(bidRate.legType)
                        //					assert.exists(bidRate.quoteId)
                        //                    assert.exists(bidRate.settledAmount)
                        //                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                        //                    assert.exists(bidRate.provider)
                        //                    assert.isNotNull(bidRate.provider)
                        //                    assert.exists(bidRate.rate)
                        //                    assert.notEqual("0",bidRate.rate, "rate is zero")
                        //                    assert.exists(bidRate.spotRate)
                        //                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                        //                    assert.exists(bidRate.forwardPoint)
                        //                    assert.exists(bidRate.midRate)
                        //
                        //                    // offer rate validation
                        //                    assert.exists(offerRate.legType)
                        //					assert.exists(offerRate.quoteId)
                        //                    assert.exists(offerRate.settledAmount)
                        //                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                        //                    assert.exists(offerRate.provider)
                        //                    assert.isNotNull(offerRate.provider)
                        //                    assert.exists(offerRate.rate)
                        //                    assert.notEqual("0",offerRate.rate, "rate is zero")
                        //                    assert.exists(offerRate.spotRate)
                        //                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                        //                    assert.exists(offerRate.forwardPoint)
                        //                    assert.exists(offerRate.midRate)
                        //
                        //                    assert.exists(rfsActiveQuote.effectiveTime)
                        //                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                        //                    assert.equal(rfsData.priceTypeSwap, rfsActiveQuote.priceType)
                        //                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                        //                    assert.equal('A', rfsActiveQuote.status)

                                            assert.equal('BID', bidRate.type)
                                            assert.equal('OFFER', offerRate.type)

                                       });

                  describe ("NDF Trade function, mismatch amt Buy ", function() {

                                                                      before(function(done){

                                                                        var tradereq = [{
                                                                                        quoteId: GUID,
                                                                                        side: "SELL",
                                                                                        symbol: rfsData.symbolNdf,
                                                                                        dealtCurrency: rfsData.baseCcyNdf,
                                                                                        tradeChannel: "DNET/RFS/BB",
                                                                                        clOrderId: parseInt(reqId1)
                                                                                }]
                                                                                var wstradereq = { rfsTrades : tradereq }
                                                                                console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                                                                connection.send(JSON.stringify(wstradereq));

                                                                                connection.onmessage = (e1) => {
                                                                                    traderes = JSON.parse(e1.data)
                                                                                    console.log("Trade response is \n"+JSON.stringify(traderes));
                                                                                    if(traderes.rfsTradeAck)
                                                                                     {
                                                                                        rfsTradeAckMSG = traderes.rfsTradeAck[0];
                                                                                        console.log("First trade response is \n" +JSON.stringify(rfsTradeAckMSG));
                                                                                    } else if(traderes.rfsTradeResponses)
                                                                                        {
                                                                                            rfsTradeResponsesMSG = traderes.rfsTradeResponses[0];
                                                                                            console.log("Second trade response is \n" +JSON.stringify(rfsTradeResponsesMSG));
                                                                                             done()
                                                                                        }

                                                                                } //on message


                                                                                }); //before

                                                                       it("NDF Swap Trade test, mismtach amt, buy", function(){
                                                                                //{"orderId":"FXI9200695110","tradeId":"FXI9200695110","tradeType":"NDFSwap","tenor":"1W","tradeDate":"10/25/2021","valueDate":"11/03/2021","fixingDate":"11/01/2021","executionTime":"10/25/2021 12:22:05:562 GMT","maker":false,"orderSide":"Buy","status":"Verified","symbol":"USD/INR","dealtIns":"USD","dealtAmount":"1000000.00","dAmt":0,"settledAmount":"********.00","sAmt":0,"baseAmount":"1000000.00","bAmt":0,"termAmount":"********.00","tAmt":0,"spotRate":"75.104000","rate":"75.104200","dRate":0,"forwardPoints":"0.0002","customerAccount":"pfOrg","customerOrg":"pfOrg","trader":"user1","counterParty":"NTFX","cptyLongName":"NATIXIS PARIS","cptyTradeId":"FIXProvider-Exec-b40c7d9cce0e3a","counterPartyAccount":"NTFXle","fRate":0,"fDAmt":0,"fSAmt":0,"fBAmt":0,"fTAmt":0,"UPI":"USD_INR_FXNDFSwap","UTI":"MCQRZU5ST1INP9200695110","SEF":false,"externalRequestId":"54","requestId":"G4796976d517cb7667dcc1704","maskedLP":"NTFX","isnet":false,"isMidMarket":false,"mifidFields":{"isin":"EZBW6KRJ3F26"},"sefFields":{"upi":"USD_INR_FXNDFSwap"},"swapTrade":{},"brokerCDQ":{},"benchMarkRate":0,"cptyB":{},"outright":{"fixingDate":"11/01/2021"},"nearLegISIN":"EZBW6KRJ3F26","channel":"DNET/RFS/BB","pricingType":"RFS","header":{"referenceId":"54","customerOrg":"pfOrg","customerId":"user1","customerAccount":"pfOrg"}}
                                                                                    console.log("Trade test passed \n" +JSON.stringify(rfsTradeResponsesMSG));
                                                                                    console.log("Trade response now \n" +JSON.stringify(rfsTradeResponsesMSG.trades[0]));
                                                                                    //Important NDF fields
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].orderId);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].tradeId);
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].tradeType,"NDFSwap","This is not an NDF trade");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].tenor,"1W","Tenor is incorrect");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].status,"Verified","The trade is not in Verified state");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].symbol,"USD/INR","The ccy is not USD/INR");
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].fixingDate);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].forwardPoints);
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].UPI,"USD_INR_FXNDFSwap","UPI is incorrect");
                                                                                    //common fields
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].dealtIns,"USD","The deal ccy is not USD");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].maker,false,"This is not a taker trade");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].orderSide,"Buy","The side is incorrect");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].dealtAmount,"1000000.00","The dealt amount is incorrect");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].baseAmount,"1000000.00","The base amount is incorrect");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].customerOrg,rfsData.customerOrg,"The customer org is incorrect");
                                                                                    //assert.equal(rfsTradeResponsesMSG.trades[0].SEF,false,"This is a SEF trade");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].requestId,systemReqId,"The request Id is incorrect");
                                                                                    //assert.equal(rfsTradeResponsesMSG.trades[0].externalRequestId,parseInt(reqId1),"The externalRequestId is incorrect");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].channel,"DNET/RFS/BB","The channel is incorrect");
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].tradeDate);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].valueDate);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].executionTime);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].settledAmount);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].trader);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].counterParty);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].cptyTradeId);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].counterPartyAccount);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].UTI);
                                                                                    //assert.exists(rfsTradeResponsesMSG.trades[0].maskedLP);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].isnet);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].isMidMarket);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].mifidFields);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].sefFields);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].swapTrade);                                                                                                assert.exists(rfsTradeResponsesMSG.trades[0].brokerCDQ);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].benchMarkRate);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].cptyB);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].outright);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].nearLegISIN);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].header);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].externalRequestId);
                                                                                    //Swap fields
                                                                                    //farTenor":"3W","farFixingDate":"11/16/2021","farValueDate":"11/19/2021","farSide":"Sell","farRate":"75.103800","fRate":0,"farDealtAmount":"1000000.00","fDAmt":0,"farSettledAmount":"13314.91","fSAmt":0,"farBaseAmount":"13314.91","fBAmt":0,"farTermAmount":"1000000.00","fTAmt":0,"farForwardPoints":"-0.0002"
                                                                                    //"farLegISIN":"EZV3BV1B5Q17","nearLegISIN":"EZ1T4V3N8CF6"
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].farTenor,"3W","Tenor is incorrect");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].farSide,"Sell","farSide is incorrect");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].farDealtAmount,"1000000.00","far dealt amount is incorrect");
                                                                                    assert.equal(rfsTradeResponsesMSG.trades[0].farBaseAmount,"1000000.00","farBarAmount is incorrect");
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].farForwardPoints);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].farFixingDate);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].farValueDate);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].farRate);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].farSettledAmount);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].farTermAmount);
                                                                                    assert.exists(rfsTradeResponsesMSG.trades[0].nearLegISIN);
                                                                                });//it trade test

                                                                        }); //describe trade function clos

               });  //decribe 2-way rate test


            /** No validations done for other fields, dealt ccy , symbol, channel, clorderId
             describe("NDF Two-Way Rate function, Trade reject, incorrect symbol in trade req message", function () {

                            let dealtAmt = '1,000,000.00'
                            before(function (done) {
                                console.log('*************************** rfs NDF - Two-Way Rate test ************************** ' + new Date());
                                reqId = Math.floor(Math.random() * 100)
                                console.log("Rfs NDF - Two-Way RateTest -> reqId = " + reqId)
                                var subrequests = [{
                                             symbol : rfsData.symbolNdf,
                                             amount : "1000000.0",
                                             dealtCurrency : rfsData.baseCcyNdf,
                                             expiry: 150, //rfsData.expiry,
                                             nearValueDate : "3W",
                                             fixingDate : "" ,
                                             side : rfsData.sideType2Way,
                                             priceType : rfsData.priceTypeNdfSwap,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: rfsData.providers,
                                             clOrderId: parseInt(reqId)
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("Rfs NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                                connection.send(JSON.stringify(wsreq));
                                i = 1

                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    if (res.rfsRates) {
                                        rate = res.rfsRates[0]
                                        if(i < 3 && rate.status === "A") {
                                            console.log("Rfs NDF - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                            rfsActiveQuote = rate
                                            i= i + 1
                                            systemReqId = rate.requestId
                                        } else if (rate.status === "I") {
                                            console.log("Rfs NDF - Two-Way RateTest ->waiting for Inactive quote")
                                            rfsInactiveQuote = res.rfsRates[0]
                                            console.log("Rfs NDF - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                                         } else if (i ===3) {
                                            console.log("Rfs NDF - Two-Way RateTest -> systemReqId : " + systemReqId)
                                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                            i++
                                         }
                                      done()
                                    }  //res.rfsRates
                                } //on message

                              }); //before close

                            it("NDF Rate test, Buy trade, trade reject", function () {
                                console.log("Rfs NDF - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                                bidsArray = rfsActiveQuote.bids
                                bidRate = bidsArray[0]
                                offerArray = rfsActiveQuote.offers
                                offerRate = offerArray[0]
                                assert.exists(rfsActiveQuote.requestId)
            					assert.exists(rfsActiveQuote.nearValueDate)
                                assert.exists(rfsActiveQuote.ttl)
            					assert.exists(rfsActiveQuote.mids)
                                // bid rate validation
                                assert.exists(bidRate.quoteId)
                                assert.exists(bidRate.settledAmount)
                                assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                                assert.exists(bidRate.provider)
                                assert.isNotNull(bidRate.provider)
                                assert.exists(bidRate.rate)
                                assert.notEqual("0",bidRate.rate, "rate is zero")
                                assert.exists(bidRate.spotRate)
                                assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                                assert.exists(bidRate.forwardPoint)
                                assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                                assert.exists(bidRate.midRate)
                                // offer rate validation
                                assert.exists(offerRate.quoteId)
                                assert.exists(offerRate.settledAmount)
                                assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                                assert.exists(offerRate.provider)
                                assert.isNotNull(offerRate.provider)
                                assert.exists(offerRate.rate)
                                assert.notEqual("0",offerRate.rate, "rate is zero")
                                assert.exists(offerRate.spotRate)
                                assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                                assert.exists(offerRate.forwardPoint)
                                assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                                assert.exists(offerRate.midRate)

                                assert.exists(rfsActiveQuote.effectiveTime)
                                assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                                assert.equal(rfsData.priceTypeNdfSwap, rfsActiveQuote.priceType)
                                assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                                assert.equal('A', rfsActiveQuote.status)

                                assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                                assert.equal(dealtAmt, bidRate.dealtAmount)
                                assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                                assert.equal(dealtAmt, offerRate.dealtAmount)
                           }); //it rate test


                      describe ("NDF Trade function, Buy, trade reject, incorrect symbol in trade request message", function() {

                                                    before(function(done){

                                                              var tradereq = [{
                                                                              quoteId: offerRate.quoteId,
                                                                              side: "BUY",
                                                                              symbol: rfsData.symbolNdf,  //no validations done
                                                                              dealtCurrency: rfsData.baseCcyNdf,  //no validations done
                                                                              tradeChannel: "DNET/RFS/BB",    //no validations done
                                                                              clOrderId: "", //rfsActiveQuote.systemReqId
                                                                      }]
                                                                      var wstradereq = { rfsTrades : tradereq }
                                                                      console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                                                      connection.send(JSON.stringify(wstradereq));

                                                                      connection.onmessage = (e1) => {
                                                                      //{"rfsResponses":[{"rfsEvent":"TRADE_REQUEST_REJECTED","rfsMessage":{"eventTime":"2021/10/25 13:56:42"},"errorCode":"QUOTE_NOT_AVAILABLE"}]}
                                                                          traderes = JSON.parse(e1.data)
                                                                          console.log("Error response is \n"+JSON.stringify(traderes));
                                                                          if(traderes.errors)
                                                                           {
                                                                              errorMSG = traderes.errors[0];
                                                                              //console.log("Error response is \n" +JSON.stringify(rfsTradeAckMSG));
                                                                              done()
                                                                          }
                                                                      } //on message


                                                                      }); //before

                                               it("NDF Trade it test, trade reject, incorrect symbol in trade req msg", function(){
                                                       //[{"errorCode":1,"errorMessage":"Not a valid request."}]
                                                                console.log("Trade reject test passed \n" +JSON.stringify(traderes));
                                                                assert.equal(JSON.stringify(traderes.errors[0].errorMessage),'"Not a valid request."',"The error message is incorrect");
                                                                assert.equal(JSON.stringify(traderes.errors[0].errorCode),1,"The error code is incorrect");
                                                        });//it trade reject test

                            }); //describe trade reject function close

              });  //decribe 2-way rate test, trade reject TC
          */
    }); //describe neg scenarios TC
 } //rfs NDF Swap Neg scenarios function let

rfsNDFSwapTradeTC();
rfsNDFSwapTradeNegTC();
