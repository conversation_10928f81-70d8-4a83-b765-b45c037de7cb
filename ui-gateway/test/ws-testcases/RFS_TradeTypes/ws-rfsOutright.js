const assert = require('chai').assert
const expect = require('chai').expect
const WebSocket = require('ws')

//const login = require('../login').login
const env = require('../../config/properties').env
const rfsData = require('../../config/properties').rfsData

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId
let systemReqId
let rfsWithdrawResponse
let rfsInactiveQuote
let rfsActiveQuote
let rfsSubscriptionAck
let rfsWithdrawAck
let res
let errors
let Host
let apikey

// Login credentials should be for MDF enabled org
// For marketdata scripts, org should be getting rates in MDF
// Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

let wsconnect = function (done, cookies) {
    //const websocket_url = 'ws://' + env.kongHost + ':' + env.kongPort + '/fxstream'
    const websocket_url = 'wss://' + env.hostname +  '/v2/fxstream'
    connection = new WebSocket(websocket_url, [], {
        'headers': {
			'Host': env.apiHost,
			'apikey': env.apikey
        }
    })

    connection.onopen = () => {

        done()
    }

    connection.onerror = (error) => {
        console.log(`WebSocket error: ${error}`)
    }
}

let rfsOutrightTC = function(){

    describe("RFS outright ", function () {

        before(function (done) {
            wsconnect(done);
        });

        after(function () {
            connection.close()
        });
        let reqId = Math.floor(Math.random() * 100)

        describe("Subscription test ", function () {
        // Validate all the messages in the workflow except rate
        //// rfsOutrightSubscriptions: request : {"rfsSubscriptions":[{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"EUR","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"TWO_WAY","priceType":"Outright","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":70}]}
            let dealtAmt = '1000000'
            let amount = '1000000.0'

            before(function (done) {
                console.log('*************************** rfs outright - Two-Way Subscription Rate test ************************** ' + new Date());
                tempReqId = "RFS_OR_Subscription_" + reqId
                console.log("Rfs outright - Subscription Rate Test -> reqId = " + reqId)
                var subrequests = [{
                     symbol : rfsData.symbol,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcy,
                     expiry: rfsData.expiry,
                     nearValueDate : rfsData.nlTenor,
                     fixingDate : "" ,
                     side : rfsData.sideType2Way,
                     priceType : rfsData.priceTypeOR,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth : rfsData.depth,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Subscription Rate Test ->  rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                rfsActiveQuote = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Subscription Rate Test ->  res : " + JSON.stringify(res))
                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 2 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i === 2) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                    } else if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        systemReqId = rfsSubscriptionResponses.requestId
                    } else if (res.rfsWithdrawAck) {
                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                        if (flag) { done() }
                        flag = true
                    } else if (res.rfsResponses) {
                        rfsWithdrawResponse = res.rfsResponses[0]
                        if(flag) { done()}
                        flag = true

                    }
                }
            });

            // quote : {"requestId":"G4796976d517c17b230031f5f","priceType":"Outright","effectiveTime":0,"symbol":"EUR/USD","ttl":119,"dealtCurrency":"EUR","status":"A","nearSettleDate":"10/05/2021","bids":[{"legType":0,"quoteId":"G-4796976cf-17c17b231f8-SGR-44-pfOrg-SG-1632485126656","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,440.00","provider":"SG","rate":1.18644,"spotRate":1.18634,"forwardPoint":0.0001,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b23118-UBSA-37f-pfOrg-UBS-1632485126434","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,300.00","provider":"UBS","rate":1.1863,"spotRate":1.1862,"forwardPoint":0.0001,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c17b23118-UBSA-37f-pfOrg-UBS-1632485126434","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,730.00","provider":"UBS","rate":1.18673,"spotRate":1.18653,"forwardPoint":0.0002,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b231f8-SGR-44-pfOrg-SG-1632485126656","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,740.00","provider":"SG","rate":1.18674,"spotRate":1.18654,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
            it("Subscription test", function () {
                if(rfsSubscriptionAck != "") {
                    // validate    rfsSubscriptionAck
                    console.log("============Rfs outright - Subscription Rate Test -> rfsSubscriptionAck : ")
                    console.log(rfsSubscriptionAck)
                    assert.equal('EUR/USD', rfsSubscriptionAck.request.symbol)
                    assert.equal(amount, rfsSubscriptionAck.request.amount)
                    assert.equal(rfsData.baseCcy, rfsSubscriptionAck.request.dealtCurrency)
                    assert.equal(rfsData.expiry, rfsSubscriptionAck.request.expiry)
                    assert.equal(rfsData.nlTenor, rfsSubscriptionAck.request.nearValueDate)
                    assert.exists(rfsSubscriptionAck.request.fixingDate)
                    assert.equal(rfsData.sideType2Way, rfsSubscriptionAck.request.side)
                    assert.equal(rfsData.priceTypeOR, rfsSubscriptionAck.request.priceType)
                    assert.equal(rfsData.customerAccount, rfsSubscriptionAck.request.customerAccount)
                    assert.equal(rfsData.customerOrg, rfsSubscriptionAck.request.customerOrg)
                    assert.equal(rfsData.aggregatedView, rfsSubscriptionAck.request.priceViewType)
                    assert.equal(rfsData.depth, rfsSubscriptionAck.request.depth)
                    //assert.equal(rfsData.providers, rfsSubscriptionAck.request.providers)
                    assert.exists(rfsSubscriptionAck.request.providers)
                    assert.equal(rfsData.channel, rfsSubscriptionAck.request.channel)
                    assert.exists(rfsSubscriptionAck.request.clOrderId)

                     // validate rfsSubscriptionResponses
                    refData = rfsSubscriptionResponses.refData
                    rfsMessage = rfsSubscriptionResponses.rfsMessage
                    console.log("Rfs outright - Subscription Rate Test -> refData : " )
                    console.log("====================" )
                    console.log(rfsMessage)
                    assert.equal('EUR/USD', refData.instrument)
                    assert.exists(refData.spotPrecision)
                    assert.exists(refData.spotPointsPrecision)
                    assert.exists(refData.forwardPointsPrecision)
                    assert.exists(refData.pipsFactor)
                    assert.exists(refData.instrumentType)
                    assert.exists(refData.nonDeliverable)
                    assert.exists(refData.spotValueDate)
                    assert.exists(refData.inverseSpotPrecision)
					assert.exists(refData.inverseSpotPointsPrecision)
					assert.exists(refData.inverseForwardPointsPrecision)
					assert.exists(refData.inversePipsFactor)
					assert.exists(refData.nonSpotSettlementType)
					assert.exists(rfsSubscriptionResponses.requestId)
					assert.exists(rfsSubscriptionResponses.transactionId)
					assert.exists(rfsSubscriptionResponses.expiryTimeInSeconds)
					assert.equal(rfsSubscriptionResponses.expiry, refData.expiryTimeInSeconds)
					assert.exists(rfsSubscriptionResponses.rfsMessage)
    				assert.exists(rfsMessage.eventTime)
					assert.exists(rfsMessage.eventName)
					assert.equal('RFS Submitted', rfsMessage.eventName)
					assert.exists(rfsMessage.eventDetails)

                    // validate withdraw ack
                    //{"rfsWithdrawAck":[{"request":{"requestId":"G4796976d3188d405e47a7ffe"},"status":"received"}]}
                    console.log("======rfsWithdrawAck=========="+JSON.stringify(rfsWithdrawAck))
                    assert.exists(rfsWithdrawAck.request.requestId)
					assert.equal('received', rfsWithdrawAck.status)

                    // validate rfsResponses (rfsWithdrawResponse)
                    assert.exists(rfsWithdrawResponse.requestId)
					assert.equal('REQUEST_WITHDRAWN', rfsWithdrawResponse.rfsEvent)
                    assert.exists(rfsWithdrawResponse.rfsMessage)
                    assert.exists(rfsWithdrawResponse.rfsMessage.eventTime)
                    assert.exists(rfsWithdrawResponse.rfsMessage.eventName)
                    assert.equal('RFS Withdrawn',rfsWithdrawResponse.rfsMessage.eventName)
                    assert.exists(rfsWithdrawResponse.rfsMessage.eventDetails)
                    assert.exists(rfsWithdrawResponse.clOrderId)


                } else {
                    console.log("***** rfs outright - Two-Way Subscription Rate test -> Not received required msgs")
                    assert.equal(true,false)
                }

           });

        });

        describe("Two-Way Rate test ", function () {
        //// rfsOutrightSubscriptions: request : {"rfsSubscriptions":[{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"EUR","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"TWO_WAY","priceType":"Outright","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":70}]}
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** rfs outright - Two-Way Rate test ************************** ' + new Date());
                tempReqId = "RFS_OR_TwoWay_Rate_" + reqId
                console.log("Rfs outright - Two-Way RateTest -> reqId = " + reqId)
                var subrequests = [{
                     symbol : rfsData.symbol,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcy,
                     expiry: rfsData.expiry,
                     nearValueDate : rfsData.nlTenor,
                     fixingDate : "" ,
                     side : rfsData.sideType2Way,
                     priceType : rfsData.priceTypeOR,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth : rfsData.depth,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Two-Way RateTest ->  rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                systemReqId = ""
                flag = false

                rfsActiveQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - Two-Way RateTest ->  res : " + JSON.stringify(res))

                    if (res.rfsRates && res.rfsRates[0].requestId === systemReqId) {
                        rate = res.rfsRates[0]
                        if(i < 2 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i === 2) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                     } else if (res.rfsSubscriptionAck) {
                         rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                     } else if (res.rfsSubscriptionResponses) {
                         rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                         systemReqId = rfsSubscriptionResponses.requestId
                     } else if (res.rfsWithdrawAck) {
                         rfsWithdrawAck = res.rfsWithdrawAck[0]
                         if (flag) { done() }
                         flag = true
                     } else if (res.rfsResponses) {
                         rfsWithdrawResponse = res.rfsResponses[0]
                         if(flag) { done()}
                         flag = true
                     }
                }
            });
            it("Two-Way Rate test", function () {
                if(rfsActiveQuote != "") {
                    console.log("Rfs outright - Two-Way RateTest -> quote : ")
                    console.log(rfsActiveQuote)
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeOR, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)
                    assert.exists(rfsActiveQuote.nearValueDate)

                    // bid rate validation
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    assert.equal('0', bidRate.legType)
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    console.log("===========" + bidRate.forwardPoint)
                    assert.notEqual('0',bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.equal('0', offerRate.legType)
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual('0',offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate)
                    assert.exists(rfsActiveQuote.mids)
                } else {
                    console.log("***** rfs outright - Two-Way Rate test -> No active quote available")
                    assert.equal(true,false)
                }
           });

        });

        describe("One-Way-Bid Rate test ", function () {
            // //rfsOutrightSubscriptions: request : {"rfsSubscriptions":[{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"EUR","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"BUY","priceType":"Outright","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":66}]}
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS outright - One-Way-Bid Rate test ************************** ' + new Date());
                tempReqId = "RFS_OR_OneWay_Bid_Rate_" + reqId
                var subrequests = [{
                    symbol : rfsData.symbol,
                    amount : "1000000.0",
                    dealtCurrency : rfsData.baseCcy,
                    expiry: rfsData.expiry,
                    nearValueDate : rfsData.nlTenor,
                    fixingDate : "" ,
                    side : rfsData.sideTypeBuy,
                    priceType : rfsData.priceTypeOR,
                    customerAccount : rfsData.customerAccount,
                    customerOrg: rfsData.customerOrg,
                    priceViewType: rfsData.aggregatedView,
                    depth : rfsData.depth,
                    channel : rfsData.channel,
                    providers: rfsData.providers,
                    clOrderId: tempReqId
                }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - One-Way-Bid Rate test  ->  rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                rfsActiveQuote = ""
                systemReqId = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - One-Way-Bid Rate RateTest ->  res : " + JSON.stringify(res))

                    if (res.rfsRates && res.rfsRates[0].requestId === systemReqId) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            console.log("Rfs outright - One-Way-Bid Rate test  ->  rate response : " + JSON.stringify(rate))
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                    } else if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        systemReqId = rfsSubscriptionResponses.requestId
                    } else if (res.rfsWithdrawAck) {
                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                        if (flag) { done() }
                        flag = true
                    } else if (res.rfsResponses) {
                        rfsWithdrawResponse = res.rfsResponses[0]
                        if(flag) { done()}
                        flag = true

                    }
                }
            });

            //  quote : {"requestId":"G4796976d517c17b7c0871f8a","priceType":"Outright","effectiveTime":0,"symbol":"EUR/USD","ttl":119,"dealtCurrency":"EUR","status":"A","nearSettleDate":"10/05/2021","bids":[],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c17b7c2fd-NTFXA-3a1-pfOrg-NTFX-1632485491463","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,590.00","provider":"NTFX","rate":1.18659,"spotRate":1.18639,"forwardPoint":0.0002,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b7c28e-UBSA-3a0-pfOrg-UBS-1632485491355","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,730.00","provider":"UBS","rate":1.18673,"spotRate":1.18653,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
            it("One-Way-Bid Rate test", function () {
                if(rfsActiveQuote != "") {
                    console.log("Rfs outright - One-Way-Bid Rate test -> quote : " )
                    console.log(rfsActiveQuote)
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeOR, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)
                    assert.exists(rfsActiveQuote.nearValueDate)
                    assert.exists(rfsActiveQuote.mids)
                    // offer rate validation
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.equal('0', offerRate.legType)
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate)
                } else {
                    console.log("***** Rfs outright - One-Way-Bid Rate test -> No active quote available")
                    assert.equal(true,false)
                }
            });

        });

        describe("One-Way-Offer Rate test ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS outright - One-Way-Offer Rate test ************************** ' + new Date());
                tempReqId = "RFS_OR_OneWay_Offer_Rate_" + reqId
                var subrequests = [{
                    symbol : rfsData.symbol,
                    amount : "1000000.0",
                    dealtCurrency : rfsData.baseCcy,
                    expiry: rfsData.expiry,
                    nearValueDate : rfsData.nlTenor,
                    fixingDate : "" ,
                    side : rfsData.sideTypeSell,
                    priceType : rfsData.priceTypeOR,
                    customerAccount : rfsData.customerAccount,
                    customerOrg: rfsData.customerOrg,
                    priceViewType: rfsData.aggregatedView,
                    depth : rfsData.depth,
                    channel : rfsData.channel,
                    providers: rfsData.providers,
                    clOrderId: tempReqId
                }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - One-Way-Offer Rate test  ->  rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1

                rfsActiveQuote = ""
                systemReqId = ""
                flag = false
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - One-Way-Offer Rate test  ->  res : " + JSON.stringify(res))

                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                    } else if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        systemReqId = rfsSubscriptionResponses.requestId
                    } else if (res.rfsWithdrawAck) {
                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                        if (flag) { done() }
                        flag = true
                    } else if (res.rfsResponses) {
                        rfsWithdrawResponse = res.rfsResponses[0]
                        if(flag) { done()}
                        flag = true

                    }
                }
            });

    //Rfs outright - One-Way-Offer Rate test  ->  rate response : {"requestId":"G4796976d517c17bc28cb1fd5","priceType":"Outright","effectiveTime":0,"symbol":"EUR/USD","ttl":9,"dealtCurrency":"EUR","status":"A","nearSettleDate":"10/05/2021","bids":[{"legType":0,"quoteId":"G-4796976cf-17c17bc2b17-UBSA-3af-pfOrg-UBS-1632485780256","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,300.00","provider":"UBS","rate":1.1863,"spotRate":1.1862,"forwardPoint":0.0001,"midRate":0}],"offers":[],"mids":[]}
            it("One-Way-Offer Rate test", function () {
                if(rfsActiveQuote != "") {
                    console.log("Rfs outright - One-Way-Offer Rate test -> quote : ")
                    console.log(rfsActiveQuote)
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeOR, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)
                    assert.exists(rfsActiveQuote.nearValueDate)

                    assert.lengthOf(rfsActiveQuote.offers,0)
                    assert.lengthOf(rfsActiveQuote.mids,0)

                    // bid rate validation
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    assert.equal('0', bidRate.legType)
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID', bidRate.type)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate)
                } else {
                    console.log("***** RFS outright - One-Way-Offer Rate test -> No active quote available")
                    assert.equal(true,false)
                }
            });

        });

        describe("Withdraw response test ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS outright - Withdraw response test ************************** ' + new Date());
                tempReqId = "RFS_OR_Withdraw_Rate_" + reqId
                var subrequests = [{
                    symbol : rfsData.symbol,
                    amount : "1000000.0",
                    dealtCurrency : rfsData.baseCcy,
                    expiry: "60", // rfsData.expiry, keep enough time to withdraw
                    nearValueDate : rfsData.nlTenor,
                    fixingDate : "" ,
                    side : rfsData.sideType2Way,
                    priceType : rfsData.priceTypeOR,
                    customerAccount : rfsData.customerAccount,
                    customerOrg: rfsData.customerOrg,
                    priceViewType: rfsData.aggregatedView,
                    depth : rfsData.depth,
                    channel : rfsData.channel,
                    providers: rfsData.providers,
                    clOrderId: tempReqId
                }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - Withdraw response Test ->  rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                rfsWithdrawResponse = ""
                systemReqId = ""
                flag = false

                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 2 && rate.status === "A") {
                            console.log("Rfs outright - Withdraw response Test ->  rate response : " + JSON.stringify(rate))
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i ===2) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                    } else if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        systemReqId = rfsSubscriptionResponses.requestId
                    } else if (res.rfsWithdrawAck) {
                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                        if (flag) { done() }
                        flag = true
                    } else if (res.rfsResponses) {
                        rfsWithdrawResponse = res.rfsResponses[0]
                        if(flag) { done()}
                        flag = true

                    }
                }
            });

            //rfsWithdrawResponse : {"requestId":"G4796976d517c27fc75c2221","rfsEvent":"REQUEST_WITHDRAWN","rfsMessage":{"eventTime":"2021/09/27 16:00:29","eventName":"RFS Withdrawn","eventDetails":"RFS Request Withdrawn for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date 1W"},"clOrderId":"G4796976d517c27fc75c2221"}
            it("Withdraw response test", function () {
                if(rfsWithdrawResponse != "") {
                    assert.exists(rfsWithdrawResponse.requestId)
                    assert.equal('REQUEST_WITHDRAWN', rfsWithdrawResponse.rfsEvent)
                    assert.exists(rfsWithdrawResponse.rfsMessage.eventTime)
                    assert.equal('RFS Withdrawn', rfsWithdrawResponse.rfsMessage.eventName)
                    expect(JSON.stringify(rfsWithdrawResponse.rfsMessage.eventDetails)).to.have.string('"RFS Request Withdrawn for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date 1W');
                    assert.exists(rfsWithdrawResponse.clOrderId)
                } else {
                    console.log("***** RFS outright - Withdraw response test -> Withdraw request not received")
                    assert.equal(true,false)
                }
            });

        });

        describe("Two-Way-Term Rate test ", function () {
        //// rfsOutrightSubscriptions: request : {"rfsSubscriptions":[{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"EUR","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"TWO_WAY","priceType":"Outright","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":70}]}
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS outright - Two-Way-Term Rate test ************************** ' + new Date());
                tempReqId = "RFS_OR_TwoWay_Term_Rate_" + reqId
                console.log("Rfs outright - Two-Way RateTest -> reqId = " + reqId)
                var subrequests = [{
                     symbol : rfsData.symbol,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.termCcy,
                     expiry: rfsData.expiry,
                     nearValueDate : rfsData.nlTenor,
                     fixingDate : "" ,
                     side : rfsData.sideType2Way,
                     priceType : rfsData.priceTypeOR,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth : rfsData.depth,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - RFS_OR_TwoWay_Term RateTest ->  rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                systemReqId = ""
                flag = false
                rfsActiveQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - RFS_OR_TwoWay_Term RateTest ->  res : " + JSON.stringify(res))

                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                    } else if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        systemReqId = rfsSubscriptionResponses.requestId
                    } else if (res.rfsWithdrawAck) {
                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                        if (flag) { done() }
                        flag = true
                    } else if (res.rfsResponses) {
                        rfsWithdrawResponse = res.rfsResponses[0]
                        if(flag) { done()}
                        flag = true

                    }                }
            });
            // quote : {"requestId":"G4796976d517c17b230031f5f","priceType":"Outright","effectiveTime":0,"symbol":"EUR/USD","ttl":119,"dealtCurrency":"EUR","status":"A","nearSettleDate":"10/05/2021","bids":[{"legType":0,"quoteId":"G-4796976cf-17c17b231f8-SGR-44-pfOrg-SG-1632485126656","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,440.00","provider":"SG","rate":1.18644,"spotRate":1.18634,"forwardPoint":0.0001,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b23118-UBSA-37f-pfOrg-UBS-1632485126434","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,300.00","provider":"UBS","rate":1.1863,"spotRate":1.1862,"forwardPoint":0.0001,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c17b23118-UBSA-37f-pfOrg-UBS-1632485126434","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,730.00","provider":"UBS","rate":1.18673,"spotRate":1.18653,"forwardPoint":0.0002,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b231f8-SGR-44-pfOrg-SG-1632485126656","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,740.00","provider":"SG","rate":1.18674,"spotRate":1.18654,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
            it("Two-Way-Term Rate test", function () {
                if(rfsActiveQuote != "") {
                    console.log("Rfs outright - RFS_OR_TwoWay_Term RateTest -> quote : ")
                    console.log(rfsActiveQuote)
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeOR, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)
                    assert.exists(rfsActiveQuote.nearValueDate)

                    // bid rate validation
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    assert.equal('0', bidRate.legType)
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.equal('0', offerRate.legType)
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate)
                    assert.exists(rfsActiveQuote.mids)
                } else {
                    console.log("***** RFS outright - Two-Way-Term Rate test -> No active quote available")
                    assert.equal(true,false)
                }
           });

        });

        describe("One-Way-Offer-Term Rate test ", function () {
            // //rfsOutrightSubscriptions: request : {"rfsSubscriptions":[{"symbol":"EUR/USD","amount":"1000000.0","dealtCurrency":"EUR","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"BUY","priceType":"Outright","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":66}]}
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS outright - One-Way-Offer-Term Rate test ************************** ' + new Date());
                tempReqId = "RFS_OR_OneWay_Offer_Term_Rate_" + reqId
                var subrequests = [{
                    symbol : rfsData.symbol,
                    amount : "1000000.0",
                    dealtCurrency : rfsData.termCcy,
                    expiry: rfsData.expiry,
                    nearValueDate : rfsData.nlTenor,
                    fixingDate : "" ,
                    side : rfsData.sideTypeSell,
                    priceType : rfsData.priceTypeOR,
                    customerAccount : rfsData.customerAccount,
                    customerOrg: rfsData.customerOrg,
                    priceViewType: rfsData.aggregatedView,
                    depth : rfsData.depth,
                    channel : rfsData.channel,
                    providers: rfsData.providers,
                    clOrderId: tempReqId
                }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - One-Way-Offer-Term Rate test  ->  rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                systemReqId = ""
                flag = false
                rfsActiveQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - One-Way-Offer-Term Rate RateTest ->  res : " + JSON.stringify(res))

                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            console.log("Rfs outright - One-Way-Offer-Term Rate test  ->  rate response : " + JSON.stringify(rate))
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                    } else if (res.rfsSubscriptionAck) {
                         rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                    } else if (res.rfsSubscriptionResponses) {
                         rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                         systemReqId = rfsSubscriptionResponses.requestId
                    } else if (res.rfsWithdrawAck) {
                         rfsWithdrawAck = res.rfsWithdrawAck[0]
                         if (flag) { done() }
                         flag = true
                    } else if (res.rfsResponses) {
                         rfsWithdrawResponse = res.rfsResponses[0]
                         if(flag) { done()}
                         flag = true

                    }
                }
            });

            //  quote : {"requestId":"G4796976d517c17b7c0871f8a","priceType":"Outright","effectiveTime":0,"symbol":"EUR/USD","ttl":119,"dealtCurrency":"EUR","status":"A","nearSettleDate":"10/05/2021","bids":[],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c17b7c2fd-NTFXA-3a1-pfOrg-NTFX-1632485491463","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,590.00","provider":"NTFX","rate":1.18659,"spotRate":1.18639,"forwardPoint":0.0002,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b7c28e-UBSA-3a0-pfOrg-UBS-1632485491355","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,730.00","provider":"UBS","rate":1.18673,"spotRate":1.18653,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
            it("One-Way-Offer-Term Rate test", function () {
                if(rfsActiveQuote != "") {
                    console.log("Rfs outright - One-Way-Offer-Term Rate test -> quote : " )
                    console.log(rfsActiveQuote)
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeOR, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)
                    assert.exists(rfsActiveQuote.nearValueDate)
                    assert.exists(rfsActiveQuote.mids)
                    // offer rate validation
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.equal('0', offerRate.legType)
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER', offerRate.type)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate)
                } else {
                    console.log("***** RFS outright - One-Way-Offer-Term Rate test -> No active quote available")
                    assert.equal(true,false)
                }
            });

        });

        describe("One-Way-Bid-Term Rate test ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS outright - One-Way-Bid-Term Rate test ************************** ' + new Date());
                tempReqId = "RFS_OR_OneWay_Bid_Term_Rate_" + reqId
                var subrequests = [{
                    symbol : rfsData.symbol,
                    amount : "1000000.0",
                    dealtCurrency : rfsData.termCcy,
                    expiry: rfsData.expiry,
                    nearValueDate : rfsData.nlTenor,
                    fixingDate : "" ,
                    side : rfsData.sideTypeBuy,
                    priceType : rfsData.priceTypeOR,
                    customerAccount : rfsData.customerAccount,
                    customerOrg: rfsData.customerOrg,
                    priceViewType: rfsData.aggregatedView,
                    depth : rfsData.depth,
                    channel : rfsData.channel,
                    providers: rfsData.providers,
                    clOrderId: tempReqId
                }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("Rfs outright - One-Way-Bid-Term Rate test  ->  rfsOutrightSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                systemReqId = ""
                flag = false
                rfsActiveQuote = ""
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("Rfs outright - One-Way-Bid-Term Rate test  ->  res : " + JSON.stringify(res))

                    if (res.rfsRates) {
                        rate = res.rfsRates[0]
                        if(i < 3 && rate.status === "A") {
                            rfsActiveQuote = rate
                            i= i + 1
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            rfsInactiveQuote = res.rfsRates[0]
                        } else if (i ===3) {
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                            i++
                        }
                    } else if (res.rfsSubscriptionAck) {
                        rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                    } else if (res.rfsSubscriptionResponses) {
                        rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                        systemReqId = rfsSubscriptionResponses.requestId
                    } else if (res.rfsWithdrawAck) {
                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                        if (flag) { done() }
                        flag = true
                    } else if (res.rfsResponses) {
                        rfsWithdrawResponse = res.rfsResponses[0]
                        if(flag) { done()}
                        flag = true

                    }
                }
            });

    //Rfs outright - One-Way-Bid-Term Rate test  ->  rate response : {"requestId":"G4796976d517c17bc28cb1fd5","priceType":"Outright","effectiveTime":0,"symbol":"EUR/USD","ttl":9,"dealtCurrency":"EUR","status":"A","nearSettleDate":"10/05/2021","bids":[{"legType":0,"quoteId":"G-4796976cf-17c17bc2b17-UBSA-3af-pfOrg-UBS-1632485780256","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,300.00","provider":"UBS","rate":1.1863,"spotRate":1.1862,"forwardPoint":0.0001,"midRate":0}],"offers":[],"mids":[]}
            it("One-Way-Bid-Term Rate test ", function () {
                if(rfsActiveQuote != "") {
                    console.log("Rfs outright - One-Way-Bid-Term Rate test -> quote : ")
                    console.log(rfsActiveQuote)
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeOR, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl)
                    assert.equal(rfsData.termCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)
                    assert.exists(rfsActiveQuote.nearValueDate)

                    assert.lengthOf(rfsActiveQuote.offers,0)
                    assert.lengthOf(rfsActiveQuote.mids,0)

                    // bid rate validation
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    assert.equal('0', bidRate.legType)
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID', bidRate.type)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate)
                } else {
                    console.log("***** RFS outright - One-Way-Bid-Term Rate test -> No active quote available")
                    assert.equal(true,false)
                }
            });
        });

    });
};

rfsOutrightTC();
