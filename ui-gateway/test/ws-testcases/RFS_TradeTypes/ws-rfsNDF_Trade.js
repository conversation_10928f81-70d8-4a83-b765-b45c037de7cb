const assert = require('chai').assert
const expect = require('chai').expect
const WebSocket = require('ws')

//const login = require('../login').login
const env = require('../../config/properties').env
const rfsData = require('../../config/properties').rfsData
const userData = require('../../config/properties').user

let connection
let rateSubscriptionResponses
let rateUnsubscriptionResponses
let reqId
let systemReqId
let rfsWithdrawResponse
let rfsInactiveQuote
let rfsActiveQuote
let rfsSubscriptionAck
let rfsTrade
let res
let errors

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let getBusinessDayDates = function() {
    let today = new Date();

    // Get today's date in the format YYYY-MM-DD
    let fromValueDate = new Date(today);

    // Get the 'to' date, initially set to today + 4 days
    let toDate = new Date(today);
    toDate.setDate(today.getDate() - 2);

    // Function to check if a date is Saturday or Sunday
    function isWeekend(date) {
        return date.getDay() === 6 || date.getDay() === 0;  // 6 = Saturday, 0 = Sunday
    }

    // Adjust 'toDate' if it falls on a weekend
    while (isWeekend(toDate)) {
        toDate.setDate(toDate.getDate() + 1); // Move to next day
    }

       // Adjust 'fromValueDate' if it falls on a weekend
        while (isWeekend(fromValueDate)) {
            fromValueDate.setDate(fromValueDate.getDate() + 1); // Move to next day
        }

    // Format toDate in YYYY-MM-DD format
let toValueDate = toDate.getFullYear() + '-'
                + (toDate.getMonth() + 2).toString().padStart(2, '0') + '-'
                + toDate.getDate().toString().padStart(2, '0');

     fromValueDate = fromValueDate.getFullYear() + '-'
                        + (fromValueDate.getMonth() + 2).toString().padStart(2, '0') + '-'
                        + fromValueDate.getDate().toString().padStart(2, '0');

    // Return both the 'from' and 'to' dates
    return {
        fromDate: fromValueDate,
        toDate: toValueDate
    };
}

let rfsNDFTC = function(){

describe("RFS NDF trade ", function () {

        before(function (done) {
            wsconnect(done);
        });

        after(function () {
            connection.close()
        });

        let reqId = Math.floor(Math.random() * 100)
/*
        describe("RFS NDF trade - Two-Way Rate Offer trade test ", function () {
            let dealtAmt = '1000000'
            let rate
            before(function (done) {
                console.log('*************************** RFS NDF trade - Two-Way Rate Offer trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_Rate_OfferTrade_" + reqId
                console.log("RFS NDF - Two-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideType2Way,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId

                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote : ")
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.baseCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - Two-way quote Offer trade test", function () {
                console.log("RFS NDF Trade - Two-way quote offer trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.offers[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.offers[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal(false,trade.maker, "maker value is not correct")
                assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.baseCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)

                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD ');

            });
        });

        describe("RFS NDF trade - Two-Way Rate Bid trade test ", function () {
        //// rfsNDFSubscriptions: request : {"rfsSubscriptions":[{"symbol":"INR/USD","amount":"1000000.0","dealtCurrency":"INR","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"TWO_WAY","priceType":"NDF","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":70}]}
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS NDF trade - Two-Way Rate Bid trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_Rate_OfferTrade_" + reqId
                console.log("RFS NDF - Two-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideType2Way,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote = " )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.baseCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - Two-way quote Offer trade test", function () {
                console.log("RFS NDF Trade - Two-way quote offer trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.bids[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.bids[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal(false,trade.maker, "maker value is not correct")

                assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.baseCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)

                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 USD ');

            });
        });

        describe("RFS NDF trade - Two-Way Rate Offer term trade test ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS NDF trade - Two-Way Rate Offer term trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_Rate_OfferTermTrade_" + reqId
                console.log("RFS NDF - Two-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.termCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideType2Way,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote = " )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.termCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - Two-way quote Offer trade test", function () {
                console.log("RFS NDF Trade - Two-way quote offer trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.termAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.bids[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.bids[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal(false,trade.maker, "maker value is not correct")
                assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.termCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)

                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 INR vs.');

            });
        });

        describe("RFS NDF trade - Two-Way Rate Bid term trade test ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS NDF trade - Two-Way Rate Bid term trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_Rate_BidTermTrade_" + reqId
                console.log("RFS NDF - Two-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.termCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideType2Way,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.termCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - Two-way quote Offer trade test", function () {
                console.log("RFS NDF Trade - Two-way quote offer trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.termAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.offers[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.offers[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal(false,trade.maker, "maker value is not correct")
                assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.termCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)

                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 INR vs.');

            });
        });

        describe("RFS NDF trade - One-Way Rate Offer trade test ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS NDF trade - One-Way Rate Offer trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_Rate_OfferTrade_" + reqId
                console.log("RFS NDF - One-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideTypeBuy,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - One-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.baseCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - One-Way quote Offer trade test", function () {
                console.log("RFS NDF Trade - One-Way quote offer trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.offers[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.offers[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal(false,trade.maker, "maker value is not correct")
                assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.baseCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)

                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD vs.');

            });
        });

        describe("RFS NDF trade - One-Way Rate Bid trade test ", function () {
        //// rfsNDFSubscriptions: request : {"rfsSubscriptions":[{"symbol":"INR/USD","amount":"1000000.0","dealtCurrency":"INR","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"TWO_WAY","priceType":"NDF","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":70}]}
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS NDF trade - One-Way Rate Bid trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_Rate_OfferTrade_" + reqId
                console.log("RFS NDF - One-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideTypeSell,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - One-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote = " )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.baseCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - One-Way quote Offer trade test", function () {
                console.log("RFS NDF Trade - One-Way quote offer trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.bids[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.bids[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal(false,trade.maker, "maker value is not correct")
                assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.baseCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)

                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 USD vs.');

            });
        });

        describe("RFS NDF trade - One-Way Rate Offer  term trade test ", function () {
            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS NDF trade - One-Way Rate Offer  term trade test ************************** ' + new Date());
                tempReqId = "OR_OneWay_Rate_OfferTermTrade_" + reqId
                console.log("RFS NDF - One-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.termCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideTypeBuy,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - One-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.termCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - One-Way quote Offer term trade test", function () {
                console.log("RFS NDF Trade - One-Way quote offer term trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.termAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.bids[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.bids[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.bids[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.termCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)

                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 INR vs.');

            });
        });

        describe("RFS NDF trade - One-Way Rate Bid term trade test ", function () {

            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS NDF trade - One-Way Rate Bid term trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_Rate_OfferTermTrade_" + reqId
                console.log("RFS NDF - One-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.termCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideTypeSell,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - One-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.termCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - One-Way quote Offer term trade test", function () {
                console.log("RFS NDF Trade - One-Way quote offer term trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.termAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.offers[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.offers[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal(false,trade.maker, "maker value is not correct")
                assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.termCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)
                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 INR vs.');

            });
        });
*/

        describe("RFS NDF trade - Two-Way Rate Offer trade test - with fixing date ", function () {
            let dealtAmt = '1000000'
            let rate
            // Example of using the function
            let { fromDate, toDate } = getBusinessDayDates();
            console.log('From Date:', fromDate);
            console.log('To Date:', toDate);

            before(function (done) {
                console.log('*************************** RFS NDF trade - Two-Way Rate Offer trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_Rate_OfferTrade_" + reqId
                console.log("RFS NDF - Two-Way RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : fromDate,
                     fixingDate : toDate,
                     side : rfsData.sideType2Way,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId

                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote : ")
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.baseCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - Two-way quote Offer trade test - with fixing date", function () {
                console.log("RFS NDF Trade - Two-way quote offer trade test -> Trade details : " )
                console.log(rfsTrade)
                let trade = rfsTrade.trades[0]
                rfsMessage = rfsTrade.rfsMessage

                assert.exists(trade.executionTime)
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                assert.equal(rfsActiveQuote.offers[0].spotRate,trade.spotRate, "spotRate is not correct")
                assert.equal(rfsActiveQuote.offers[0].rate,trade.rate, "rate is not correct")
                assert.equal(rfsActiveQuote.offers[0].forwardPoints,trade.forwardPoint, "forwardPoints is not correct")
                assert.exists(trade.orderId)
                assert.exists(trade.tradeId)
                assert.equal(rfsData.priceTypeNdf,trade.tradeType, "tradeType is not NDF")
                //assert.equal(rfsData.nlTenor,trade.tenor, "tenor is not 1W")
                assert.exists(trade.tradeDate)
                assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                assert.equal(false,trade.maker, "maker value is not correct")
                assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                assert.equal("Verified",trade.status, "status is not correct")
                assert.equal(rfsData.symbolNdf,trade.symbol, "symbol is not correct")
                assert.equal(rfsData.baseCcyNdf,trade.dealtIns, "dealtIns is not correct")
                assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                assert.equal(userData.username,trade.trader, "trader is not correct")
                assert.exists(trade.counterParty)
                assert.exists(trade.cptyLongName)
                assert.exists(trade.cptyTradeId)
                assert.exists(trade.counterPartyAccount)
                //assert.exists(trade.counterpartyBLEI)
                assert.exists(trade.UPI)
                assert.exists(trade.UTI)
                assert.exists(trade.externalRequestId)
                assert.exists(trade.requestId)
                assert.exists("false",trade.isnet)
                assert.exists("false",trade.isMidMarket)
                assert.exists(trade.mifidFields)
                assert.exists(trade.sefFields)
                assert.exists(trade.channel)

                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                assert.exists(JSON.stringify(rfsMessage.eventDetails))
                expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD ');
                assert.equal(trade.fixingDate,toDate)
            });
        });


// =============== uncomment these 2 once PLT-4893, AP-11932 is fixed
        describe("AP-11932 -RFS NDF trade - One-Way Offer Rate Bid trade test ", function () {
        // quote was requested for Offer side and trade request was sent for Bid side - trade request should get rejected. But right now it is going through.
            let dealtAmt = '1000000'
            before(function (done) {
                console.log('*************************** RFS NDF trade - One-Way Offer Rate Bid trade test ************************** ' + new Date());
                tempReqId = "OR_TwoWay_OfferRate_BidTrade_" + reqId
                console.log("RFS NDF - One-Way Offer RateTest -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideTypeBuy,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - One-Way OfferRateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: rfsData.sideTypeSell,
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.baseCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        } else if (res.errors) {
                            errors = res.errors[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - One-Way Offer quote Bid trade test", function () {
                console.log("RFS NDF Trade - One-Way Offer quote Bid trade test -> Error details : " )
                console.log(errors)
                assert.equal("1",errors.errorCode, "errorCode is not correct")
                assert.equal("Not a valid request.",errors.errorMessage, "errorMessage is not correct")
            });
        });

        describe("AP-11932 -RFS NDF trade - quote request in base and trade request in term test ", function () {
        // quote was requested in baseCcyNdf and trade request was sent in termCcyNdf - trade request should get rejected (Not a valid request), but it is going thru right now, it should be corrected.

            let dealtAmt = '1000000'

            before(function (done) {
                console.log('*************************** RFS NDF trade - quote request in base and trade request in term test ************************** ' + new Date());
                tempReqId = "OR_OneWay_QuoteInBaseTradeInTerm_" + reqId
                console.log("RFS NDF - OR_OneWay_QuoteInBaseTradeInTerm -> reqId = " + tempReqId)
                var subrequests = [{
                     symbol : rfsData.symbolNdf,
                     amount : "1000000.0",
                     dealtCurrency : rfsData.baseCcyNdf,
                     expiry: rfsData.expiry,
                     nearValueDate : "1W",
                     fixingDate : "" ,
                     side : rfsData.sideTypeBuy,
                     priceType : rfsData.priceTypeNdf,
                     customerAccount : rfsData.customerAccount,
                     customerOrg: rfsData.customerOrg,
                     priceViewType: rfsData.aggregatedView,
                     depth: 5,
                     channel : rfsData.channel,
                     providers: rfsData.providers,
                     clOrderId: tempReqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS NDF - One-Way OfferRateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                i = 1
                tradeDone = "false"

                rfsActiveQuote = ""
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: rfsData.sideTypeBuy,
                                    symbol: rfsData.symbolNdf,
                                    dealtCurrency: rfsData.termCcyNdf,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request : ")
								console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
						  rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
							rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        } else if (res.errors) {
                            errors = res.errors[0]
                            done()
                        }
                    }
            });

            it("RFS NDF Trade - One-Way Offer quote Bid trade test", function () {
                console.log("RFS NDF Trade - neWay_QuoteInBaseTradeInTerm test -> Error details : " )
                console.log(errors)
                assert.equal("1",errors.errorCode, "errorCode is not correct")
                assert.equal("Not a valid request.",errors.errorMessage, "errorMessage is not correct")
            });
        });
//============ PLT-4893

    });

};

rfsNDFTC();
