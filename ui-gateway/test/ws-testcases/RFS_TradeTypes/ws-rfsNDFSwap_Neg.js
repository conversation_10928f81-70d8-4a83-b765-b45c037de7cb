    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')

    //const login = require('../login').login
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let reqId = Math.floor(Math.random() * 100)
    let dltAmtInQuote = 1000000
    let flag

    // Login credentials should be for MDF enabled org
    // For marketdata scripts, org should be getting rates in MDF
    // Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let rfsSwapNegativeTC = function(){

        describe("RFS Swap Negative scenario ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });

            //{ "rfsSubscriptions" : [ { "symbol": "USD/INR", "amount": "1000000.0", "dealtCurrency": "EUR", "expiry": 15, "nearValueDate": "1W", "farDealtAmount" : "1000000.0","farValueDate" : "2W", "fixingDate" : "" , "farFixingDate" : "", "side": "BUY", "priceType": "Swap", "customerAccount": "pfOrg", "customerOrg": "pfOrg", "priceViewType": 1, "depth": 2, "channel": "DNET/RFS/BB", "providers": ["NTFX","MSFX","SG","SUCD","UBS","WFNA"], "clOrderId": "view1MultiLP2" } ] }


            describe("Invalid CP ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid CP ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidCP_" + reqId
                    console.log("Rfs Swap - Invalid CP - > reqId = " + reqId)
                    var subrequests = [{
                                 symbol : 'ABC/ACB',
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid CP - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid CP - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid CP - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid CP - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                it("Invalid CP", function () {
                    console.log("Rfs Swap - Invalid CP - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                    console.log("Rfs Swap - Invalid CP - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.InvalidCurrency"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
              });

            });

            describe("Invalid Amt ", function () {
            //{"expiryTimeInSeconds":0,"clOrderId":"2","status":"ERROR"}
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid Amt ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidAmt_" + reqId
                    console.log("Rfs Swap - Invalid Amt - > reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "abc",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid Amt - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid Amt - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid Amt - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid Amt - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                //"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

                it("Invalid Amt", function () {
                    console.log("Rfs Swap - Invalid Amt - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                    console.log("Rfs Swap - Invalid Amt - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
              });

            });

            describe("Invalid dealtCurrency ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid dealtCurrency ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidDltCcy_" + reqId
                    console.log("Rfs Swap - Invalid dealtCurrency -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "ABC",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid dealtCurrency - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid dealtCurrency - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

//==========Rfs Spot - rate response : {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,
//"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

                it("Invalid dealt ccy", function () {
                    console.log("Rfs Swap - Invalid dealtCurrency - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                    console.log("Rfs Swap - Invalid dealtCurrency - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.InvalidCurrency"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                });

            });

            describe("Invalid nearValueDate ", function () {
// AP-10389
// RFSSpot is ignoring value date, it allows 1W etc tenors too in the request, but actually considers as spot request based on priceType
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid nearValueDate ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidNrVD_" + reqId
                    console.log("Rfs Swap - Invalid nearValueDate : rate - reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "abc",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid nearValueDate -> rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid near valueDate - > res : " + JSON.stringify(res))
                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

//==========Rfs Spot - rate response : {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,
//"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

                it("Invalid nearValueDate", function () {
                    console.log("Rfs Swap - Invalid dealtCurrency - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                    console.log("Rfs Swap - Invalid dealtCurrency - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"INCORRECT_REQUEST_PARAMS"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                });

            });

            describe("Without nearValueDate", function () {
// RFSSpot is ignoring value date, it allows 1W etc tenors too in the request, but actually considers as spot request based on priceType
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Without nearValueDate ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_WithoutNrVD_" + reqId
                    console.log("Rfs Swap - Without nearValueDate : rate - reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
//                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Without nearValueDate -> rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Without nearValueDate - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Without nearValueDate - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Without nearValueDate - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for USD/INR with Dealt currency EUR. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
                it("Without nearValueDate", function () {
                    console.log("Rfs Swap - Without nearValueDate - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.ValueDateNotSpecified"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))

               });
            });

            describe("Without fixingDate", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Without fixingDate ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_WithoutFixingDate_" + reqId
                    console.log("Rfs Swap - Without fixingDate : rate - reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "",
  								 farDealtAmount : "1000000.0",
  								 farValueDate : "",
                                 fixingDate : "" ,
  								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Without fixingDate -> rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Without fixing date & valueDates - > res : " + JSON.stringify(res))
                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for USD/INR with Dealt currency EUR. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
                it("Without fixingDate", function () {
                     console.log("Rfs Swap - Without fixingDate - > rfsSubscriptionResponses = " + rfsActiveQuote)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.ValueDateNotSpecified"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))

               });
            });

            describe("Invalid side ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid side ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidSide_" + reqId
                    console.log("Rfs Swap - Invalid side -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : "ONE_WAY",
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid side - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid side - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid side - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid side - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        } else if (res.errors) {
                            errors = res.errors
                            console.log("errors : "+ JSON.stringify(errors))
                            done()
                        }
                    }
                });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                it("Invalid Side", function () {
                    console.log("Rfs Swap - Invalid side - > errors = " + JSON.stringify(errors))
                    assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                    assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                });
            });

            describe("Invalid priceType ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid priceType ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidPriceType" + reqId
                    console.log("Rfs Swap - Invalid priceType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : "xspot",
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid priceType - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid priceType - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid priceType - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid priceType - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        } else if (res.errors) {
                            errors = res.errors
                            console.log("errors : "+ JSON.stringify(errors))
                            done()
                        }
                    }
                });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                it("Invalid priceType", function () {
                    console.log("Rfs Swap - Invalid priceType - > errors = " + JSON.stringify(errors))
                    assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                    assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                });
            });

            describe("Invalid customerAccount ", function () {
          // AP-10414 -> It should get rejected, but working fine.
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid customerAccount ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidCustomerAcct_" + reqId
                    console.log("Rfs Swap - Invalid customerAccount -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : "abc",
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid customerAccount - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid customerAccount - > res : " + JSON.stringify(res))
                        if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid CP - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                it("Invalid customerAccount", function () {
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.InvalidAccount"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                });
            });

            describe("Invalid customerOrg ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid customerOrg ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidCustomer_" + reqId
                    console.log("Rfs Swap - Invalid customerOrg -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: "ABCOrg",
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid customerOrg - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid customerOrg - > res : " + JSON.stringify(res))
                        if (res.rfsSubscriptionResponses) {
                             rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                             console.log("Rfs Swap - Invalid CP - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                             done()
                         }
                   }

                });

                it("Invalid customerOrg", function () {
                    console.log("Rfs Swap - Invalid customerOrg -> quote : " + JSON.stringify(rfsActiveQuote))
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.InvalidOrg"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                });

          });

/* ====================== Below tcs are not working as expected, logged PLT-4023 for invalid PriceViewType, channel etc ==========*/
/*
            describe("Invalid priceViewType ", function () {
           // priceViewType =1 -> aggregated view, any other number (0 or other positive integers are considered as non aggregated view where each LP quote is sent separately
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid priceViewType ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidPriceViewType_" + reqId
                    console.log("Rfs Swap - Invalid priceViewType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: 8,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid priceViewType - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid priceViewType - > res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs Swap - Invalid priceViewType ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs Swap - Invalid priceViewType ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs Swap - Invalid priceViewType -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs Swap - Invalid priceViewType -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs Swap - Invalid priceViewType -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs Swap - Invalid priceViewType -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }

                });

                it("Invalid priceViewType", function () {
                    console.log("Rfs Swap - Invalid priceViewType -> quote : " + JSON.stringify(rfsActiveQuote))
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.InvalidPriceViewType"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                });

           });

            describe("Negative priceViewType ", function () {
           // priceViewType =1 -> aggregated view, any other number (0 or other positive/Negative integers are considered as non aggregated view where each LP quote is sent separately
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Negative priceViewType ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_NegativePriceViewType_" + reqId
                    console.log("Rfs Swap - Negative priceViewType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: "-2",
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Negative priceViewType - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Negative priceViewType - > res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs Swap - Negative priceViewType ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs Swap - Negative priceViewType ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs Swap - Negative priceViewType -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs Swap - Negative priceViewType -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs Swap - Negative priceViewType -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs Swap - Negative priceViewType -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }

                });

                it("Negative priceViewType", function () {
                    console.log("Rfs Swap - Negative priceViewType -> quote : " + JSON.stringify(rfsActiveQuote))
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.priceViewType"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                });

          });

            describe("Invalid channel ", function () {
          // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid channel ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidChannel_" + reqId
                    console.log("Rfs Swap - Invalid channel -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : "abc",
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid channel - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Invalid channel -> reqId = " + reqId )
                        console.log("Rfs Swap - Invalid channel -> res = " + JSON.stringify(res) )

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }

                });

                it("Invalid channel", function () {
                //Rfs Swap - Invalid channel -> res = {"rfsSubscriptionResponses":[{"refData":{"instrument":"USD/INR","spotPrecision":5,"spotPointsPrecision":3,"forwardPointsPrecision":2,"pipsFactor":10000,"instrumentType":"CURRENCY","nonDeliverable":false,"spotValueDate":"2023-07-21","inverseSpotPrecision":6,"inverseSpotPointsPrecision":3,"inverseForwardPointsPrecision":2,"inversePipsFactor":10000,"SEFSupported":false,"clearingExempt":false,"nonSpotSettlementType":false},"requestId":"G479697b2d1896c3b8cd6e74","transactionId":"FXI4248496079","expiryTimeInSeconds":25,"rfsMessage":{"eventTime":"2023/07/19 03:39:49","eventName":"RFS Submitted","eventDetails":"RFS Submitted for USD/INR. Near leg:2-Way 1,000,000.00. Value Date SPOT. Far leg: 2-Way 1,000,000.00. Value Date 1W. RFS sent to SG,VISA,MSFX"},"clOrderId":"RFS_OR_Subscription_InvalidChannel_44","status":"OK"}]}

                     console.log("Rfs Swap - Invalid channel -> quote : " + JSON.stringify(rfsSubscriptionResponses))
                       expect(JSON.stringify(rfsSubscriptionResponses.rfsMessage.eventDetails)).to.have.string('"RFS Submitted for USD/INR. Near leg:2-Way 1,000,000.00. Value Date SPOT. Far leg: 2-Way 1,000,000.00."');
                      assert.exists(JSON.stringify(rfsSubscriptionResponses.clOrderId))
                      assert.equal('"OK"', JSON.stringify(rfsSubscriptionResponses.status), "Error Status is not matching")
                });
          });

            describe("without channel tag", function () {
           // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
                 let dealtAmt = '1,000,000.00'

                 before(function (done) {
                     console.log('*************************** rfs Swap Negative TC - without channel tag ************************** ' + new Date());
                     tempReqId = "RFS_OR_Subscription_WithoutChannelTag_" + reqId
                     console.log("Rfs Swap - without channel tag -> reqId = " + reqId )
                     var subrequests = [{
                                  symbol : 'USD/INR',
                                  amount : "1000000.0",
                                  dealtCurrency : "EUR",
                                  expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                  side : rfsData.sideType2Way,
                                  priceType : rfsData.priceTypeSwap,
                                  customerAccount : rfsData.customerAccount,
                                  customerOrg: rfsData.customerOrg,
                                  priceViewType: rfsData.aggregatedView,
                                  depth: 5,
                                  providers: rfsData.providers,
                                  clOrderId: tempReqId
                      }]
                     var wsreq = { rfsSubscriptions : subrequests }
                     console.log("Rfs Swap - without channel tag - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                     connection.send(JSON.stringify(wsreq));
                     i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - without channel tag - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs Swap - Invalid dealtCurrency - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }

                    }
                });

//Rfs Swap - without channel tag - > res.rfsSubscriptionResponses received{"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/23 08:25:55","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for USD/INR with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"93","status":"ERROR","errorCode":"channel:may not be null"}
                it("without channel tag", function () {
                    assert.exists(JSON.stringify(rfsSubscriptionResponses.requestId))
                    assert.exists(JSON.stringify(rfsSubscriptionResponses.transactionId))
                    assert.exists(JSON.stringify(rfsSubscriptionResponses.eventDetails))
                    assert.equal('"OK"', JSON.stringify(rfsSubscriptionResponses.status))

              });

           });
*/

            describe("Invalid depth ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid depth ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidDepth_" + reqId
                    console.log("Rfs Swap - Invalid depth -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: "pfOrg",
                                 priceViewType: rfsData.aggregatedView,
                                 depth: "a",
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid depth - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                         res = JSON.parse(e.data)
                         console.log("Rfs Swap - Invalid depth - > res : " + JSON.stringify(res))

                         if (res.rfsSubscriptionAck) {
                             rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                             console.log("Rfs Swap - Invalid depth - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                         } else if (res.rfsSubscriptionResponses) {
                             rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                             console.log("Rfs Swap - Invalid depth - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                             done()
                         } else if (res.errors) {
                             errors = res.errors
                             console.log("errors : "+ JSON.stringify(errors))
                             done()
                         }
                     }
                 });

 //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                 it("Invalid Depth", function () {
                     console.log("Rfs Swap - Invalid depth - > errors = " + JSON.stringify(errors))
                     assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                     assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                 });
            });

            describe("Invalid providers ", function () {
            // UIG can not validate if provider is valid or not, request gets expired
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap Negative TC - Invalid providers ************************** ' + new Date());
                    tempReqId = "RFS_OR_Subscription_InvalidProviders_" + reqId
                    console.log("Rfs Swap - Invalid providers -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'USD/INR',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: ["ABC"],
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Invalid providers - > rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                     connection.onmessage = (e) => {
                          res = JSON.parse(e.data)
                          console.log("Rfs Swap - Invalid providers - > res : " + JSON.stringify(res))

                          if (res.rfsSubscriptionAck) {
                              rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                              console.log("Rfs Swap - Invalid providers - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                          } else if (res.rfsSubscriptionResponses) {
                              rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                              console.log("Rfs Swap - Invalid providers - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                              done()
                          } else if (res.errors) {
                              errors = res.errors
                              console.log("errors : "+ JSON.stringify(errors))
                              done()
                          }
                      }
                  });

  //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                  it("Invalid Providers", function () {
                      assert.equal(0, JSON.stringify(rfsSubscriptionResponses.expiryTimeInSeconds), "ExpiryTime is not matching")
                      expect(JSON.stringify(rfsSubscriptionResponses.rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for USD/INR.');
                      assert.exists(JSON.stringify(rfsSubscriptionResponses.clOrderId))
                      assert.equal('"ERROR"', JSON.stringify(rfsSubscriptionResponses.status), "Error Status is not matching")
                     assert.equal('"INTERNAL_SERVER_ERROR"', JSON.stringify(rfsSubscriptionResponses.errorCode), "Error code is not matching")
                  });
               });


        });
    };

rfsSwapNegativeTC();
