    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')

    //const login = require('../login').login
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let reqId
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors

    //Add term currency TCS

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let rfsNDFNegativeTC = function(){

        describe("RFS NDF Negative scenario ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });

            //{ "rfsSubscriptions" : [ { "symbol": "USD/INR", "amount": "1000000.0", "dealtCurrency": "USD", "expiry": 15, "nearValueDate": "1W", "farDealtAmount" : "1000000.0","farValueDate" : "2W", "fixingDate" : "" , "farFixingDate" : "", "side": "BUY", "priceType": "Swap", "customerAccount": "pfOrg", "customerOrg": "pfOrg", "priceViewType": 1, "depth": 2, "channel": "DNET/RFS/BB", "providers": ["NTFX","MSFX","SG","SUCD","UBS","WFNA"], "clOrderId": "view1MultiLP2" } ] }

            describe("Invalid CP ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid CP ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid CP - > reqId = " + reqId)
                    var subrequests = [{
                                 symbol : 'ABC/ACB',
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid CP - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs NDF - Invalid CP - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs NDF - Invalid CP - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs NDF - Invalid CP - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                it("Invalid CP", function () {
                    console.log("Rfs NDF - Invalid CP - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                    console.log("Rfs NDF - Invalid CP - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    //assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventTime))
                    //assert.equal('"RFS Failed"', JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventName))
                    //expect(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for ABC/ACB with Dealt currency USD. 2-Way 1,000,000.00. Value Date 1W. RFS submission failed');
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                    assert.equal('"RequestValidationError.InvalidCurrency"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))  //INCORRECT_REQUEST_PARAMS
              });

            });


            describe("Invalid Amt ", function () {
            //{"expiryTimeInSeconds":0,"clOrderId":"2","status":"ERROR"}
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid Amt ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid Amt - > reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "abc",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid Amt - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs NDF - Invalid Amt - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs NDF - Invalid Amt - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs NDF - Invalid Amt - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

                //"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency USD. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

                it("Invalid Amt", function () {
                    console.log("Rfs NDF - Invalid Amt - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                    console.log("Rfs NDF - Invalid Amt - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
              });

            });


            describe("Invalid side ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid side ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid side -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : "ONE_WAY",
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid side - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs NDF - Invalid side - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs NDF - Invalid side - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs NDF - Invalid side - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        } else if (res.errors) {
                            errors = res.errors
                            console.log("errors : "+ JSON.stringify(errors))
                            done()
                        }
                    }
                });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                it("Invalid Side", function () {
                    console.log("Rfs NDF - Invalid side - > errors = " + JSON.stringify(errors))
                    assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                    assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                });
            });


            describe("Invalid priceType ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid priceType ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid priceType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : "xspot",
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid priceType - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs NDF - Invalid priceType - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs NDF - Invalid priceType - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs NDF - Invalid priceType - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        } else if (res.errors) {
                            errors = res.errors
                            console.log("errors : "+ JSON.stringify(errors))
                            done()
                        }
                    }
                });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                it("Invalid priceType", function () {
                    console.log("Rfs NDF - Invalid priceType - > errors = " + JSON.stringify(errors))
                    assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                    assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                });
            });

            describe("Invalid depth ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid depth ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid depth -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: "pfOrg",
                                 priceViewType: rfsData.aggregatedView,
                                 depth: "a",
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid depth - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                         res = JSON.parse(e.data)
                         console.log("Rfs NDF - Invalid depth - > res : " + JSON.stringify(res))

                         if (res.rfsSubscriptionAck) {
                             rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                             console.log("Rfs NDF - Invalid depth - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                         } else if (res.rfsSubscriptionResponses) {
                             rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                             console.log("Rfs NDF - Invalid depth - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                             done()
                         } else if (res.errors) {
                             errors = res.errors
                             console.log("errors : "+ JSON.stringify(errors))
                             done()
                         }
                     }
                 });

 //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                 it("Invalid Depth", function () {
                     console.log("Rfs NDF - Invalid depth - > errors = " + JSON.stringify(errors))
                     assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                     assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                 });
            });

            describe("Invalid providers ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid customerOrg ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid customerOrg -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: "ABCOrg",
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: ["ABC"],
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid providers - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                     connection.onmessage = (e) => {
                          res = JSON.parse(e.data)
                          console.log("Rfs NDF - Invalid providers - > res : " + JSON.stringify(res))

                          if (res.rfsSubscriptionAck) {
                              rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                              console.log("Rfs NDF - Invalid providers - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                          } else if (res.rfsSubscriptionResponses) {
                              rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                              console.log("Rfs NDF - Invalid providers - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                              done()
                          } else if (res.errors) {
                              errors = res.errors
                              console.log("errors : "+ JSON.stringify(errors))
                              done()
                          }
                      }
                  });

  //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                  it("Invalid Providers", function () {
                      console.log("=============Rfs NDF - Invalid providers - > rfsSubscriptionResponses = " + JSON.stringify(rfsSubscriptionResponses))
                      console.log("=============rfsMessage = " + JSON.stringify(rfsSubscriptionResponses.rfsMessage))
                      console.log("=============eventDetails = " + JSON.stringify(rfsSubscriptionResponses.rfsMessage))
                      assert.equal('0', JSON.stringify(rfsSubscriptionResponses.expiryTimeInSeconds))
                      //assert.exists(JSON.stringify(rfsSubscriptionResponses.rfsMessage.eventTime))
                      //assert.equal('"RFS Failed"', JSON.stringify(rfsSubscriptionResponses.rfsMessage.eventName))
                      //expect(JSON.stringify(rfsSubscriptionResponses.rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for USD/INR with Dealt currency USD. 2-Way 1,000,000.00. Value Date 1W. RFS submission failed for');
                      assert.exists(JSON.stringify(rfsSubscriptionResponses.clOrderId))
                      assert.equal('"ERROR"', JSON.stringify(rfsSubscriptionResponses.status))
                      assert.equal('"RequestValidationError.InvalidOrg"', JSON.stringify(rfsSubscriptionResponses.errorCode))
                  });
               });

             describe("Invalid dealtCurrency ", function () {
                            let dealtAmt = '1,000,000.00'

                            before(function (done) {
                                console.log('*************************** rfs NDF Negative TC - Invalid dealtCurrency ************************** ' + new Date());
                                reqId = Math.floor(Math.random() * 100)
                                console.log("Rfs NDF - Invalid dealtCurrency -> reqId = " + reqId )
                                var subrequests = [{
                                             symbol: rfsData.symbolNdf,
                                             amount : "1000000.0",
                                             dealtCurrency : "ABC",
                                             expiry: rfsData.expiry,
                                             nearValueDate : "1W",
                                             fixingDate : "" ,
                                             side : rfsData.sideType2Way,
                                             priceType : rfsData.priceTypeNdf,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: rfsData.providers,
                                             clOrderId: parseInt(reqId)
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("Rfs NDF - Invalid dealtCurrency - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                                connection.send(JSON.stringify(wsreq));
                                i = 1

                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("Rfs NDF - Invalid dealtCurrency - > res : " + JSON.stringify(res))

                                    if (res.rfsSubscriptionAck) {
                                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                                        console.log("Rfs NDF - Invalid dealtCurrency - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                                    } else if (res.rfsSubscriptionResponses) {
                                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                                        console.log("Rfs NDF - Invalid dealtCurrency - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                                        done()
                                    }
                                }
                            });

            //==========Rfs Spot - rate response : {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,
            //"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency USD. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

                            it("Invalid CP", function () {
                                console.log("Rfs NDF - Invalid dealtCurrency - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                                console.log("Rfs NDF - Invalid dealtCurrency - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                            });

                        });


             describe("AP-10389-Invalid nearValueDate ", function () {
            // AP-10389
                            let dealtAmt = '1,000,000.00'

                            before(function (done) {
                                console.log('*************************** rfs NDF Negative TC - Invalid nearValueDate ************************** ' + new Date());
                                reqId = Math.floor(Math.random() * 100)
                                console.log("Rfs NDF - Invalid nearValueDate : rate - reqId = " + reqId )
                                var subrequests = [{
                                             symbol: rfsData.symbolNdf,
                                             amount : "1000000.0",
                                             dealtCurrency : "USD",
                                             expiry: rfsData.expiry,
                                             nearValueDate : "SPOT",
                                             fixingDate : "" ,
                                             side : rfsData.sideType2Way,
                                             priceType : rfsData.priceTypeNdf,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: rfsData.providers,
                                             clOrderId: parseInt(reqId)
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("Rfs NDF - Invalid nearValueDate -> rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                                connection.send(JSON.stringify(wsreq));
                                i = 1

                                connection.onmessage = (e) => {
                                                            res = JSON.parse(e.data)
                                                            console.log("Rfs NDF - Invalid nearValueDate - > res : " + JSON.stringify(res))

                                                            if (res.rfsSubscriptionAck) {
                                                                rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                                                                console.log("Rfs NDF - Invalid nearValueDate - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                                                            } else if (res.rfsSubscriptionResponses) {
                                                                rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                                                                console.log("Rfs NDF - Invalid nearValueDate - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                                                                done()
                                                            }
                                                  }


                            });

                     it("Invalid VD", function () {
                                console.log("Rfs NDF - Invalid nearValueDate - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                                console.log("Rfs NDF - Invalid nearValueDate - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                                assert.equal('"Request.Validation.Trade.FixingDate.BeforeTradeDate"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                            });

              });


            describe("Without nearValueDate", function () {
                            let dealtAmt = '1,000,000.00'

                            before(function (done) {
                                console.log('*************************** rfs NDF Negative TC - Without nearValueDate ************************** ' + new Date());
                                reqId = Math.floor(Math.random() * 100)
                                console.log("Rfs NDF - Without nearValueDate : rate - reqId = " + reqId )
                                var subrequests = [{
                                             symbol: rfsData.symbolNdf,
                                             amount : "1000000.0",
                                             dealtCurrency : "USD",
                                             expiry: rfsData.expiry,
                                             fixingDate : "" ,
                                             side : rfsData.sideType2Way,
                                             priceType : rfsData.priceTypeNdf,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: rfsData.providers,
                                             clOrderId: parseInt(reqId)
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("Rfs NDF - Without nearValueDate -> rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                                connection.send(JSON.stringify(wsreq));
                                i = 1

                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("Rfs NDF - Without nearValueDate - > res : " + JSON.stringify(res))

                                    if (res.rfsSubscriptionAck) {
                                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                                        console.log("Rfs NDF - Without nearValueDate - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                                    } else if (res.rfsSubscriptionResponses) {
                                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                                        console.log("Rfs NDF - Without nearValueDate - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                                        done()
                                    }
                                }
                            });

                            //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for USD/INR with Dealt currency USD. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
                            it("Without nearValueDate", function () {
                                console.log("Rfs NDF - Without nearValueDate - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                                console.log("Rfs NDF - Without nearValueDate - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                                //assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventTime))
                                //assert.equal('"RFS Failed"', JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventName))
                                //expect(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for USD/INR with Dealt currency USD. 2-Way 1,000,000.00. Value Date null. RFS submission failed');
                                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                                assert.equal('"RequestValidationError.ValueDateNotSpecified"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                                //Request.Validation.Trade.No.FixingDate.Or.ValueDate
                           });
                        });


            describe("Invalid customerAccount ", function () {
          // AP-10414 -> It should get rejected, but working fine. -- update, uig itself throws error now
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid customerAccount ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid customerAccount -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : "abc",
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid customerAccount - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("Rfs NDF - Invalid customerAccount - > res : " + JSON.stringify(res))

                                    if (res.rfsSubscriptionAck) {
                                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                                        console.log("Rfs NDF - Invalid customerAccount - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                                    } else if (res.rfsSubscriptionResponses) {
                                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                                        console.log("Rfs NDF - Invalid customerAccount - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                                        done()
                                    }
                                  }
                });

                 it("Invalid customerAccount", function () {
                                console.log("Rfs NDF - Invalid customerAccount - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                                console.log("Rfs NDF - Invalid customerAccount - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                                assert.equal('"RequestValidationError.InvalidAccount"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                            });

            });


            describe("Invalid customerOrg ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid customerOrg ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid customerOrg -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: "ABCOrg",
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid customerOrg - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("Rfs NDF - Invalid customerOrg - > res : " + JSON.stringify(res))

                                    if (res.rfsSubscriptionAck) {
                                        rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                                        console.log("Rfs NDF - Invalid customerOrg- > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                                    } else if (res.rfsSubscriptionResponses) {
                                        rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                                        console.log("Rfs NDF - Invalid customerOrg - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                                        done()
                                    }
                                   }
                });

                it("Invalid customerOrg", function () {
                                console.log("Rfs NDF - Invalid customerOrg - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                                console.log("Rfs NDF - Invalid customerOrg - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                                assert.equal('0', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                                assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
                                assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
                                assert.equal('"RequestValidationError.InvalidOrg"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                            });

          });


            describe("Invalid priceViewType ", function () {
           // priceViewType =1 -> aggregated view, any other number (0 or other positive integers are considered as non aggregated view where each LP quote is sent separately
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid priceViewType ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid priceViewType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: 8,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid priceViewType - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - Invalid priceViewType ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - Invalid priceViewType ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - Invalid priceViewType -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - Invalid priceViewType -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs NDF - Invalid priceViewType -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs NDF - Invalid priceViewType -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }

                });

                it("Invalid priceViewType", function () {
                     console.log("Rfs NDF - Invalid priceViewType -> quote : " + JSON.stringify(rfsActiveQuote))
                     let bidsArray = rfsActiveQuote.bids
                     let bidRate = bidsArray[0]
                     let offerArray = rfsActiveQuote.offers
                     let offerRate = offerArray[0]
                     // bid rate validation
                     assert.exists(bidRate.rate)
                     assert.notEqual("0",bidRate.rate, "rate is zero")
                     // offer rate validation
                     assert.exists(offerRate.rate)
                     assert.notEqual("0",offerRate.rate, "rate is zero")
                });

           });

            describe("Negative priceViewType ", function () {
           // priceViewType =1 -> aggregated view, any other number (0 or other positive/Negative integers are considered as non aggregated view where each LP quote is sent separately
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Negative priceViewType ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Negative priceViewType -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: "-2",
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Negative priceViewType - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - Negative priceViewType ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - Negative priceViewType ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - Negative priceViewType -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - Negative priceViewType -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs NDF - Negative priceViewType -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs NDF - Negative priceViewType -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }

                });

                it("Negative priceViewType", function () {
                     console.log("Rfs NDF - Negative priceViewType -> quote : " + JSON.stringify(rfsActiveQuote))
                     let bidsArray = rfsActiveQuote.bids
                     let bidRate = bidsArray[0]
                     let offerArray = rfsActiveQuote.offers
                     let offerRate = offerArray[0]
                     // bid rate validation
                     assert.exists(bidRate.rate)
                     assert.notEqual("0",bidRate.rate, "rate is zero")
                     // offer rate validation
                     assert.exists(offerRate.rate)
                     assert.notEqual("0",offerRate.rate, "rate is zero")
                });

          });

            describe("Invalid channel ", function () {
          // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF Negative TC - Invalid channel ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Invalid channel -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol: rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : "USD",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : "abc",
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Invalid channel - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - Invalid channel ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - Invalid channel ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - Invalid channel -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - Invalid channel -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs NDF - Invalid channel -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs NDF - Invalid channel -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }

                });

                it("Invalid channel", function () {
                     console.log("Rfs NDF - Invalid channel -> quote : " + JSON.stringify(rfsActiveQuote))
                     let bidsArray = rfsActiveQuote.bids
                     let bidRate = bidsArray[0]
                     let offerArray = rfsActiveQuote.offers
                     let offerRate = offerArray[0]
                     // bid rate validation
                     assert.exists(bidRate.rate)
                     assert.notEqual("0",bidRate.rate, "rate is zero")
                     // offer rate validation
                     assert.exists(offerRate.rate)
                     assert.notEqual("0",offerRate.rate, "rate is zero")
                });
          });

            describe("Positive Scenario - without channel tag-works", function () {
           // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
                 let dealtAmt = '1,000,000.00'

                 before(function (done) {
                     console.log('*************************** rfs NDF Negative TC - without channel tag ************************** ' + new Date());
                     reqId = Math.floor(Math.random() * 100)
                     console.log("Rfs NDF - without channel tag -> reqId = " + reqId )
                     var subrequests = [{
                                  symbol: rfsData.symbolNdf,
                                  amount : "1000000.0",
                                  dealtCurrency : "USD",
                                  expiry: rfsData.expiry,
                                  nearValueDate : "1W",
                                  fixingDate : "" ,
                                  side : rfsData.sideType2Way,
                                  priceType : rfsData.priceTypeNdf,
                                  customerAccount : rfsData.customerAccount,
                                  customerOrg: rfsData.customerOrg,
                                  priceViewType: rfsData.aggregatedView,
                                  depth: 5,
                                  providers: rfsData.providers,
                                  clOrderId: parseInt(reqId)
                      }]
                     var wsreq = { rfsSubscriptions : subrequests }
                     console.log("Rfs NDF - without channel tag - > rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                     connection.send(JSON.stringify(wsreq));
                     i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs NDF - without channel tag - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                            console.log("Rfs NDF - without channel tag - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                            console.log("Rfs NDF - without channel tag - > res.rfsSubscriptionResponses received" + rfsSubscriptionResponses)
                            done()
                        }
                    }
                });

//Rfs NDF - without channel tag - > res.rfsSubscriptionResponses received{"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/23 08:25:55","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for USD/INR with Dealt currency USD. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"93","status":"ERROR","errorCode":"channel:may not be null"}
                it("without channel tag", function () {
                    console.log("Rfs NDF - Invalid CP - > rfsSubscriptionAck_Request -  : " + rfsSubscriptionAck)
                    console.log("Rfs NDF - Invalid CP - > rfsSubscriptionResponses = " + rfsSubscriptionResponses)
                    assert.equal('120', JSON.stringify(res.rfsSubscriptionResponses[0].expiryTimeInSeconds))
                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventTime))
                    assert.equal('"RFS Submitted"', JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventName))
//                    expect(JSON.stringify(res.rfsSubscriptionResponses[0].rfsMessage.eventDetails)).to.have.string('"RFS Submission Failed for USD/INR with Dealt currency USD. 2-Way 1,000,000.00. Value Date 1W. RFS submission failed for');
//                    assert.exists(JSON.stringify(res.rfsSubscriptionResponses[0].clOrderId))
//                    assert.equal('"ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].status))
//                    assert.equal('"channel:may not be null"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
              });

           });

        });
    };

rfsNDFNegativeTC();
