//package test.ws-testcases.rfs;

    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')

    //const login = require('../login').login
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData
    const userData = require('../../config/properties').user

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let rfsTrade
    let res
    let errors
    let reqId = Math.floor(Math.random() * 100)
    let dltAmtInQuote = 1000000

    // Login credentials should be for MDF enabled org
    // For marketdata scripts, org should be getting rates in MDF
    // Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let getBusinessDayDates = function() {
    let today = new Date();

    // Get today's date in the format YYYY-MM-DD
    let fromValueDate = new Date(today);

    // Get the 'to' date, initially set to today + 4 days
    let toDate = new Date(today);
    toDate.setDate(today.getDate() + 4);

    // Function to check if a date is Saturday or Sunday
    function isWeekend(date) {
        return date.getDay() === 6 || date.getDay() === 0;  // 6 = Saturday, 0 = Sunday
    }

    // Adjust 'toDate' if it falls on a weekend
    while (isWeekend(toDate)) {
        toDate.setDate(toDate.getDate() + 1); // Move to next day
    }

       // Adjust 'fromValueDate' if it falls on a weekend
        while (isWeekend(fromValueDate)) {
            fromValueDate.setDate(fromValueDate.getDate() + 1); // Move to next day
        }

    // Format toDate in YYYY-MM-DD format
     let toValueDate = toDate.getFullYear() + '-'
                     + (toDate.getMonth() + 1).toString().padStart(2, '0') + '-'
                     + toDate.getDate().toString().padStart(2, '0');

          fromValueDate = fromValueDate.getFullYear() + '-'
                             + (fromValueDate.getMonth() + 1).toString().padStart(2, '0') + '-'
                             + fromValueDate.getDate().toString().padStart(2, '0');

    // Return both the 'from' and 'to' dates
    return {
        fromDate: fromValueDate,
        toDate: toValueDate
    };
}

let rfsFwdFwdTradeTC = function(){

        describe("RFS FwdFwd trade", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });


            describe("Two-Way Rate - Offer Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs FwdFwd - Two-Way Rate - Offer Trade test ************************** ' + new Date());
                    tempReqId = "RFS_FwdFwd_TwoWay_OfferTrade_" + reqId
                    console.log("Rfs FwdFwd - Two-Way Rate - Offer Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs FwdFwd - Two-Way Rate - Offer Trade Test ->  rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs FwdFwd - Two-Way Rate - Offer Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_FwdFwd_TwoWay_OfferTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote Offer Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.termAmount, "termAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].spotRate,trade.spotRate, "spotRate is not correct")
                    assert.equal(rfsActiveQuote.bids[0].rate,trade.rate, "rate is not correct")
                    assert.equal(rfsActiveQuote.bids[0].forwardPoint,trade.forwardPoints, "forwardPoints is not correct")

                    assert.equal(rfsActiveQuote.offers[0].rate,trade.farRate, "farRate is not correct")
                    assert.equal(rfsActiveQuote.offers[0].forwardPoint,trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapPoints, "FwdFwdPoints is not there")
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.swapTrade, "FwdFwdTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeFwdFwd,trade.tradeType, "tradeType is not Spot")
                    assert.equal("1W",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("2W",trade.farTenor, "farTenor is not correct")
                    assert.equal(rfsActiveQuote.farValueDate,trade.farValueDate, "farValueDate is not correct")
                    assert.exists(trade.farSide)
                    assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime))
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails))
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 EUR vs.');
                    txt = "Sell " + rfsActiveQuote.bids[0].dealtAmount + ".00 EUR vs. " + rfsActiveQuote.bids[0].settledAmount + ".00 USD. Value Date: 1W and Buy " + rfsActiveQuote.bids[0].dealtAmount+ ".00 EUR vs. " + rfsActiveQuote.offers[0].settledAmount + ".00 USD. Value Date: 2W against " + trade.tradeId + ". From: " + trade.counterParty + ", Counterparty "
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string(txt)
               });

            });


            describe("One-Way Rate -1W-1M- Offer Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs FwdFwd - One-Way Rate - Offer Trade test ************************** ' + new Date());
                    tempReqId = "RFS_FwdFwd_OneWay_OfferTrade_" + reqId
                    console.log("Rfs FwdFwd - One-Way Rate - Offer Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1M",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs FwdFwd - One-Way Rate - Offer Trade Test ->  rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs FwdFwd - One-Way Rate - Offer Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_FwdFwd_OneWay_OfferTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - One-Way quote Offer Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.termAmount, "termAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].spotRate,trade.spotRate, "spotRate is not correct")
                    assert.equal(rfsActiveQuote.bids[0].rate,trade.rate, "rate is not correct")
                    assert.equal(rfsActiveQuote.bids[0].forwardPoint,trade.forwardPoints, "forwardPoints is not correct")

                    assert.equal(rfsActiveQuote.offers[0].rate,trade.farRate, "farRate is not correct")
                    assert.equal(rfsActiveQuote.offers[0].forwardPoint,trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapPoints, "FwdFwdPoints is not there")
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.swapTrade, "FwdFwdTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeFwdFwd,trade.tradeType, "tradeType is not Spot")
                    assert.equal("1W",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("1M",trade.farTenor, "farTenor is not correct")
                    assert.equal(rfsActiveQuote.farValueDate,trade.farValueDate, "farValueDate is not correct")
                    assert.exists(trade.farSide)
                    assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime))
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails))
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 EUR vs.');
                    txt = "Sell " + rfsActiveQuote.bids[0].dealtAmount + ".00 EUR vs. " + rfsActiveQuote.bids[0].settledAmount + ".00 USD. Value Date: 1W and Buy " + rfsActiveQuote.bids[0].dealtAmount+ ".00 EUR vs. " + rfsActiveQuote.offers[0].settledAmount + ".00 USD. Value Date: 1M against " + trade.tradeId + ". From: " + trade.counterParty + ", Counterparty "
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string(txt)

               });

            });


            describe("One-Way Rate TOD-1W- Bid Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs FwdFwd - One-Way Rate - Bid Trade test ************************** ' + new Date());
                    tempReqId = "RFS_FwdFwd_OneWay_BidTrade_" + reqId
                    console.log("Rfs FwdFwd - One-Way Rate - Bid Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "TOD",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs FwdFwd - One-Way Rate - Bid Trade Test ->  rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs FwdFwd - One-Way Rate - Bid Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_FwdFwd_OneWay_Trade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - One-Way quote Bid Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
//                    assert.notStrictEqual(rfsActiveQuote.offers[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                    //assert.notStrictEqual(rfsActiveQuote.offers[0].settledAmount,trade.termAmount, "termAmount is not correct")

                    assert.exists(trade.settledAmount)
                    assert.exists(trade.termAmount)

                    assert.equal(rfsActiveQuote.offers[0].spotRate,trade.spotRate, "spotRate is not correct")
                    assert.equal(rfsActiveQuote.offers[0].rate,trade.rate, "rate is not correct")
                    assert.equal(rfsActiveQuote.offers[0].forwardPoint,trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "FwdFwdPoints is not there")

                    assert.equal(rfsActiveQuote.bids[0].rate,trade.farRate, "farRate is not there")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].forwardPoint,trade.farForwardPoints, "farForwardPoints is not there")

                    assert.exists(trade.swapTrade, "FwdFwdTrade is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)
                    assert.equal(rfsData.priceTypeFwdFwd,trade.tradeType, "tradeType is not Spot")
                    assert.equal("TOD",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "farValueDate is not correct")
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("1W",trade.farTenor, "fartenor is not TOM")
                    //assert.exists(trade.farValueDate)
                    assert.equal(rfsActiveQuote.farValueDate,trade.farValueDate, "farValueDate is not correct")
                    assert.exists(trade.farSide)
					assert.equal("Sell",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime))
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails))
                    txt = "Buy " + rfsActiveQuote.offers[0].dealtAmount + ".00 EUR vs. " + rfsActiveQuote.offers[0].settledAmount + ".00 USD. Value Date: TOD and Sell " + rfsActiveQuote.bids[0].dealtAmount+ ".00 EUR vs. " + rfsActiveQuote.bids[0].settledAmount + ".00 USD. Value Date: 1W against " + trade.tradeId + ". From: " + trade.counterParty + ", Counterparty "
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string(txt)


               });

            });


            describe("Two-Way Rate -1W-2W Offer Term Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs FwdFwd - Two-Way Rate - Offer Term Trade test ************************** ' + new Date());
                    tempReqId = "RFS_FwdFwd_TwoWay_OfferTermTrade_" + reqId
                    console.log("Rfs FwdFwd - Two-Way Rate - Offer Term Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs FwdFwd - Two-Way Rate - Offer Term Trade Test ->  rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs FwdFwd - Two-Way Rate - Offer Term Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_FwdFwd_TwoWay_OfferTermTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote Offer Term Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000",trade.dealtAmount, "dealtAmount is not correct")
                    assert.exists(trade.settledAmount)
                    assert.equal("1000000",trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.baseAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.exists(trade.rate)
                    //assert.equal("0.0002",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "FwdFwdPoints is not there")
                    assert.exists(trade.farRate, "farRate is not there")
                    assert.equal("1000000",trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.exists(trade.farSettledAmount, "farSettledAmount is not there")
                    assert.equal("1000000",trade.farTermAmount, "farTermAmount is not correct")
                    assert.exists(trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapTrade, "FwdFwdTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeFwdFwd,trade.tradeType, "tradeType is not Spot")
                    assert.equal("1W",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("2W",trade.farTenor, "farTenor is not Spot")
                    assert.exists(trade.farValueDate)
                    assert.exists(trade.farSide)
                    assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 USD vs.');
               });

            });


            describe("Two-Way Rate -MisMatch-1W-2W- Bid Term Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs FwdFwd - Two-Way Rate - Bid Term Trade test ************************** ' + new Date());
                    tempReqId = "RFS_FwdFwd_TwoWay_BidTermTrade_" + reqId
                    console.log("Rfs FwdFwd - Two-Way Rate - Bid Term Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "2000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs FwdFwd - Two-Way Rate - Bid Term Trade Test ->  rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs FwdFwd - Two-Way Rate - Bid Term Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_FwdFwd_TwoWay_BidTermTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote Bid Term Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000",trade.dealtAmount, "dealtAmount is not correct")
                    assert.exists(trade.settledAmount)
                    assert.equal("1000000",trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.baseAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.exists(trade.rate)
                    assert.exists(trade.forwardPoints)
                    //assert.equal("0.0001",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "FwdFwdPoints is not there")
                    assert.exists(trade.farRate, "farRate is not there")
                    assert.equal("2000000",trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.exists(trade.farSettledAmount, "farSettledAmount is not there")
                    assert.equal("2000000",trade.farTermAmount, "farTermAmount is not correct")
                    assert.exists(trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapTrade, "FwdFwdTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeFwdFwd,trade.tradeType, "tradeType is not Spot")
                    assert.equal("1W",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("2W",trade.farTenor, "tenor is not Spot")
                    assert.exists(trade.farValueDate)
                    assert.exists(trade.farSide)
					assert.equal("Sell",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD vs.');
               });

            });


            describe("One-Way Rate -Mismatch-1M-2M Offer Term Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs FwdFwd - One-Way Rate - Offer Term Trade test ************************** ' + new Date());
                    tempReqId = "RFS_FwdFwd_OneWay_OfferTermTrade_" + reqId
                    console.log("Rfs FwdFwd - One-Way Rate - Offer Term Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "2000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1M",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2M",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs FwdFwd - One-Way Rate - Offer Term Trade Test ->  rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs FwdFwd - One-Way Rate - Offer Term Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_FwdFwd_OneWay_OfferTermTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - One-Way quote Offer Term Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("2000000",trade.dealtAmount, "dealtAmount is not correct")
                    assert.exists(trade.settledAmount)
                    assert.equal("2000000",trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.baseAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.exists(trade.rate)
                    assert.exists(trade.forwardPoints)
                    //assert.equal("0.0002",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "FwdFwdPoints is not there")
                    assert.exists(trade.farRate, "farRate is not there")
                    assert.equal("1000000",trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.exists(trade.farSettledAmount, "farSettledAmount is not there")
                    assert.equal("1000000",trade.farTermAmount, "farTermAmount is not correct")
                    assert.exists(trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapTrade, "FwdFwdTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeFwdFwd,trade.tradeType, "tradeType is not Spot")
                    assert.equal("1M",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("2M",trade.farTenor, "tenor is not Spot")
                    assert.exists(trade.farValueDate)
                    assert.exists(trade.farSide)
                    assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 2000000.00 USD vs.');
               });

            });


            describe("One-Way Rate -Broken Date- Bid Term Trade test ", function () {
               let dealtAmt = '1,000,000.00'

            // Example of using the function
            let { fromDate, toDate } = getBusinessDayDates();
            console.log('From Date:', fromDate);
            console.log('To Date:', toDate);

                before(function (done) {
                    console.log('*************************** rfs FwdFwd - One-Way Rate - Bid Term Trade test ************************** ' + new Date());
                    tempReqId = "RFS_FwdFwd_OneWay_BidTermTrade_" + reqId
                    console.log("Rfs FwdFwd - One-Way Rate - Bid Term Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : fromDate,
								 farDealtAmount : "1000000.0",
								 farValueDate : toDate,
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs FwdFwd - One-Way Rate - Bid Term Trade Test ->  rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs FwdFwd - One-Way Rate - Bid Term Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_FwdFwd_OneWay_BidTermTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - One-Way quote Bid Term Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000",trade.dealtAmount, "dealtAmount is not correct")
                    assert.exists(trade.settledAmount)
                    assert.equal("1000000",trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.baseAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.exists(trade.rate)
                    //assert.equal("0.0001",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "FwdFwdPoints is not there")
                    assert.exists(trade.farRate, "farRate is not there")
                    assert.equal("1000000",trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.exists(trade.farSettledAmount, "farSettledAmount is not there")
                    assert.equal("1000000",trade.farTermAmount, "farTermAmount is not correct")
                    assert.exists(trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapTrade, "FwdFwdTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeFwdFwd,trade.tradeType, "tradeType is not Spot")
                    //assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(fromDate,trade.tradeDate)
                    assert.equal(fromDate,trade.valueDate)
                    assert.equal(toDate,trade.farValueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    //assert.exists(trade.farTenor)
                    assert.exists(trade.farValueDate)
                    assert.exists(trade.farSide)
					assert.equal("Sell",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD vs.');
               });

            });

            describe("Two-Way Rate -pre-spot -TOMSPOT- Bid Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs FwdFwd - Two-Way Rate - Bid Trade test ************************** ' + new Date());
                    tempReqId = "RFS_FwdFwd_TwoWay_BidTrade_TOMSPOT" + reqId
                    console.log("Rfs FwdFwd - Two-Way Rate - TOMSPOT -Bid Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "TOM",
								 farDealtAmount : "1000000.0",
								 farValueDate : "SPOT",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : 0, //rfsData.sideType2Way,
                                 priceType : 3, //rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: 1, //rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 //providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs FwdFwd - Two-Way Rate -TOMSPOT- Bid Trade Test ->  rfsFwdFwdSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs FwdFwd - Two-Way Rate - Bid Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "API/WS/RFS",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_FwdFwd_TwoWay_BidTrade TOM-SPOT test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote Bid Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                    //assert.equal(rfsActiveQuote.offers[1].settledAmount,trade.settledAmount, "settledAmount is not correct")
                    assert.exists(trade.termAmount)
                    //expect(rfsActiveQuote.offers[1].settledAmount).to.have.lengthOf(7)
                    assert.exists(trade.settledAmount)
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                    //assert.notStrictEqual(rfsActiveQuote.offers[1].settledAmount,trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.termAmount)

                    //expect(rfsActiveQuote.offers[1].spotRate).to.include(trade.spotRate)
                    //assert.equal(rfsActiveQuote.offers[1].spotRate,trade.spotRate, "spotRate is not correct")
                    assert.exists(trade.spotRate, "spotrate is not there")
                    assert.exists(trade.rate, "rate is not correct")
                    assert.exists(trade.forwardPoints, "forwardPoints is not correct")
                    //assert.notStrictEqual(rfsActiveQuote.offers[1].forwardPoint,trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "FwdFwdPoints is not there")

//                    assert.equal(rfsActiveQuote.bids[1].rate,trade.farRate, "farRate is not there")
//                    assert.equal(rfsActiveQuote.bids[1].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
//                    assert.equal(rfsActiveQuote.bids[1].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
//                    assert.equal(rfsActiveQuote.bids[1].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
//                    assert.equal(rfsActiveQuote.bids[1].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
//                    assert.equal(rfsActiveQuote.bids[1].forwardPoint,trade.farForwardPoints, "farForwardPoints is not there")


                    assert.exists(trade.farRate, "farRate is not there") //rfsActiveQuote.bids[1].rate,
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.notStrictEqual(rfsActiveQuote.bids[0].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.notStrictEqual(rfsActiveQuote.bids[0].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
                    assert.notStrictEqual(rfsActiveQuote.bids[0].forwardPoint,trade.farForwardPoints, "farForwardPoints is not there")

                    assert.exists(trade.swapTrade, "FwdFwdTrade is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)
                    assert.equal(rfsData.priceTypeFwdFwd,trade.tradeType, "tradeType is not Spot")
                    assert.equal("TOM",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "farValueDate is not correct")
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("SPOT",trade.farTenor, "tenor is not Spot")
                    //assert.exists(trade.farValueDate)
                    assert.equal(rfsActiveQuote.farValueDate,trade.farValueDate, "farValueDate is not correct")
                    assert.exists(trade.farSide)
					assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime))
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails))
                    //txt = "Sell " + rfsActiveQuote.offers[0].dealtAmount + ".00 EUR vs. " + rfsActiveQuote.offers[0].settledAmount + ".00 USD. Value Date: TOM and Buy " + rfsActiveQuote.bids[0].dealtAmount+ ".00 EUR vs. " + rfsActiveQuote.bids[0].settledAmount + ".00 USD. Value Date: SPOT against " + trade.tradeId + ". From: " + trade.counterParty + ", Counterparty "
                    //expect(rfsMessage.eventDetails).to.include(txt)

               });

            });


        });
    };

rfsFwdFwdTradeTC();
