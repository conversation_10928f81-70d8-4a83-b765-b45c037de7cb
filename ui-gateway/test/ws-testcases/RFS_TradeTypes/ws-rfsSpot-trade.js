    const assert = require('chai').assert
    const expect = require('chai').expect
    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData
    const userData = require('../../config/properties').user

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let rfsTrade
    let rfsTradeResponse

    let reqId = Math.floor(Math.random() * 100)

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

    let rfsSpotTradeTC = function(){

        describe("RFS Spot Trade", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });

            describe("RFS Spot Buy Trade - Two-way quote test", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Buy Trade - Two-way quote test  ************************** ' + new Date());
                    console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                    console.log(wsreq)
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote id" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: reqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                            i++;
                        } else if (res.rfsTradeAck) {
                            rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                            rfsTradeResponses = res.rfsTradeResponses[0]
                            rfsTrade = res.rfsTradeResponses[0]
                            done()
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
                });

                it("RFS Spot Buy Trade - Two-way quote test", function () {

                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote test -> Trade details : " )
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(trade.dealtAmount*trade.spotRate,trade.settledAmount, "settledAmount is not correct")
                    //assert.exists(trade.settledAmount)
                    assert.equal("1000000.00",trade.baseAmount, "baseAmount is not correct")
                    assert.equal(trade.dealtAmount*trade.spotRate,trade.termAmount, "termAmount is not correct")
                    //assert.exists(trade.termAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 EUR vs.');

             });

            });

            describe("RFS Spot Sell Trade - Two-way quote test", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Sell Trade - Two-way quote test  ************************** ' + new Date());

                    console.log("RFS Spot Sell Trade - Two-way quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: "SPOT_TwoWay_Rate_BidTrade_" + reqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Sell Trade - Two-way quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    reqId = Math.floor(Math.random() * 100)
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                console.log("RFS Spot Sell Trade - Two-way quote test ->  rate response : " )
                                console.log(rate)
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : ")
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
                            rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                            rfsTrade = res.rfsTradeResponses[0]
                            done()
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
                });

                it("RFS Spot Buy Trade - Two-way quote test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Sell Trade - Two-way quote test -> Trade details : " )
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount*trade.spotRate).split(".")[0],trade.settledAmount, "settledAmount is not correct")
                    assert.equal("1000000.00",trade.baseAmount, "baseAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount*trade.spotRate).split(".")[0],trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeSpot,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 EUR vs.');

             });

            });

            describe("RFS Spot Buy Trade - One-way-Offer quote test", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Buy Trade - One-way-Offer quote test  ************************** ' + new Date());
                    console.log("RFS Spot Buy Trade - One-way-Offer quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: "SPOT_OneWay_Rate_OfferTrade_" + reqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Buy Trade - One-way-Offer quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    reqId = Math.floor(Math.random() * 100)
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                 rfsActiveQuote = rate
                                 quoteId =rfsActiveQuote.offers[0].quoteId
                                 console.log("RFS quote id = " + quoteId)
                                 console.log("RFS Spot Buy Trade - One-way-Offer quote test ->  rate response : " )
                                 console.log(rfsActiveQuote)
                                 systemReqId = rate.requestId
                                 var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));

                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
                            rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                            rfsTradeResponses = res.rfsTradeResponses[0]
                            rfsTrade = res.rfsTradeResponses[0]
                            done()
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
                });

                it("RFS Spot Buy Trade - One-way-Offer quote test", function () {
                    console.log("RFS Spot Buy Trade - One-way-Offer quote test -> Trade details : " )
                    console.log(rfsTrade)

                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage

                    assert.exists(trade.orderId,"orderId doesnt exist")
                    assert.exists(trade.tradeId)
                    assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.exists(trade.executionTime)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount*trade.spotRate).split(".")[0], trade.settledAmount, "settledAmount is not correct")
                    assert.exists(trade.settledAmount,"settledAmount doesnt exist")
                    assert.equal("1000000.00",trade.baseAmount, "baseAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount*trade.spotRate).split(".")[0], trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.spotRate,"spotRate doesnt exist")
                    assert.exists(trade.rate,"rate doesnt exist")
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty,"counterParty doesnt exist")
                    assert.exists(trade.cptyLongName,"cptyLongName doesnt exist")
                    assert.exists(trade.cptyTradeId,"cptyTradeId doesnt exist")
                    assert.exists(trade.counterPartyAccount,"counterPartyAccount doesnt exist")
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.channel)

                    assert.exists(trade.header.referenceId,"referenceId doesnt exist")
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime), "eventTime is not correct")
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails), "eventDetails is not correct")
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 EUR vs.');

                });

            });

            describe("RFS Spot Sell Trade - One-way-Bid quote test", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Sell Trade - One-way-Bid quote test  ************************** ' + new Date());
                    console.log("RFS Spot Sell Trade - One-way-Bid quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: "SPOT_OneWay_Rate_BidTrade_" + reqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Sell Trade - One-way-Bid quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                console.log("RFS Spot Sell Trade - One-way-Bid quote test ->  rate response : " )
                                console.log(rate)
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: "SPOT_OneWay_Rate_BidTrade_" + reqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
                            rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsTradeResponses) {
                            rfsTrade = res.rfsTradeResponses[0]
                            done()
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
                });

                it("RFS Spot Sell Trade - OneWay-Bid test", function () {
                    console.log("RFS Spot Sell Trade - One-way-Bid quote test -> Trade details : ")
                    console.log(rfsTrade)
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)
                    assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.exists(trade.executionTime)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount*trade.spotRate).split(".")[0], trade.settledAmount, "settledAmount is not correct")
                    assert.equal("1000000.00",trade.baseAmount, "baseAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount*trade.spotRate).split(".")[0], trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)

                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 EUR vs.');

             });

            });

            describe("RFS Spot Buy Term Trade - Two-way quote test", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Buy Term Trade - Two-way quote test  ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS Spot Buy Term Trade - Two-way quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Buy Term Trade - Two-way quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    reqId = Math.floor(Math.random() * 100)
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        //console.log("=======res==========" + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                console.log("RFS Spot Buy Term Trade - Two-way quote test ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                connection.send(JSON.stringify(wstradereq));

                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                console.log("RFS Spot Buy Term Trade - Two-way quote test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS Spot Buy Term Trade - Two-way quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
                          console.log("RFS Spot Buy Term Trade - Two-way quote test -> rfsTradeAck received")
                        } else if (res.rfsTradeResponses) {
                          console.log("RFS Spot Buy Term Trade - Two-way quote test -> rfsTradeResponses received")
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS Spot Buy Term Trade - Two-way quote test -> rfsWithdrawAck received")
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS Spot Buy Term Trade - Two-way quote test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

//{"rfsTradeResponses":[{"trades":[{"orderId":"FXI9201040586","tradeId":"FXI9201040586","tradeType":"Spot","tenor":"SPOT",
//"tradeDate":"10/26/2021","valueDate":"10/28/2021","executionTime":"10/26/2021 06:07:00:189 GMT","maker":false,"orderSide":"Buy","status":"Verified",
//"instrument":"EUR/USD","dealtIns":"EUR","dealtAmount":"1000000.00","dAmt":0,"settledAmount":"1186390.00","sAmt":0,"baseAmount":"1000000.00",
//"bAmt":0,"termAmount":"1186390.00","tAmt":0,"spotRate":"1.18639","rate":"1.18639","dRate":0,"forwardPoints":"0.00000","customerAccount":"pfOrg",
//"customerOrg":"pfOrg","trader":"user1","counterParty":"WFNA","cptyLongName":"Wells Fargo","cptyTradeId":"","counterPartyAccount":"WFNAMB",
//"fRate":0,"fDAmt":0,"fSAmt":0,"fBAmt":0,"fTAmt":0,"counterpartyBLEI":"549300YLYOXGEYO4YK89","UPI":"EUR_USD_SPOT","UTI":"MCQRZU5ST1INP9201040586",
//"SEF":false,"externalRequestId":"74","requestId":"G4796976d517cbb35860d819","maskedLP":"WFNA","isnet":false,"isMidMarket":false,"mifidFields":{},
//"sefFields":{"cptyBLEI":"549300YLYOXGEYO4YK89","upi":"EUR_USD_SPOT"},"swapTrade":{},"brokerCDQ":{},"benchMarkRate":0,"cptyB":{},"outright":{},
//"channel":"DNET/RFS/BB","pricingType":"ESPnMDS","header":{"referenceId":"74","customerOrg":"pfOrg","customerId":"user1","customerAccount":"pfOrg"}}]
//,"rfsMessage":{"eventTime":"2021/10/26 06:07:00","eventName":"RFS Trade Verified","
//eventDetails":"Buy 1000000.00 EUR vs. 1186390.00 USD. Value Date: SPOT against FXI9201040586. From: WFNA, Counterparty WFNAMB. External ID:  Price verified."}}]}
                it("RFS Spot Buy Term Trade - Two-way quote test", function () {
                    console.log("RFS Spot Buy Term Trade - Two-way quote test -> Trade details : " )
                    console.log(rfsTrade)

                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote test -> Trade details : " )
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.settledAmount).split(".")[0], "settledAmount is not correct")
                    //assert.exists(trade.settledAmount)
                    assert.equal("1000000.00",trade.termAmount, "termAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.baseAmount).split(".")[0], "baseAmount is not correct")
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 USD ');

             });

            });

            describe("RFS Spot Sell Term Trade - Two-way quote test", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Sell Term Trade - Two-way quote test  ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS Spot Sell Term Trade - Two-way quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Sell Term Trade - Two-way quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    reqId = Math.floor(Math.random() * 100)
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        //console.log("=======res==========" + JSON.stringify(res))

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                console.log("RFS Spot Sell Term Trade - Two-way quote test ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                connection.send(JSON.stringify(wstradereq));

                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                console.log("RFS Spot Sell Term Trade - Two-way quote test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS Spot Sell Term Trade - Two-way quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
                          console.log("RFS Spot Sell Term Trade - Two-way quote test -> rfsTradeAck received")
                        } else if (res.rfsTradeResponses) {
                          console.log("RFS Spot Sell Term Trade - Two-way quote test -> rfsTradeResponses received")
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS Spot Sell Term Trade - Two-way quote test -> rfsWithdrawAck received")
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS Spot Sell Term Trade - Two-way quote test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

//{"rfsTradeResponses":[{"trades":[{"orderId":"FXI9201040586","tradeId":"FXI9201040586","tradeType":"Spot","tenor":"SPOT",
//"tradeDate":"10/26/2021","valueDate":"10/28/2021","executionTime":"10/26/2021 06:07:00:189 GMT","maker":false,"orderSide":"Buy","status":"Verified",
//"instrument":"EUR/USD","dealtIns":"EUR","dealtAmount":"1000000.00","dAmt":0,"settledAmount":"1186390.00","sAmt":0,"baseAmount":"1000000.00",
//"bAmt":0,"termAmount":"1186390.00","tAmt":0,"spotRate":"1.18639","rate":"1.18639","dRate":0,"forwardPoints":"0.00000","customerAccount":"pfOrg",
//"customerOrg":"pfOrg","trader":"user1","counterParty":"WFNA","cptyLongName":"Wells Fargo","cptyTradeId":"","counterPartyAccount":"WFNAMB",
//"fRate":0,"fDAmt":0,"fSAmt":0,"fBAmt":0,"fTAmt":0,"counterpartyBLEI":"549300YLYOXGEYO4YK89","UPI":"EUR_USD_SPOT","UTI":"MCQRZU5ST1INP9201040586",
//"SEF":false,"externalRequestId":"74","requestId":"G4796976d517cbb35860d819","maskedLP":"WFNA","isnet":false,"isMidMarket":false,"mifidFields":{},
//"sefFields":{"cptyBLEI":"549300YLYOXGEYO4YK89","upi":"EUR_USD_SPOT"},"swapTrade":{},"brokerCDQ":{},"benchMarkRate":0,"cptyB":{},"outright":{},
//"channel":"DNET/RFS/BB","pricingType":"ESPnMDS","header":{"referenceId":"74","customerOrg":"pfOrg","customerId":"user1","customerAccount":"pfOrg"}}]
//,"rfsMessage":{"eventTime":"2021/10/26 06:07:00","eventName":"RFS Trade Verified","
//eventDetails":"Buy 1000000.00 EUR vs. 1186390.00 USD. Value Date: SPOT against FXI9201040586. From: WFNA, Counterparty WFNAMB. External ID:  Price verified."}}]}
                it("RFS Spot Sell Term Trade - Two-way quote test", function () {
                    console.log("RFS Spot Sell Term Trade - Two-way quote test -> Trade details : " + JSON.stringify(rfsTrade))
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage

                    console.log("RFS Spot Buy Trade - Two-way quote test -> Trade details : " )
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.settledAmount).split(".")[0], "settledAmount is not correct")
                    //assert.exists(trade.settledAmount)
                    assert.equal("1000000.00",trade.termAmount, "termAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.baseAmount).split(".")[0], "baseAmount is not correct")
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD vs.');

             });

            });

            describe("RFS Spot Buy Term Trade - One-way-Bid quote test", function ()  {
                  let dealtAmt = '1,000,000.00'

                  before(function (done) {
                      console.log('*************************** RFS Spot Buy Term Trade - One-way-Bid quote test  ************************** ' + new Date());
                      reqId = Math.floor(Math.random() * 100)
                      console.log("RFS Spot Buy Term Trade - One-way-Bid quote test -> reqId = " + reqId)
                      var subrequests = [{
                                   symbol : rfsData.symbol,
                                   amount : "1000000.0",
                                   dealtCurrency : rfsData.termCcy,
                                   expiry: rfsData.expiry,
                                   nearValueDate : "SPOT",
                                   fixingDate : "" ,
                                   side : rfsData.sideTypeBuy,
                                   priceType : rfsData.priceTypeSpot,
                                   customerAccount : rfsData.customerAccount,
                                   customerOrg: rfsData.customerOrg,
                                   priceViewType: rfsData.aggregatedView,
                                   depth: 5,
                                   channel : rfsData.channel,
                                   providers: rfsData.providers,
                                   clOrderId: parseInt(reqId)
                       }]
                      var wsreq = { rfsSubscriptions : subrequests }
                      console.log("RFS Spot Buy Term Trade - One-way-Bid quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                      connection.send(JSON.stringify(wsreq));
                      tradeDone = "false"
                      i=1
                      reqId = Math.floor(Math.random() * 100)
                      connection.onmessage = (e) => {
                          res = JSON.parse(e.data)

                          if (res.rfsRates && i<10) {
                              rate = res.rfsRates[0]
                              if(rate.status === "A" && tradeDone === "false") {
                                  console.log("RFS Spot Buy Term Trade - One-way-Bid quote test ->  rate response : " + JSON.stringify(rate))
                                  rfsActiveQuote = rate
                                  quoteId =rfsActiveQuote.bids[0].quoteId
                                  console.log("RFS quote id = " + quoteId)
                                  systemReqId = rate.requestId
                                  var tradereq = [{
                                      quoteId: quoteId,
                                      side: "BUY",
                                      symbol: rfsData.symbol,
                                      dealtCurrency: rfsData.termCcy,
                                      tradeChannel: "DNET/RFS/BB",
                                      clOrderId: parseInt(reqId)
                                  }]
                                  var wstradereq = { rfsTrades : tradereq }
                                  console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                  connection.send(JSON.stringify(wstradereq));

                                  tradeDone = "true"
                              } else if (rate.status === "I") {
                                  console.log("RFS Spot Buy Term Trade - One-way-Bid quote test ->waiting for Inactive quote")
                                  rfsInactiveQuote = res.rfsRates[0]
                                  console.log("RFS Spot Buy Term Trade - One-way-Bid quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                              }
                              i++;
                          } else if (res.rfsTradeAck) {
                            console.log("RFS Spot Buy Term Trade - One-way-Bid quote test -> rfsTradeAck received")
                          } else if (res.rfsTradeResponses) {
                            console.log("RFS Spot Buy Term Trade - One-way-Bid quote test -> rfsTradeResponses received")
                            rfsTrade = res.rfsTradeResponses[0]
                            done()
                          } else if (res.rfsWithdrawAck) {
                              console.log("RFS Spot Buy Term Trade - One-way-Bid quote test -> rfsWithdrawAck received")
                              done()
                          } else if (res.rfsResponses) {
                              rfsWithdrawResponse = res.rfsResponses[0]
                              console.log("RFS Spot Buy Term Trade - One-way-Bid quote test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                              done()
                          }
                      }
                  });

                  it("RFS Spot Buy Term Trade - One-way-Bid quote test", function () {
                      console.log("RFS Spot Buy Term Trade - One-way-Bid quote test -> Trade details : " + JSON.stringify(rfsTrade))

                        let trade = rfsTrade.trades[0]
                        rfsMessage = rfsTrade.rfsMessage
                        console.log("RFS Spot Buy Trade - Two-way quote test -> Trade details : " )
                        console.log(rfsTrade)

                        assert.exists(trade.executionTime)
                        assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                        assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.settledAmount).split(".")[0], "settledAmount is not correct")
                        //assert.exists(trade.settledAmount)
                        assert.equal("1000000.00",trade.termAmount, "termAmount is not correct")
                        assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.baseAmount).split(".")[0], "baseAmount is not correct")
                        assert.exists(trade.spotRate)
                        assert.exists(trade.rate)
                        assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                        assert.exists(trade.orderId)
                        assert.exists(trade.tradeId)

                        assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                        assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                        assert.exists(trade.tradeDate)
                        assert.exists(trade.valueDate)
                        assert.equal(false,trade.maker, "maker value is not correct")
                        assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                        assert.equal("Verified",trade.status, "status is not correct")
                        assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                        assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                        assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                        assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                        assert.equal(userData.username,trade.trader, "trader is not correct")
                        assert.exists(trade.counterParty)
                        assert.exists(trade.cptyLongName)
                        assert.exists(trade.cptyTradeId)
                        assert.exists(trade.counterPartyAccount)
                        //assert.exists(trade.counterpartyBLEI)
                        assert.exists(trade.UPI)
                        assert.exists(trade.UTI)
                        assert.exists(trade.externalRequestId)
                        assert.exists(trade.requestId, "requestId doesnt exist")
                        assert.equal(false,trade.isnet, "isnet value is not correct")
                        assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                        assert.exists(trade.channel, "channel doesnt exist")
                        assert.exists(trade.header.referenceId)
                        assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                        assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                        assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                        assert.exists(JSON.stringify(rfsMessage.eventTime))
                        assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                        assert.exists(JSON.stringify(rfsMessage.eventDetails))
                        expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD vs. ');

                  });

              });

            describe("RFS Spot Sell Term Trade - One-way-Offer quote test", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Sell Term Trade - One-way-Offer quote test  ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Sell Term Trade - One-way-Offer quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    reqId = Math.floor(Math.random() * 100)
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                console.log("RFS Spot Sell Term Trade - One-way-Offer quote test ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                connection.send(JSON.stringify(wstradereq));

                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                console.log("RFS Spot Sell Term Trade - One-way-Offer quote test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
                          console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsTradeAck received")
                        } else if (res.rfsTradeResponses) {
                          console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsTradeResponses received")
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsWithdrawAck received")
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

                it("RFS Spot Sell Term Trade - One-way-Offer quote test", function () {
                    console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> Trade details : " + JSON.stringify(rfsTrade))
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage

                    assert.exists(trade.executionTime)
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.settledAmount).split(".")[0], "settledAmount is not correct")
                    //assert.exists(trade.settledAmount)
                    assert.equal("1000000.00",trade.termAmount, "termAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.baseAmount).split(".")[0], "baseAmount is not correct")
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 USD vs.');

             });

            });

            describe("RFS Spot Sell Term Trade - One-way-Bid quote test", function ()  {
            // Doesnt allow to change the side in trade request. Trade always goes with the "side" subscription request was sent.
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Sell Term Trade - One-way-Bid quote test  ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS Spot Sell Term Trade - One-way-Bid quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Sell Term Trade - One-way-Bid quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    reqId = Math.floor(Math.random() * 100)
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                console.log("RFS Spot Sell Term Trade - One-way-Bid quote test ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                connection.send(JSON.stringify(wstradereq));

                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                console.log("RFS Spot Sell Term Trade - One-way-Bid quote test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS Spot Sell Term Trade - One-way-Bid quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
                          console.log("RFS Spot Sell Term Trade - One-way-Bid quote test -> rfsTradeAck received")
                        } else if (res.rfsTradeResponses) {
                          console.log("RFS Spot Sell Term Trade - One-way-Bid quote test -> rfsTradeResponses received")
                          rfsTrade = res.rfsTradeResponses[0]
                          done()
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS Spot Sell Term Trade - One-way-Bid quote test -> rfsWithdrawAck received")
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS Spot Sell Term Trade - One-way-Bid quote test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

                it("RFS Spot Sell Term Trade - One-way-Bid quote test", function () {
                    console.log("RFS Spot Sell Term Trade - One-way-Bid quote test -> Trade details : " )
                    console.log(rfsTrade)
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage

                    assert.exists(trade.executionTime)
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.settledAmount).split(".")[0], "settledAmount is not correct")
                    //assert.exists(trade.settledAmount)
                    assert.equal("1000000.00",trade.termAmount, "termAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.baseAmount).split(".")[0], "baseAmount is not correct")
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD vs.');

             });

            });

            describe("RFS Spot Buy Term Trade - One-way-Offer quote test", function ()  {
            // Doesnt allow to change the side in trade request. Trade always goes with the "side" subscription request was sent.
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Sell Term Trade - One-way-Offer quote test  ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Sell Term Trade - One-way-Offer quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    reqId = Math.floor(Math.random() * 100)
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)

                        if (res.rfsRates && i<10) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                console.log("RFS Spot Sell Term Trade - One-way-Offer quote test ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: parseInt(reqId)
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                                connection.send(JSON.stringify(wstradereq));

                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                console.log("RFS Spot Sell Term Trade - One-way-Offer quote test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                            }
                            i++;
                        } else if (res.rfsTradeAck) {
                             console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsTradeAck received")
                        } else if (res.rfsTradeResponses) {
                             console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsTradeResponses received")
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                        } else if (res.rfsWithdrawAck) {
                            console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsWithdrawAck received")
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

                it("RFS Spot Sell Term Trade - One-way-Offer quote test", function () {
                    console.log("RFS Spot Sell Term Trade - One-way-Offer quote test -> Trade details : ")
                    console.log(rfsTrade)
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage

                    assert.exists(trade.executionTime)
                    assert.equal("1000000.00",trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.settledAmount).split(".")[0], "settledAmount is not correct")
                    //assert.exists(trade.settledAmount)
                    assert.equal("1000000.00",trade.termAmount, "termAmount is not correct")
                    assert.equal(JSON.stringify(trade.dealtAmount/trade.spotRate).split(".")[0],JSON.stringify(trade.baseAmount).split(".")[0], "baseAmount is not correct")
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.equal("0.00000",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal("Spot",trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 USD ');

             });

            });

        });
    };

    let rfsSpotTradeNegativeTC = function(){
            let expiredQuoteId

        describe("RFS Swap Negative scenario ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });


       describe("RFS Spot Trade - Invalid quote test", function ()  {
            let dealtAmt = '1,000,000.00'

            before(function (done) {
                console.log('*************************** RFS Spot Trade - Invalid quote test  ************************** ' + new Date());
                console.log("RFS Spot Trade - Invalid quote test -> reqId = " + reqId)
                var subrequests = [{
                             symbol : rfsData.symbol,
                             amount : "1000000.0",
                             dealtCurrency : rfsData.baseCcy,
                             expiry: rfsData.expiry,
                             nearValueDate : "SPOT",
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeSpot,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth: 5,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: "RFS_SPOT_Invalid_Quote_" + reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS Spot Trade - Invalid quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                tradeDone = "false"
                i=1
                tradeReqId = Math.floor(Math.random() * 100)
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("RFS Spot Trade - Invalid quote test ->  rfsSpotSubscriptions: res :" + JSON.stringify(res))

                    if (res.rfsRates && i<10) {
                        rate = res.rfsRates[0]
                        if(rate.status === "A" && tradeDone === "false") {
                            console.log("RFS Spot Trade - Invalid quote test ->  rate response : " + JSON.stringify(rate))
                            rfsActiveQuote = rate
                            expiredQuoteId =rfsActiveQuote.bids[0].quoteId
                            quoteId = "123456"

                            console.log("RFS quote id = " + quoteId)
                            systemReqId = rate.requestId
                            var tradereq = [{
                                quoteId: quoteId,
                                side: "BUY",
                                symbol: rfsData.symbol,
                                dealtCurrency: rfsData.baseCcy,
                                tradeChannel: "DNET/RFS/BB",
                                clOrderId: parseInt(tradeReqId)
                            }]
                            var wstradereq = { rfsTrades : tradereq }
                            console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                            connection.send(JSON.stringify(wstradereq));

                            tradeDone = "true"
                        } else if (rate.status === "I") {
                            console.log("RFS Spot Trade - Invalid quote test ->waiting for Inactive quote")
                            rfsInactiveQuote = res.rfsRates[0]
                            console.log("RFS Spot Trade - Invalid quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                        }
                        i++;
                    } else if (res.rfsTradeAck) {
                      console.log("RFS Spot Trade - Invalid quote test -> rfsTradeAck received")

                    } else if (res.rfsTradeResponses) {
                      console.log("RFS Spot Trade - Invalid quote test -> rfsTradeResponses received")
                      rfsTrade = res.rfsTradeResponses[0]
                      //done()
                   // } else if (res.rfsWithdrawAck) {
                     //   console.log("============== RFS Spot Trade - Invalid quote test -> rfsWithdrawAck received")
                      //  done()
                    } else if (res.rfsResponses) {
                        rfsResponses = res.rfsResponses[0]
                        if (rfsResponses.rfsEvent == "TRADE_REQUEST_REJECTED") {
                            rfsTradeResponse = rfsResponses
                            console.log("RFS Spot Trade - Invalid quote test -> TRADE_REQUEST_REJECTED : " + JSON.stringify(rfsResponses))
                            connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                        } else if (rfsResponses.rfsEvent == "REQUEST_WITHDRAWN") {
                            console.log("RFS Spot Trade - Invalid quote test -> REQUEST_WITHDRAWN : " + JSON.stringify(rfsResponses))
                            done()
                        }
                        //done()
                    }
                }
            });


            //{"rfsResponses":[{"rfsEvent":"TRADE_REQUEST_REJECTED","rfsMessage":{"eventTime":"2021/10/30 08:26:00"},"errorCode":"QUOTE_NOT_AVAILABLE","clOrderId":"84"}]}

             it("RFS Spot Trade - Invalid quote test", function () {
                console.log("RFS Spot Trade - Invalid quote test -> Trade details : " + JSON.stringify(rfsResponses))
                rfsMessage = rfsTradeResponse.rfsMessage
                assert.equal('"TRADE_REQUEST_REJECTED"', JSON.stringify(rfsTradeResponse.rfsEvent))
                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"QUOTE_NOT_AVAILABLE"', JSON.stringify(rfsTradeResponse.errorCode))
                assert.exists(JSON.stringify(rfsTradeResponse.clOrderId))

         });

        });

       describe("RFS Spot Trade - Withdrawn quote test", function ()  {
            let dealtAmt = '1,000,000.00'

            before(function (done) {
                console.log('*************************** RFS Spot Trade - Withdrawn quote test  ************************** ' + new Date());

                console.log("RFS Spot Trade - Withdrawn quote test -> reqId = " + reqId)
                var subrequests = [{
                             symbol : rfsData.symbol,
                             amount : "1000000.0",
                             dealtCurrency : rfsData.baseCcy,
                             expiry: rfsData.expiry,
                             nearValueDate : "SPOT",
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeSpot,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth: 5,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: "RFS_SPOT_Withdraw_TradedQuote_" + reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS Spot Trade - Withdrawn quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                tradeDone = "false"
                i=1
                reqId = Math.floor(Math.random() * 100)
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("RFS Spot Trade - Withdrawn quote test ->  rfsSpotSubscriptions: res : " + JSON.stringify(res))

                    if (res.rfsRates && i<10) {
                        rate = res.rfsRates[0]
                        if(rate.status === "A" && tradeDone === "false") {
                            console.log("RFS Spot Trade - Withdrawn quote test ->  rate response : " + JSON.stringify(rate))
                            rfsActiveQuote = rate
                            //expiredQuoteId =rfsActiveQuote.bids[0].quoteId
                            quoteId = rfsActiveQuote.bids[0].quoteId
                            console.log("RFS quote id = " + quoteId)
                            systemReqId = rate.requestId
                            var tradereq = [{
                                quoteId: quoteId,
                                side: "BUY",
                                symbol: rfsData.symbol,
                                dealtCurrency: rfsData.baseCcy,
                                tradeChannel: "DNET/RFS/BB",
                                clOrderId: parseInt(reqId)
                            }]
                            var wstradereq = { rfsTrades : tradereq }
                            console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                            connection.send(JSON.stringify(wstradereq));

                            tradeDone = "true"
                        //} else if (rate.status === "I") {
                        //    console.log("RFS Spot Trade - Withdrawn quote test ->waiting for Inactive quote")
                        //    rfsInactiveQuote = res.rfsRates[0]
                        //    console.log("RFS Spot Trade - Withdrawn quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                        }
                        i++;
                    } else if (res.rfsTradeAck) {
                      console.log("RFS Spot Trade - Withdrawn quote test -> rfsTradeAck received")
                      connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                    } else if (res.rfsTradeResponses) {
                      console.log("RFS Spot Trade - Withdrawn quote test -> rfsTradeResponses received")
                      rfsTrade = res.rfsTradeResponses[0]
                    } else if (res.rfsWithdrawAck ) {
                        console.log("RFS Spot Trade - Withdrawn quote test -> rfsWithdrawAck received")
                        //done()
                    } else if (res.rfsResponses) {
                        rfsResponses = res.rfsResponses[0]
                        done()
                    }
                }
            });


            //{"rfsResponses":[{"rfsEvent":"TRADE_REQUEST_REJECTED","rfsMessage":{"eventTime":"2021/10/30 08:26:00"},"errorCode":"QUOTE_NOT_AVAILABLE","clOrderId":"84"}]}
            //{"rfsResponses":[{"requestId":"G4796976d517cd6ecfefc327","rfsEvent":"REQUEST_WITHDRAWN","rfsMessage":{"eventTime":"2021/10/31 15:17:09","eventName":"RFS Withdrawn","eventDetails":"RFS Request Withdrawn for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT"},"clOrderId":"G4796976d517cd6ecfefc327"}]}
             it("RFS Spot Trade - Withdrawn quote test", function () {
                console.log("RFS Spot Trade - Withdrawn quote test -> Withdraw response : " + JSON.stringify(rfsResponses))
                //rfsMessage = rfsResponses.rfsMessage
                assert.equal('"INTERNAL_SERVER_ERROR"', JSON.stringify(rfsResponses.errorCode))
                assert.exists(JSON.stringify(rfsResponses.requestId))
                assert.equal('"WITHDRAW_REQUEST_REJECTED"', JSON.stringify(rfsResponses.rfsEvent))
                assert.exists(JSON.stringify(rfsResponses.clOrderId))
         });

        });

       describe("RFS Spot Trade - Expired quote test", function ()  {
            let dealtAmt = '1,000,000.00'

            before(function (done) {
                console.log('*************************** RFS Spot Trade - Expired quote test  ************************** ' + new Date());
                console.log("RFS Spot Trade - Expired quote test -> reqId = " + reqId)
                var subrequests = [{
                             symbol : rfsData.symbol,
                             amount : "1000000.0",
                             dealtCurrency : rfsData.baseCcy,
                             expiry: 2,
                             nearValueDate : "SPOT",
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeSpot,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth: 5,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: "RFS_SPOT_Expired_Quote_" + reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS Spot Trade - Expired quote test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                tradeDone = "false"
                i=1
                tradeReqId = Math.floor(Math.random() * 100)
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("RFS Spot Trade - Expired quote test ->  res : " + JSON.stringify(res))

                    if (res.rfsRates && i<10) {
                        rate = res.rfsRates[0]
                        if(rate.status === "A" && tradeDone === "false") {
                            console.log("RFS Spot Trade - Expired quote test ->  rate response : " + JSON.stringify(rate))
                            rfsActiveQuote = rate
                            expiredQuoteId =rfsActiveQuote.requestId
                            systemReqId = rate.requestId
                        } else if (rate.status === "I") {
                            console.log("RFS Spot Trade - Expired quote test ->waiting for Inactive quote")
                            rfsInactiveQuote = res.rfsRates[0]
                            console.log("RFS Spot Trade - Expired quote test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                        }
                        i++;
                    } else if (res.rfsTradeAck) {
                      console.log("RFS Spot Trade - Expired quote test -> rfsTradeAck received")

                    } else if (res.rfsTradeResponses) {
                      console.log("RFS Spot Trade - Expired quote test -> rfsTradeResponses received")
                      rfsTrade = res.rfsTradeResponses[0]
                      //done()
                    } else if (res.rfsWithdrawAck) {
                        console.log("RFS Spot Trade - Expired quote test -> rfsWithdrawAck received")
                        done()
                    } else if (res.rfsResponses) {
                        rfsResponses = res.rfsResponses[0]
                        console.log("RFS Spot Trade - Expired quote test -> rfsResponses : " + JSON.stringify(rfsResponses))
                        if (rfsResponses.rfsEvent === "REQUEST_EXPIRED") {
                            var tradereq = [{
                                quoteId: expiredQuoteId,
                                side: "BUY",
                                symbol: rfsData.symbol,
                                dealtCurrency: rfsData.baseCcy,
                                tradeChannel: "DNET/RFS/BB",
                                clOrderId: parseInt(tradeReqId)
                            }]
                            var wstradereq = { rfsTrades : tradereq }
                            console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                            connection.send(JSON.stringify(wstradereq));
//
                        } else if (rfsResponses.rfsEvent === "TRADE_REQUEST_REJECTED") {

                        //connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                        done()
                        }
                    }
                }
            });


            // {"rfsResponses":[{"rfsEvent":"TRADE_REQUEST_REJECTED","rfsMessage":{"eventTime":"2021/10/31 16:03:44"},"errorCode":"QUOTE_NOT_AVAILABLE","clOrderId":"68"}]}

             it("RFS Spot Trade - Expired quote test", function () {
                console.log("RFS Spot Trade - Expired quote test -> Trade details : " + JSON.stringify(rfsResponses))
                rfsMessage = rfsResponses.rfsMessage
                assert.equal('"TRADE_REQUEST_REJECTED"', JSON.stringify(rfsResponses.rfsEvent))
                assert.exists(JSON.stringify(rfsMessage.eventTime))
                assert.equal('"QUOTE_NOT_AVAILABLE"', JSON.stringify(rfsResponses.errorCode))
                assert.exists(JSON.stringify(rfsResponses.clOrderId))

         });

        });

       describe("RFS Spot Buy Trade - Invalid DealtCcy test", function ()  {
        // DealtCcy doesnt get validated in trade request and order goes thru fine
        // Logged PLT-4921
            let dealtAmt = '1,000,000.00'

            before(function (done) {
                console.log('*************************** RFS Spot Buy Trade - Invalid DealtCcy test  ************************** ' + new Date());
                console.log("RFS Spot Buy Trade - Invalid DealtCcy test -> reqId = " + reqId)
                var subrequests = [{
                             symbol : rfsData.symbol,
                             amount : "1000000.0",
                             dealtCurrency : rfsData.baseCcy,
                             expiry: rfsData.expiry,
                             nearValueDate : "SPOT",
                             fixingDate : "" ,
                             side : rfsData.sideType2Way,
                             priceType : rfsData.priceTypeSpot,
                             customerAccount : rfsData.customerAccount,
                             customerOrg: rfsData.customerOrg,
                             priceViewType: rfsData.aggregatedView,
                             depth: 5,
                             channel : rfsData.channel,
                             providers: rfsData.providers,
                             clOrderId: "RFS_SPOT_Invalid_DealtCcy_" + reqId
                 }]
                var wsreq = { rfsSubscriptions : subrequests }
                console.log("RFS Spot Buy Trade - Invalid DealtCcy test ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                connection.send(JSON.stringify(wsreq));
                tradeDone = "false"
                i=1
                connection.onmessage = (e) => {
                    res = JSON.parse(e.data)
                    console.log("RFS Spot Buy Trade - Invalid DealtCcy test -> res " + JSON.stringify(res))

                    if (res.rfsRates && i<10) {
                        rate = res.rfsRates[0]
                        if(rate.status === "A" && tradeDone === "false") {
                            console.log("RFS Spot Buy Trade - Invalid DealtCcy test ->  rate response : " + JSON.stringify(rate))
                            rfsActiveQuote = rate
                            quoteId =rfsActiveQuote.bids[0].quoteId
                            console.log("RFS quote id = " + quoteId)
                            systemReqId = rate.requestId
                            var tradereq = [{
                                quoteId: quoteId,
                                side: "BUY",
                                symbol: rfsData.symbol,
                                dealtCurrency: "ABC",
                                tradeChannel: "DNET/RFS/BB",
                                clOrderId: "RFS_SPOT_Invalid_DealtCcy_" + reqId
                            }]
                            var wstradereq = { rfsTrades : tradereq }
                            console.log("Trade request sent is,,,\n" + JSON.stringify(wstradereq))
                            connection.send(JSON.stringify(wstradereq));
                            tradeDone = "true"
                        } else if (rate.status === "I") {
                            console.log("RFS Spot Buy Trade - Invalid DealtCcy test ->waiting for Inactive quote")
                            rfsInactiveQuote = res.rfsRates[0]
                            console.log("RFS Spot Buy Trade - Invalid DealtCcy test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                        }
                        i++;
                    } else if (res.rfsTradeAck) {
                      console.log("RFS Spot Buy Trade - Invalid DealtCcy test -> rfsTradeAck received")
                    } else if (res.rfsTradeResponses) {
                      console.log("RFS Spot Buy Trade - Invalid DealtCcy test -> rfsTradeResponses received")
                      rfsTrade = res.rfsTradeResponses[0]
                      done()
                    } else if (res.rfsWithdrawAck) {
                        done()
                    } else if (res.rfsResponses) {
                        rfsSubscriptionResponses = res.rfsResponses[0]
                        done()
                    }
                }
            });

//{"rfsTradeResponses":[{"trades":[{"orderId":"FXI9201040586","tradeId":"FXI9201040586","tradeType":"Spot","tenor":"SPOT",
//"tradeDate":"10/26/2021","valueDate":"10/28/2021","executionTime":"10/26/2021 06:07:00:189 GMT","maker":false,"orderSide":"Buy","status":"Verified",
//"instrument":"EUR/USD","dealtIns":"EUR","dealtAmount":"1000000.00","dAmt":0,"settledAmount":"1186390.00","sAmt":0,"baseAmount":"1000000.00",
//"bAmt":0,"termAmount":"1186390.00","tAmt":0,"spotRate":"1.18639","rate":"1.18639","dRate":0,"forwardPoints":"0.00000","customerAccount":"pfOrg",
//"customerOrg":"pfOrg","trader":"user1","counterParty":"WFNA","cptyLongName":"Wells Fargo","cptyTradeId":"","counterPartyAccount":"WFNAMB",
//"fRate":0,"fDAmt":0,"fSAmt":0,"fBAmt":0,"fTAmt":0,"counterpartyBLEI":"549300YLYOXGEYO4YK89","UPI":"EUR_USD_SPOT","UTI":"MCQRZU5ST1INP9201040586",
//"SEF":false,"externalRequestId":"74","requestId":"G4796976d517cbb35860d819","maskedLP":"WFNA","isnet":false,"isMidMarket":false,"mifidFields":{},
//"sefFields":{"cptyBLEI":"549300YLYOXGEYO4YK89","upi":"EUR_USD_SPOT"},"swapTrade":{},"brokerCDQ":{},"benchMarkRate":0,"cptyB":{},"outright":{},
//"channel":"DNET/RFS/BB","pricingType":"ESPnMDS","header":{"referenceId":"74","customerOrg":"pfOrg","customerId":"user1","customerAccount":"pfOrg"}}]
//,"rfsMessage":{"eventTime":"2021/10/26 06:07:00","eventName":"RFS Trade Verified","
//eventDetails":"Buy 1000000.00 EUR vs. 1186390.00 USD. Value Date: SPOT against FXI9201040586. From: WFNA, Counterparty WFNAMB. External ID:  Price verified."}}]}


            // {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,"clOrderId":"RFS_SPOT_Invalid_DealtCcy_66","status":"ERROR","errorCode":"RequestValidationError.CurrencyNotSpecified"}]}
            it("RFS Spot Buy Trade - Invalid DealtCcy test", function () {
                console.log("RFS Spot Buy Trade - Invalid DealtCcy test -> Trade details : " + JSON.stringify(rfsTrade))
                let ErrorMsg = rfsSubscriptionResponses[0]
                assert.equal("ERROR",ErrorMsg.status, "status is not correct")
                assert.equal("RequestValidationError.CurrencyNotSpecified",ErrorMsg.errorCode, "errorCode is not correct")
         });

        });


      });
    };

rfsSpotTradeTC();
//rfsSpotTradeNegativeTC();