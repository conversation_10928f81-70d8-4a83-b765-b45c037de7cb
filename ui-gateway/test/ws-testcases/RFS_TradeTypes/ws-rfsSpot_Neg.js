    const assert = require('chai').assert
    const expect = require('chai').expect
    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let flag

    let reqId = Math.floor(Math.random() * 100)

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let rfsSpotNegativeTC = function(){

        describe("RFS Spot Negative scenario ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close();
            });

            //{ "rfsSubscriptions" : [ { "symbol": "EUR/USD", "amount": "1000000.0", "dealtCurrency": "EUR", "expiry": 15, "nearValueDate": "1W", "farDealtAmount" : "1000000.0","farValueDate" : "2W", "fixingDate" : "" , "farFixingDate" : "", "side": "BUY", "priceType": "Swap", "customerAccount": "pfOrg", "customerOrg": "pfOrg", "priceViewType": 1, "depth": 2, "channel": "DNET/RFS/BB", "providers": ["NTFX","MSFX","SG","SUCD","UBS","WFNA"], "clOrderId": "view1MultiLP2" } ] }

            describe("Invalid CP ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid CP ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_InvalidCP_" + reqId
                    console.log("Rfs spot - Invalid CP - > reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : 'ABC/ACB',
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid CP - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid CP - > res : ")
                        console.log(res)

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        }
                    }
                });

//Sample response
//Rfs spot - Invalid CP - > rfsSpotSubscriptions: request : {"rfsSubscriptions":[{"symbol":"ABC/ACB","amount":"1000000.0","dealtCurrency":"EUR","expiry":10,"nearValueDate":"SPOT","fixingDate":"","side":"TWO_WAY","priceType":"Spot","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":15}]}
//Rfs spot - Invalid CP - > res.rfsSubscriptionAck : {"request":{"symbol":"ABC/ACB","amount":"1000000.0","dealtCurrency":"EUR","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"expiry":10,"side":"TWO_WAY","priceType":"Spot","channel":"DNET/RFS/BB","customerOrg":"pfOrg","customerAccount":"pfOrg","nearValueDate":"SPOT","fixingDate":"","clOrderId":"15","priceViewType":1,"blockTrade":false,"intentToClear":false,"depth":5,"mtf":false,"enableCustomerSpreads":false,"isManualRequest":false},"status":"received"}
//Rfs spot - Invalid CP - > res.rfsSubscriptionResponses received{"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/22 08:46:26","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"15","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}
//Rfs spot - Invalid CP - > rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/22 08:46:26","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"15","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}

                it("Invalid CP", function () {
                    console.log("Rfs spot - Invalid CP - > rfsSubscriptionResponses -  : " )
                    console.log(rfsSubscriptionResponses)
//Rfs spot - Invalid CP - > res.rfsSubscriptionResponses received{"expiryTimeInSeconds":0,"clOrderId":"3","status":"ERROR","errorCode":"RequestValidationError.InvalidCurrency"}

                    assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                    assert.exists(rfsSubscriptionResponses.clOrderId)
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('RequestValidationError.InvalidCurrency', rfsSubscriptionResponses.errorCode)
                });
            });

            describe("Invalid Amt ", function () {
            //{"expiryTimeInSeconds":0,"clOrderId":"2","status":"ERROR"}
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid Amt ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_InvalidAmt_" + reqId
                    console.log("Rfs spot - Invalid Amt - > tempReqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "abc",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid Amt - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid Amt - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        }
                    }
                });

                //"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

                it("Invalid Amt", function () {
                    console.log("Rfs spot - Invalid Amt - > rfsSubscriptionResponses -  : " )
                    console.log(rfsSubscriptionResponses)

                    assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                    assert.exists(rfsSubscriptionResponses.clOrderId)
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('RequestValidationError.amount', rfsSubscriptionResponses.errorCode)
               });

            });

            describe("Invalid dealtCurrency ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid dealtCurrency ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_InvalidAmt_" + reqId
                    console.log("Rfs spot - Invalid dealtCurrency - > tempReqId = " + tempReqId )

                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "ABC",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid dealtCurrency - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid dealtCurrency - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        }
                    }
                });

//==========Rfs Spot - rate response : {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,
//"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

                it("Invalid dealtCcy", function () {
                    console.log("Rfs spot - Invalid dealtCurrency - > rfsSubscriptionResponses -  : " )
                    console.log(rfsSubscriptionResponses)

                    assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                    assert.exists(rfsSubscriptionResponses.clOrderId)
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('RequestValidationError.InvalidCurrency', rfsSubscriptionResponses.errorCode)
                });

            });

            describe("Invalid nearValueDate ", function () {
// AP-10389
// RFSSpot is ignoring value date, it allows 1W etc tenors too in the request, but actually considers as spot request based on priceType
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid nearValueDate ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_InvalidNearVD_" + reqId
                    console.log("Rfs spot - Invalid nearValueDate - > tempReqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "abc",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid nearValueDate -> rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid nearValueDate -> res : " + JSON.stringify(res))
                       if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        }
                    }
                });

//==========Rfs Spot - rate response : {"rfsSubscriptionResponses":[{"expiryTimeInSeconds":0,
//"rfsMessage":{"eventTime":"2021/09/13 12:22:35","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for ABC/ACB with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"73","status":"ERROR","errorCode":"INCORRECT_REQUEST_PARAMS"}]}

                it("Invalid nearValueDate", function () {
                     console.log("Rfs spot - Invalid nearValueDate - > rfsSubscriptionResponses -  : " )
                     console.log(rfsSubscriptionResponses)

                     assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                     assert.exists(rfsSubscriptionResponses.clOrderId)
                     assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                     assert.equal('ERROR', rfsSubscriptionResponses.status)
                     assert.equal('INCORRECT_REQUEST_PARAMS', rfsSubscriptionResponses.errorCode)
               });
            });

            describe("Without nearValueDate", function () {
// RFSSpot is ignoring value date, it allows 1W etc tenors too in the request, but actually considers as spot request based on priceType
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Without nearValueDate ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_Without_NearVD_" + reqId
                    console.log("Rfs spot - Without nearValueDate : rate - reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Without nearValueDate -> rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Without nearValueDate - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = res.rfsSubscriptionAck[0]
                            console.log("Rfs spot - Without nearValueDate - > res.rfsSubscriptionAck : " + rfsSubscriptionAck)
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        }
                    }
                });

                //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
                it("Without nearValueDate", function () {
                     console.log("Rfs spot - Without nearValueDate - > rfsSubscriptionResponses -  : " )
                     console.log(rfsSubscriptionResponses)

                     assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                     assert.exists(rfsSubscriptionResponses.clOrderId)
                     assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                     assert.equal('ERROR', rfsSubscriptionResponses.status)
                     assert.equal('RequestValidationError.ValueDateNotSpecified', rfsSubscriptionResponses.errorCode)
               });
            });

            describe("Without fixingDate", function () {
// RFSSpot is ignoring value date, it allows 1W etc tenors too in the request, but actually considers as spot request based on priceType
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Without fixingDate ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_WithoutFixingDate_" + reqId
                    console.log("Rfs spot - Without fixingDate : rate - reqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 //nearValueDate : "SPOT",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Without fixingDate -> rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Without fixingDate -> res   : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"Rfs spot - Without fixingDate -> rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
                });

                //rfsSubscriptionResponses = {"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/15 09:23:05","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date null. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"17","status":"ERROR","errorCode":"Request.Validation.Tenor/ValueDate.Missing"}
                it("Without fixingDate", function () {
                    console.log("Rfs spot - Without fixingDate and valuedate - > rfsSubscriptionResponses -  : " )
                    console.log(rfsSubscriptionResponses)

                    assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                    assert.exists(rfsSubscriptionResponses.clOrderId)
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('RequestValidationError.ValueDateNotSpecified', rfsSubscriptionResponses.errorCode)

               });
            });

            describe("Invalid side ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid side ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_InvalidSide_" + reqId
                    console.log("Rfs spot - Invalid side -> tempReqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : "ONE_WAY",
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid side - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid side - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        } else if (res.errors) {
                            errors = res.errors
                            done()
                        }
                    }
                });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                it("Invalid Side", function () {
                    console.log("Rfs spot - Invalid side - > errors = ")
                    console.log(errors)
                    assert.equal('1', JSON.parse(JSON.stringify(errors[0].errorCode)))
                    assert.equal('Not a valid request.', JSON.parse(JSON.stringify(errors[0].errorMessage)))
                });
            });

            describe("Invalid priceType ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid priceType ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_InvalidPriceType_" + reqId
                    console.log("Rfs spot - Invalid priceType -> reqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : "xspot",
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid priceType - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid priceType - > res : " + JSON.stringify(res))

                        if (res.rfsSubscriptionAck) {
                            rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        } else if (res.errors) {
                            errors = res.errors[0]
                            done()
                        }
                    }
                });

//{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                it("Invalid priceType", function () {
                    console.log("Rfs spot - Invalid priceType - > errors = ")
                    console.log(errors)

                    assert.equal('1', errors.errorCode)
                    assert.equal('Not a valid request.', errors.errorMessage)
                });
            });

            describe("Invalid customerAccount ", function () {
          // AP-10414 -> It should get rejected, but working fine.
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid customerAccount ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_Invalid_CustomerAcct_" + reqId
                    console.log("Rfs spot - Invalid customerAccount -> reqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : "abc",
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid customerAccount - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid customerAccount ->  res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        } else if (res.errors) {
                            errors = res.errors
                            done()
                        }
                    }

                });

                it("Invalid customerAccount", function () {
                     console.log("Rfs spot - Invalid customerAccount -> rfsSubscriptionResponses : " )
                    console.log(rfsSubscriptionResponses)

                    assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                    assert.exists(rfsSubscriptionResponses.clOrderId)
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('RequestValidationError.InvalidAccount', rfsSubscriptionResponses.errorCode)
                });
            });

            describe("Invalid customerOrg ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid customerOrg ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_Invalid_CustomerOrg_" + reqId
                    console.log("Rfs spot - Invalid customerOrg -> reqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: "ABCOrg",
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid customerOrg - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid customerOrg -> res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }

                });

                it("Invalid customerOrg", function () {
                    console.log("Rfs spot - Invalid customerOrg -> rfsSubscriptionResponses : " )
                    console.log(rfsSubscriptionResponses)

                    assert.equal('0', rfsSubscriptionResponses.expiryTimeInSeconds)
                    assert.exists(rfsSubscriptionResponses.clOrderId)
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('RequestValidationError.InvalidOrg', rfsSubscriptionResponses.errorCode)
                });

          });

/* ====================== Below tcs are not working as expected, logged PLT-4023 for invalid PriceViewType, channel etc ==========*/
/*
            describe("Invalid priceViewType ", function () {
           // priceViewType =1 -> aggregated view, any other number (0 or other positive integers are considered as non aggregated view where each LP quote is sent separately
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid priceViewType ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_Invalid_priceViewType_" + reqId
                    console.log("Rfs spot - Invalid CustomerOrg -> reqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: 8,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid priceViewType - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Invalid priceViewType ->  res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }

                });

                it("Invalid priceViewType", function () {
                    console.log("Rfs spot - Invalid priceViewType -> quote : " )
                    console.log(rfsSubscriptionResponses)

                    assert.exists(rfsSubscriptionResponses.expiryTimeInSeconds)
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('RequestValidationError.InvalidAccount', rfsSubscriptionResponses.errorCode)
                    });

           });

            describe("Negative priceViewType ", function () {
           // priceViewType =1 -> aggregated view, any other number (0 or other positive/Negative integers are considered as non aggregated view where each LP quote is sent separately
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Negative priceViewType ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_Invalid_priceViewType_" + reqId
                    console.log("Rfs spot - Negative priceViewType -> reqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: "-2",
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Negative priceViewType - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Negative priceViewType ->  res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                          done()
                        } else if (res.rfsSubscriptionResponses) {
                            fsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        }
                    }

                });

                it("Negative priceViewType", function () {
                    console.log("Rfs spot - Negative priceViewType -> rfsSubscriptionResponses : " )
                    console.log(rfsSubscriptionResponses)
                    assert.exists(rfsSubscriptionResponses.expiryTimeInSeconds)
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('RequestValidationError.InvalidAccount', rfsSubscriptionResponses.errorCode)
                });

          });

            describe("Invalid channel ", function () {
          // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid channel ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_Invalid_Channel_" + reqId
                    console.log("Rfs spot - Invalid channel -> reqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : "abc",
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid channel - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                   connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs spot - Invalid channel -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }

                });

                it("Invalid channel", function () {
                     console.log("Rfs spot - Invalid channel -> quote : " + JSON.stringify(rfsActiveQuote))
                     let bidsArray = rfsActiveQuote.bids
                     let bidRate = bidsArray[0]
                     let offerArray = rfsActiveQuote.offers
                     let offerRate = offerArray[0]
                     // bid rate validation
                     assert.exists(bidRate.rate)
                     assert.notEqual("0",bidRate.rate, "rate is zero")
                     // offer rate validation
                     assert.exists(offerRate.rate)
                     assert.notEqual("0",offerRate.rate, "rate is zero")
                });
          });

            describe("without channel tag", function () {
           // channel is getting ignored here, assuming it will be a valid value when integrated with client since user cant select channel manually in UI clients
                 let dealtAmt = '1,000,000.00'

                 before(function (done) {
                     console.log('*************************** rfs spot Negative TC - without channel tag ************************** ' + new Date());
                     tempReqId = "RFS_Spot_NegativeTest_Without_Channel_" + reqId
                     console.log("Rfs spot - without channel tag -> reqId = " + tempReqId )
                     var subrequests = [{
                                  symbol : 'EUR/USD',
                                  amount : "1000000.0",
                                  dealtCurrency : "EUR",
                                  expiry: rfsData.expiry,
                                  nearValueDate : "SPOT",
                                  fixingDate : "" ,
                                  side : rfsData.sideType2Way,
                                  priceType : rfsData.priceTypeSpot,
                                  customerAccount : rfsData.customerAccount,
                                  customerOrg: rfsData.customerOrg,
                                  priceViewType: rfsData.aggregatedView,
                                  depth: 5,
                                  providers: rfsData.providers,
                                  clOrderId: tempReqId
                      }]
                     var wsreq = { rfsSubscriptions : subrequests }
                     console.log("Rfs spot - without channel tag - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                     connection.send(JSON.stringify(wsreq));
                     i = 1

                     connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - without channel tag - > res : " + JSON.stringify(res))

                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"Rfs spot - Without fixingDate -> rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsSubscriptionResponses) {
                            rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }

                     }
                 });

//Rfs spot - without channel tag - > res.rfsSubscriptionResponses received{"expiryTimeInSeconds":0,"rfsMessage":{"eventTime":"2021/09/23 08:25:55","eventName":"RFS Failed","eventDetails":"RFS Submission Failed for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT. RFS submission failed for NTFX,MSFX,SG,SUCD,UBS,WFNA"},"clOrderId":"93","status":"ERROR","errorCode":"channel:may not be null"}
                it("without channel tag", function () {
                    assert.exists(rfsSubscriptionResponses.clOrderId)
                    assert.equal('OK',rfsSubscriptionResponses.status)
              });

           });
*/
            describe("Invalid depth ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid depth ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_Invalid_Depth_" + reqId
                    console.log("Rfs spot - Invalid depth -> reqId = " + tempReqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: "pfOrg",
                                 priceViewType: rfsData.aggregatedView,
                                 depth: "a",
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid depth - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                         res = JSON.parse(e.data)
                         console.log("Rfs spot - Invalid depth - > res : " + JSON.stringify(res))

                         if (res.rfsSubscriptionAck) {
                             rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                         } else if (res.rfsSubscriptionResponses) {
                             rfsSubscriptionResponses = JSON.stringify(res.rfsSubscriptionResponses[0])
                             done()
                         } else if (res.errors) {
                             errors = res.errors[0]
                             done()
                         }
                     }
                 });

 //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                 it("Invalid Depth", function () {
                     console.log("Rfs spot - Invalid depth - > errors = " + JSON.stringify(errors))
                     assert.equal('1', errors.errorCode)
                     assert.equal('Not a valid request.', errors.errorMessage)
                 });
            });

            describe("Invalid providers ", function () {
            // subscription is successfull, but will not get rates
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs spot Negative TC - Invalid customerOrg ************************** ' + new Date());
                    tempReqId = "RFS_Spot_NegativeTest_Invalid_Providers_" + reqId
                    console.log("Rfs spot - Invalid Providers -> reqId = " + reqId )
                    var subrequests = [{
                                 symbol : 'EUR/USD',
                                 amount : "1000000.0",
                                 dealtCurrency : "EUR",
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: ["ABC"],
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Invalid providers - > rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                     connection.onmessage = (e) => {
                          res = JSON.parse(e.data)
                          console.log("Rfs spot - Invalid providers - > res : " + JSON.stringify(res))

                          if (res.rfsSubscriptionAck) {
                              rfsSubscriptionAck = JSON.stringify(res.rfsSubscriptionAck[0])
                          } else if (res.rfsSubscriptionResponses) {
                              rfsSubscriptionResponses = res.rfsSubscriptionResponses[0]
                              done()
                          } else if (res.errors) {
                              errors = res.errors
                              done()
                          }
                      }
                  });

//No error message comes, request getting expired
  //{"errors":[{"errorCode":1,"errorMessage":"Not a valid request."}]}
                  it("Invalid Providers", function (done) {
                  // subscription is successfull, but will not get rates
                    console.log("Rfs spot - Invalid providers - > errors = " + JSON.stringify(rfsSubscriptionResponses))
                    assert.equal(tempReqId,rfsSubscriptionResponses.clOrderId)
                    assert.equal('ERROR', rfsSubscriptionResponses.status)
                    assert.equal('"INTERNAL_SERVER_ERROR"', JSON.stringify(res.rfsSubscriptionResponses[0].errorCode))
                    console.log("Rfs spot -Neg scenarios execution Completed===")
                    done()
                  });
               });



        });
    };

rfsSpotNegativeTC();
