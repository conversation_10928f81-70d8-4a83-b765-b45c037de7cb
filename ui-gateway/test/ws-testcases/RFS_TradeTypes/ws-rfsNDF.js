    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')

    //const login = require('../login').login
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let reqId
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors

    //Add term currency TCS

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let getBusinessDayDates = function() {
    let today = new Date();

    // Get today's date in the format YYYY-MM-DD
    let fromValueDate = new Date(today);

    // Get the 'to' date, initially set to today + 4 days
    let toDate = new Date(today);
    toDate.setDate(today.getDate() - 2);

    // Function to check if a date is Saturday or Sunday
    function isWeekend(date) {
        return date.getDay() === 6 || date.getDay() === 0;  // 6 = Saturday, 0 = Sunday
    }

    // Adjust 'toDate' if it falls on a weekend
    while (isWeekend(toDate)) {
        toDate.setDate(toDate.getDate() + 1); // Move to next day
    }

       // Adjust 'fromValueDate' if it falls on a weekend
        while (isWeekend(fromValueDate)) {
            fromValueDate.setDate(fromValueDate.getDate() + 1); // Move to next day
        }

    // Format toDate in YYYY-MM-DD format
  // Format toDate in YYYY-MM-DD format
     let toValueDate = toDate.getFullYear() + '-'
                     + (toDate.getMonth() + 2).toString().padStart(2, '0') + '-'
                     + toDate.getDate().toString().padStart(2, '0');

          fromValueDate = fromValueDate.getFullYear() + '-'
                             + (fromValueDate.getMonth() + 2).toString().padStart(2, '0') + '-'
                             + fromValueDate.getDate().toString().padStart(2, '0');

    // Return both the 'from' and 'to' dates
    return {
        fromDate: fromValueDate,
        toDate: toValueDate
    };
}

let rfsNDFTC = function(){

        describe("RFS NDF ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });


            describe("Two-Way Rate test ", function () {
            //// rfsNDFSubscriptions: request : {"rfsSubscriptions":[{"symbol":"USD/INR","amount":"1000000.0","dealtCurrency":"USD","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"TWO_WAY","priceType":"NDF","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":70}]}
                let dealtAmt = '1000000'; //'1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF - Two-Way Rate test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Two-Way RateTest -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - Two-Way RateTest ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - Two-Way RateTest -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs NDF - Two-Way RateTest -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs NDF - Two-Way RateTest -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });
                // old -- quote : {"requestId":"G4796976d517c17b230031f5f","priceType":"NDF","effectiveTime":0,"symbol":"USD/INR","ttl":119,"dealtCurrency":"USD","status":"A","nearSettleDate":"10/05/2021","bids":[{"legType":0,"quoteId":"G-4796976cf-17c17b231f8-SGR-44-pfOrg-SG-1632485126656","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,440.00","provider":"SG","rate":1.18644,"spotRate":1.18634,"forwardPoint":0.0001,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b23118-UBSA-37f-pfOrg-UBS-1632485126434","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,300.00","provider":"UBS","rate":1.1863,"spotRate":1.1862,"forwardPoint":0.0001,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c17b23118-UBSA-37f-pfOrg-UBS-1632485126434","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,730.00","provider":"UBS","rate":1.18673,"spotRate":1.18653,"forwardPoint":0.0002,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b231f8-SGR-44-pfOrg-SG-1632485126656","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,740.00","provider":"SG","rate":1.18674,"spotRate":1.18654,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
                //{"requestId":"G479697b321960aaa14273bbb","priceType":"NDF","effectiveTime":0,"symbol":"USD/INR","ttl":120,"dealtCurrency":"USD","status":"A","nearValueDate":"2025-04-15","nearFixingDate":"2025-04-11","bids":[{"legType":0,"quoteId":"G-479697b32-1960aaa1799-VISA-38f5a2-pfAutomationWSuser1-VISA-1743935641498","type":"BID","dealtAmount":1000000,"settledAmount":1188100,"provider":"VISA","rate":1.1881,"spotRate":1.188,"forwardPoint":0.0001,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-479697b32-1960aaa1799-VISA-38f5a2-pfAutomationWSuser1-VISA-1743935641498","type":"OFFER","dealtAmount":1000000,"settledAmount":1189200,"provider":"VISA","rate":1.1892,"spotRate":1.189,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
                it("Rate test", function () {
                    console.log("Rfs NDF - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
					assert.exists(rfsActiveQuote.nearValueDate)
					assert.exists(rfsActiveQuote.nearFixingDate)
                    assert.exists(rfsActiveQuote.ttl)
					assert.exists(rfsActiveQuote.mids)
                    // bid rate validation
                    assert.exists(bidRate.quoteId)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.exists(offerRate.quoteId)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate)

                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.equal(rfsData.priceTypeNdf, rfsActiveQuote.priceType)
                    assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

                    assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
               });

            });

            describe("One-Way-Bid Rate test ", function () {
            // //rfsNDFSubscriptions: request : {"rfsSubscriptions":[{"symbol":"USD/INR","amount":"1000000.0","dealtCurrency":"USD","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"BUY","priceType":"NDF","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":66}]}
                let dealtAmt = '1000000'; //'1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF - One-Way-Bid Rate test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - One-Way-Bid Rate test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "2W",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - One-Way-Bid Rate test  ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - One-Way-Bid Rate test  ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - One-Way-Bid Rate test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - One-Way-Bid Rate test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - RateTest -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs NDF - One-Way-Bid Rate test -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs NDF - One-Way-Bid Rate test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

//  quote : {"requestId":"G4796976d517c17b7c0871f8a","priceType":"NDF","effectiveTime":0,"symbol":"USD/INR","ttl":119,"dealtCurrency":"USD","status":"A","nearSettleDate":"10/05/2021","bids":[],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c17b7c2fd-NTFXA-3a1-pfOrg-NTFX-1632485491463","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,590.00","provider":"NTFX","rate":1.18659,"spotRate":1.18639,"forwardPoint":0.0002,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b7c28e-UBSA-3a0-pfOrg-UBS-1632485491355","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,730.00","provider":"UBS","rate":1.18673,"spotRate":1.18653,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
                it("Rate test", function () {
                    console.log("Rfs NDF - One-Way-Bid Rate test -> quote : " + JSON.stringify(rfsActiveQuote))
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeNdf, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")

                    assert.lengthOf(rfsActiveQuote.bids,0)
                    assert.lengthOf(rfsActiveQuote.mids,0)
                    //assert.lengthOf.notEqual(rfsActiveQuote.offers,0,"offer size is zero")

                    // offer rate
                    assert.equal('0',offerRate.legType, "legtype is not zero")
                    assert.exists(offerRate.quoteId)
                    assert.equal('OFFER',offerRate.type, "type is not OFFER")
                    assert.equal(dealtAmt,offerRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(offerRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual('0',offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate, "midRate doesnt exist")

               });

            });


            describe("One-Way-Offer Rate test ", function () {
                let dealtAmt = '1000000' ;  // '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF - One-Way-Offer Rate test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - One-Way-Offer Rate test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
                                 fixingDate : "" ,
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - One-Way-Offer Rate test  ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - One-Way-Offer Rate test  ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - One-Way-Offer Rate test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - One-Way-Offer Rate test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - One-Way-Offer Rate test -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs NDF - One-Way-Offer Rate test -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs NDF - One-Way-Offer Rate test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

//Rfs NDF - One-Way-Offer Rate test  ->  rate response : {"requestId":"G4796976d517c17bc28cb1fd5","priceType":"NDF","effectiveTime":0,"symbol":"USD/INR","ttl":9,"dealtCurrency":"USD","status":"A","nearSettleDate":"10/05/2021","bids":[{"legType":0,"quoteId":"G-4796976cf-17c17bc2b17-UBSA-3af-pfOrg-UBS-1632485780256","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,300.00","provider":"UBS","rate":1.1863,"spotRate":1.1862,"forwardPoint":0.0001,"midRate":0}],"offers":[],"mids":[]}
                it("Rate test", function () {
                    console.log("Rfs NDF - One-Way-Offer Rate test -> quote : " + JSON.stringify(rfsActiveQuote))
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
 //                   let offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
                    assert.equal(rfsData.priceTypeNdf, rfsActiveQuote.priceType)
                    assert.exists(rfsActiveQuote.effectiveTime, "effectiveTime doesnt exist")
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.exists(rfsActiveQuote.ttl, "ttl doesnt exist")
                    assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status, "quote is inactive")
                    assert.exists(rfsActiveQuote.nearValueDate, "nearValueDate doesnt exist")
                    assert.exists(rfsActiveQuote.nearFixingDate, "nearFixingDate doesnt exist")

                    assert.lengthOf(rfsActiveQuote.offers,0)
                    assert.lengthOf(rfsActiveQuote.mids,0)

                    // bid rate
                    assert.equal('0',bidRate.legType, "legtype is not zero")
                    assert.exists(bidRate.quoteId)
                    assert.equal('BID',bidRate.type, "type is not OFFER")
                    assert.equal(dealtAmt,bidRate.dealtAmount, "dealtAmount is not correct")
                    assert.exists(bidRate.settledAmount, "settledAmount doesnt exist")
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual('0',bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate, "midRate doesnt exist")

               });

            });

            describe("Withdraw response test ", function () {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs NDF - Withdraw response test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Withdraw response test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: "40", //rfsData.expiry,
                                 nearValueDate : "2M",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Withdraw response Test ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - Withdraw response Test ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - Withdraw response test ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - Withdraw response Test -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - Withdraw response Test -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs NDF - Withdraw response Test -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs NDF - Withdraw response Test -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });

//rfsWithdrawResponse : {"requestId":"G4796976d517c27fc75c2221","rfsEvent":"REQUEST_WITHDRAWN","rfsMessage":{"eventTime":"2021/09/27 16:00:29","eventName":"RFS Withdrawn","eventDetails":"RFS Request Withdrawn for USD/INR with Dealt currency USD. 2-Way 1,000,000.00. Value Date 1W"},"clOrderId":"G4796976d517c27fc75c2221"}
                it("Withdraw request test", function () {
                    console.log("=====Rfs NDF - Withdraw response Test -> withdraw response : " + JSON.stringify(rfsWithdrawResponse))
                    //rfsWithdrawResponse = res.rfsWithdrawAck
                    assert.exists(rfsWithdrawResponse.requestId)
                    assert.equal('REQUEST_WITHDRAWN', rfsWithdrawResponse.rfsEvent)
                    assert.exists(rfsWithdrawResponse.rfsMessage.eventTime)
                    assert.equal('RFS Withdrawn', rfsWithdrawResponse.rfsMessage.eventName)
                    expect(JSON.stringify(rfsWithdrawResponse.rfsMessage.eventDetails)).to.have.string('"RFS Request Withdrawn for USD/INR with Dealt currency USD. 2-Way 1,000,000.00. Value Date 2M');
                    assert.exists(rfsWithdrawResponse.clOrderId)
              });

            });


            describe("Two-Way Rate test- with fixing date", function () {
            //// rfsNDFSubscriptions: request : {"rfsSubscriptions":[{"symbol":"USD/INR","amount":"1000000.0","dealtCurrency":"USD","expiry":10,"nearValueDate":"1W","fixingDate":"","side":"TWO_WAY","priceType":"NDF","customerAccount":"pfOrg","customerOrg":"pfOrg","priceViewType":1,"depth":5,"channel":"DNET/RFS/BB","providers":["NTFX","MSFX","SG","SUCD","UBS","WFNA"],"clOrderId":70}]}
                let dealtAmt = '1000000'; //'1,000,000.00'

            // Example of using the function
            let { fromDate, toDate } = getBusinessDayDates();
            console.log('From Date:', fromDate);
            console.log('To Date:', toDate);

                before(function (done) {
                    console.log('*************************** rfs NDF - Two-Way Rate test ************************** ' + new Date());
                    reqId = Math.floor(Math.random() * 100)
                    console.log("Rfs NDF - Two-Way RateTest -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbolNdf,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcyNdf,
                                 expiry: rfsData.expiry,
                                 nearValueDate : fromDate,
                                 fixingDate : toDate ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeNdf,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: parseInt(reqId)
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs NDF - Two-Way RateTest ->  rfsNDFSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                console.log("Rfs NDF - Two-Way RateTest ->  rate response : " + JSON.stringify(rate))
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                console.log("Rfs NDF - Two-Way RateTest ->waiting for Inactive quote")
                                rfsInactiveQuote = res.rfsRates[0]
                                console.log("Rfs NDF - Two-Way RateTest -> rfsInactiveQuote : " + JSON.stringify(rfsInactiveQuote))
                             } else if (i ===3) {
                                console.log("Rfs NDF - Two-Way RateTest -> systemReqId : " + systemReqId)
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            console.log("Rfs NDF - Two-Way RateTest -> rfsWithdrawAck received")
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            console.log("Rfs NDF - Two-Way RateTest -> rfsWithdrawResponse : " + JSON.stringify(rfsWithdrawResponse))
                            done()
                        }
                    }
                });
                // old -- quote : {"requestId":"G4796976d517c17b230031f5f","priceType":"NDF","effectiveTime":0,"symbol":"USD/INR","ttl":119,"dealtCurrency":"USD","status":"A","nearSettleDate":"10/05/2021","bids":[{"legType":0,"quoteId":"G-4796976cf-17c17b231f8-SGR-44-pfOrg-SG-1632485126656","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,440.00","provider":"SG","rate":1.18644,"spotRate":1.18634,"forwardPoint":0.0001,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b23118-UBSA-37f-pfOrg-UBS-1632485126434","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,300.00","provider":"UBS","rate":1.1863,"spotRate":1.1862,"forwardPoint":0.0001,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c17b23118-UBSA-37f-pfOrg-UBS-1632485126434","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,730.00","provider":"UBS","rate":1.18673,"spotRate":1.18653,"forwardPoint":0.0002,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c17b231f8-SGR-44-pfOrg-SG-1632485126656","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,740.00","provider":"SG","rate":1.18674,"spotRate":1.18654,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
                //{"requestId":"G479697b321960aaa14273bbb","priceType":"NDF","effectiveTime":0,"symbol":"USD/INR","ttl":120,"dealtCurrency":"USD","status":"A","nearValueDate":"2025-04-15","nearFixingDate":"2025-04-11","bids":[{"legType":0,"quoteId":"G-479697b32-1960aaa1799-VISA-38f5a2-pfAutomationWSuser1-VISA-1743935641498","type":"BID","dealtAmount":1000000,"settledAmount":1188100,"provider":"VISA","rate":1.1881,"spotRate":1.188,"forwardPoint":0.0001,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-479697b32-1960aaa1799-VISA-38f5a2-pfAutomationWSuser1-VISA-1743935641498","type":"OFFER","dealtAmount":1000000,"settledAmount":1189200,"provider":"VISA","rate":1.1892,"spotRate":1.189,"forwardPoint":0.0002,"midRate":0}],"mids":[]}
                it("Rate test", function () {
                    console.log("Rfs NDF - Two-Way RateTest -> quote : " + JSON.stringify(rfsActiveQuote))
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
					assert.exists(rfsActiveQuote.nearValueDate)
					assert.exists(rfsActiveQuote.nearFixingDate)
                    assert.exists(rfsActiveQuote.ttl)
					assert.exists(rfsActiveQuote.mids)
                    // bid rate validation
                    assert.exists(bidRate.quoteId)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.notEqual("0",bidRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.exists(offerRate.quoteId)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.notEqual("0",offerRate.forwardPoint, "forwardPoint is zero")
                    assert.exists(offerRate.midRate)

                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbolNdf, rfsActiveQuote.symbol)
                    assert.equal(rfsData.priceTypeNdf, rfsActiveQuote.priceType)
                    assert.equal(rfsData.baseCcyNdf, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

                    assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
                    assert.equal(rfsData.fixingDate, toDate)

               });

            });

        });
    };

rfsNDFTC();
