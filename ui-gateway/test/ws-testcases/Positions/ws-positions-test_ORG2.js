const assert = require('chai').assert
const expect = require('chai').expect
const WebSocket = require('ws')
//const delay = require('delay')

const env = require('../../config/properties').env
const mdData = require('../../config/properties').mdData
const user = require('../../config/properties').user
const posData = require('../../config/properties').posData

let connection
let positionSubscriptionResponses
let positionUnsubscriptionResponses
let reqId
let posLevel
let inclCustPos
let today = new Date();
//let fromValueDate =  (today.getMonth()+1) + '/' + today.getDate()+ '/'+ today.getFullYear();
//let toValueDate = (today.getMonth()+1) + '/' + today.getDate()+ '/'+ (today.getFullYear()+10);
// current timestamp in milliseconds
let ts = Date.now();
let date_ob = new Date(ts);
let date = date_ob.getDate();
let month = date_ob.getMonth() + 1;
let year = date_ob.getFullYear();
let fromValueDate = month + "/" + date + "/" + year;
date = date_ob.getDate() + 2;
let toValueDate = month + "/" + date + "/" + year;

// Login credentials should be for MDF enabled org
// Org should have positions for positive scenarios to display
//run with --exit option

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let PostitionTCORG2 = function(){

        		before(function(done) {
        		    wsconnect(done)
        		  });


        	    after(function() {
        		    connection.close()
        	    });


        describe("PostitionTCORG12: Without fromValueDate and toValueDate tags", function () {
        	before(function (done) {
        	            posLevel = "ORG"
                        inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
            	connection.onmessage = (e) => {
            				    let res = JSON.parse(e.data)
                				console.log("PostitionTCORG12 : USER level without fromValueDate & toValueDate - res : " + JSON.stringify(res))
	    			    if (res.positionSubscriptionResponses) {
                            positionSubscriptionResponses = res.positionSubscriptionResponses
                            reqId = positionSubscriptionResponses[0].positionRequestId
                            console.log("PostitionTCORG12: subscription - reqId : " + reqId)
                            console.log("PostitionTCORG12: subscription - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
                            connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
                            done()
				        }
            	 }
            		    });

    		it("PostitionTCORG12: Positions test", function () {
    		    //await delay(9000)
        		assert.equal(posData.clientReferenceId, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).clientReferenceId)
                //assert.equal(user.username, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerId)
                assert.equal(user.orgname, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerOrg)
                assert.equal(posLevel, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).positionLevel)
                assert.equal(inclCustPos, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).inclCustomerPositions)
                assert.equal(posData.pnlCurrency, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).pnlCurrency)
                assert.equal('success', JSON.parse(JSON.stringify(positionSubscriptionResponses[0])).status)
                assert.equal(undefined, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).fromValueDate)
                assert.equal(undefined, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).toValueDate)
            }).timeout(8000);

	    });

        describe("PostitionTCORG12.5: Without fromValueDate and toValueDate tags", function () {
                	before(function (done) {
                	            posLevel = "ORG"
                                inclCustPos = false
           		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
                    			    connection.onmessage = (e) => {
                    				    let res = JSON.parse(e.data)
                        				console.log("PostitionTCORG12.5 : USER level without fromValueDate & toValueDate - res : " + JSON.stringify(res))
		                    	            if (res.position) {
                    		    			positions = res.position
                    		    			reqId = res.position.requestId
                    			    		console.log("PostitionTCORG12.5: ORG level  - reqId : " + reqId)
                    			    		console.log("PostitionTCORG12.5: ORG level - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
                    			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
                    			    		done()
                    				    }
                    			    }
                    		    });

            		it("PostitionTCORG12.5: Positions test", function () {
            		    //await delay(9000)
                		console.log("PostitionTCORG12.5: positions response - positions validation : " + JSON.stringify(positions))
                        assert.exists(positions.symbol)
                        assert.equal(positions.level,posLevel)
                        assert.exists(positions.levelValue, user.orgname)
                        assert.exists(positions.rate)
                    }).timeout(8000);
        	    });

        describe("PostitionTCORG13: Positions test ORG level ", function () {
               			before(function(done) {
                			            posLevel = "ORG"
                                        inclCustPos = false
           		    	            connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
                    			    connection.onmessage = (e) => {
                    				    let res = JSON.parse(e.data)
                        				console.log("PostitionTCORG13 : USER level with fromValueDate & toValueDate - res : " + JSON.stringify(res))
                    	    			if (res.position) {
                    		    			positions = res.position
                    		    			reqId = res.position.requestId
                    			    		console.log("PostitionTCORG13: USER level  - reqId : " + reqId)
                    			    		console.log("PostitionTCORG13: USER level - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
                    			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
                    			    		done()
                    				    }
                    			    }
                    		    });

        // {"position":{"side":"Short","symbol":"USD/JPY","level":"ORG","levelValue":"BUBBLESUCD",
        //"netOpenBase":"35,000,000.00","netOpenterm":"3,859,099,220","rate":"110.25998",
        //"realizedPnL":"(3,099.22)","unrealizedPnL":"71,610.33","pnlCurrency":"USD",
        //"children":[{"side":"Short","symbol":"USD/JPY","valueDate":"05/08/2020","level":"ORG",
            //"levelValue":"BUBBLESUCD","netOpenBase":"29,000,000.00","netOpenterm":"3,196,792,220","rate":"110.23421",
            //"realizedPnL":"0.00","unrealizedPnL":"52,543.86","pnlCurrency":"USD"},{"side":"Short","symbol":"USD/JPY",
            //"valueDate":"05/07/2020","level":"ORG","levelValue":"BUBBLESUCD","netOpenBase":"6,000,000.00","netOpenterm":"662,307,000",
            //"rate":"110.3845","realizedPnL":"(3,099.22)","unrealizedPnL":"19,066.47","pnlCurrency":"USD"}],"requestId":"180603134"}}
            		it("PostitionTCORG13: Positions test", function () {
                		console.log("PostitionTCORG13: positions response - positions validation =====: " + JSON.stringify(positions))
        		    	assert.exists(positions.side)
                        assert.exists(positions.symbol)
                        assert.exists(positions.level)
                        assert.exists(positions.levelValue)
                        assert.exists(positions.netOpenBase)
        			    assert.exists(positions.netOpenterm)
        			    assert.exists(positions.rate)
                        assert.exists(positions.unrealizedPnL)
                        assert.exists(positions.realizedPnL)
                        assert.exists(positions.pnlCurrency)
        //PostitionTCORG13====children = {"symbol":"USD/INR","valueDate":"2025-04-29","level":"ORG","levelValue":"pfAutomationWSuser1","netOpenBase":"0.00","netOpenterm":"0.00","realizedPnL":"17,406.05","unrealizedPnL":"0.00","pnlCurrency":"USD"}
                     assert.exists(positions.children)
                        let children = positions.children[0]
                        console.log("PostitionTCORG13====position.children = " + JSON.stringify(positions.children  ))
                        let length = positions.children.length
                        console.log("====length = " + length)
                        if (length > 0) {
                        console.log("PostitionTCORG13====children = " + JSON.stringify(children))
                        assert.exists(children.symbol)
                        assert.exists(children.valueDate)
                        console.log("PostitionTCORG13====childer with value dates" + children.valueDate)
                        assert.exists(children.level)
                        assert.exists(children.levelValue)
                        assert.exists(children.netOpenBase)
        			    assert.exists(children.netOpenterm)
        			    //assert.exists(children.rate)
                        assert.exists(children.unrealizedPnL)
                        assert.exists(children.realizedPnL)
                        assert.exists(children.pnlCurrency)
                        }
                    }).timeout(8000);
        	    });

       describe("PostitionTCORG5 Unsubscription test ", function () {
                                   // Keep unsubscription test before subscription test, otherwise reqIds will get messed up

                         		    before(function (done) {
                                        posLevel = "ORG"
                                       inclCustPos = false
                              		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
                           			    connection.onmessage = (e) => {
                           				    let res = JSON.parse(e.data)
                               				console.log("PostitionTCORG5 : unsubscription - res : " + JSON.stringify(res))
                           	    			if (res.positionSubscriptionResponses) {
                           		    			positionSubscriptionResponses = res.positionSubscriptionResponses
                           		    			reqId = positionSubscriptionResponses[0].positionRequestId
                           			    		console.log("PostitionTCORG5: unsubscription - reqId : " + reqId)
                           			    		console.log("PostitionTCORG5: positionSubscriptionResponses: " + JSON.stringify(positionSubscriptionResponses))
            			    		                connection.send('{"positionUnsubscriptions":[{"positionRequestId":"' + parseInt(reqId) + '"}]}')
                           				    } else if (res.positionUnsubscriptionResponses) {
                           			    		console.log("PostitionTCORG5: unsubscription - positionUnsubscriptionResponses : " + JSON.stringify(res.positionUnsubscriptionResponses))
                               				    positionUnsubscriptionResponses = res.positionUnsubscriptionResponses
                               				    done()
                           				    }
                           			    }
                           		    });

                               		it("PostitionTCORG5: Unsubscription test", function (done) {
                           			    console.log("PostitionTCORG5: positionSubscriptionResponses[0]: " + JSON.stringify(positionSubscriptionResponses[0]))
                                           assert.equal(positionUnsubscriptionResponses[0].status, "success")
                           		    	assert.equal((positionUnsubscriptionResponses[0].request).positionRequestId, reqId)
                           		    	done()
                           	    	}).timeout(8000);
                     });

}

PostitionTCORG2();



