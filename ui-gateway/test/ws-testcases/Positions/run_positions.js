//require('./ws-positions-negative-test');
//require('./ws-positions-test_ORG');
//require('./ws-positions-test_ORG2');
//require('./ws-positions-test_USER');

const { execSync } = require('child_process');

const commonArgs = '--reporter mochawesome --require mochawesome/register --reporter-options';

const testFiles = [
  'Positions/ws-positions-negative-test.js',
  'Positions/ws-positions-test_ORG.js',
  'Positions/ws-positions-test_ORG2.js',
   'Positions/ws-positions-test_USER.js'
];

const reportDir = 'mochawesome-report';
let failedTests = [];
//npm install --save-dev mochawesome mochawesome-merge mochawesome-report-generator

// Step 1: Run each test individually with a unique report filename
testFiles.forEach((file, index) => {
  const reportFile = `report-${index + 1}`;
  const command = `npx mocha  --timeout 150000 ${file} ${commonArgs} reportFilename=${reportFile},reportDir=${reportDir},overwrite=false --exit`;
  console.log(`\n➡️ Running test: ${file}`);
  try {
  execSync(command, { stdio: 'inherit' });
  } catch (error) {
        console.warn(`❌ Test failed: ${file}`);
        failedTests.push(file);
        // Continue even if one test fails
      }
});

try {
// Step 2: Merge all JSON reports into one
console.log('\n📦 Merging Mochawesome JSON reports...');
execSync(`npx mochawesome-merge "${reportDir}/*.json" > merged-report.json`, { stdio: 'inherit' });

// Step 3: Generate a combined HTML report
console.log('\n🧾 Generating combined Mochawesome HTML report...');
execSync(`npx marge merged-report.json --reportDir final-report --reportFilename run-positions-report`, { stdio: 'inherit' });
} catch (mergeError) {
  console.error('\n❌ Failed to merge reports or generate HTML report.');
}

console.log('\n✅ All done! Combined report available at: final-report/run-positions-report.html');

// Final summary
if (failedTests.length > 0) {
  console.log('\n⚠️ Some tests failed:');
  failedTests.forEach(file => console.log(`- ${file}`));
} else {
  console.log('\n🎉 All tests passed!');
}