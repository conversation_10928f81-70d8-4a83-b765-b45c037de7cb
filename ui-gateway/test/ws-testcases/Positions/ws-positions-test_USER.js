const assert = require('chai').assert
const expect = require('chai').expect
const WebSocket = require('ws')
//const delay = require('delay')

const env = require('../../config/properties').env
const mdData = require('../../config/properties').mdData
const user = require('../../config/properties').user
const posData = require('../../config/properties').posData

let connection
let positionSubscriptionResponses
let positionUnsubscriptionResponses
let reqId
let posLevel
let inclCustPos
let today = new Date();
//let fromValueDate =  (today.getMonth()+1) + '/' + today.getDate()+ '/'+ today.getFullYear();
//let toValueDate = (today.getMonth()+1) + '/' + today.getDate()+ '/'+ (today.getFullYear()+10);
// current timestamp in milliseconds
let ts = Date.now();
let date_ob = new Date(ts);
let date = date_ob.getDate();
let month = date_ob.getMonth() + 1;
let year = date_ob.getFullYear();
let fromValueDate = month + "/" + date + "/" + year;
date = date_ob.getDate() + 2;
let toValueDate = month + "/" + date + "/" + year;

// Login credentials should be for MDF enabled org
// Org should have positions for positive scenarios to display
//run with --exit option

let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

//TCs 0 to 6
let PositionTCUser = function(){
		before(function(done) {
		    wsconnect(done)
		  });

	    after(function() {
		    connection.close()
	    });

        describe("PositionUser0: Unsubscription test for PositionTCUser", function () {
                // Keep unsubscription test before subscription test, otherwise reqIds will get messed up

        		    before(function (done) {
                     posLevel = "USER"
                    inclCustPos = false
           		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
        			    connection.onmessage = (e) => {
        				    let res = JSON.parse(e.data)
            				console.log("PositionUser0 : Subscription response: " + JSON.stringify(res))
        	    			if (res.positionSubscriptionResponses) {
        		    			positionSubscriptionResponses = res.positionSubscriptionResponses
        		    			reqId = positionSubscriptionResponses[0].positionRequestId
        			    		console.log("PositionUser0: positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
        			    		console.log("PositionUser0: reqId - " + reqId)
        			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":"' + parseInt(reqId) + '"}]}')
        				    }
        				    else if (res.positionUnsubscriptionResponses) {
        			    		console.log("PositionUser0: unsubscription - positionUnsubscriptionResponses : " + JSON.stringify(res.positionUnsubscriptionResponses))
            				    positionUnsubscriptionResponses = res.positionUnsubscriptionResponses
            				    done()
        				    }
        			    }
        		    });

            		it("PositionUser0 - unsubscription test", function () {
            		    //await delay(12000)
                        assert.equal(positionUnsubscriptionResponses[0].status, "success")
        		    	assert.equal((positionUnsubscriptionResponses[0].request).positionRequestId, reqId)
        	    	});
        	    });

        describe("PositionUser1: Subscription test for - USER level with basic tags ", function () {
           // USER level with basic tags, requires customerId and customerOrg parameters along with other mandatory tags

        		    before(function (done) {
                    posLevel = "USER"
                    inclCustPos = false
                        //if(this.connection === undefined) {return}
          		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + "" + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+""+'"}]}')
        			    connection.onmessage = (e) => {
        				    let res = JSON.parse(e.data)
            				console.log("PositionTCUser1 : USER level with basic tags - res : " + JSON.stringify(res))
        	    			if (res.positionSubscriptionResponses) {
        		    			positionSubscriptionResponses = res.positionSubscriptionResponses
        		    			reqId = positionSubscriptionResponses[0].positionRequestId
        			    		console.log("PositionTCUser1: USER level with basic tags - reqId : " + reqId)
        			    		console.log("PositionTCUser1: USER level with basic tags - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
        			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
        				    	done()
        				    }
            		        if(this.positionSubscriptionResponses === undefined) {return}
        			    }
        		    });

            		it("PositionTCUser1: Subscription test for -USER level with basic tags", function () {
            		    if(this.positionSubscriptionResponses === undefined) {return}
                        assert.equal(positionSubscriptionResponses[0].status, "success")
                        assert.equal(positionSubscriptionResponses[0].positionRequestId, reqId)
        		    	assert.equal((positionSubscriptionResponses[0].request).clientReferenceId, 'html')
                        assert.equal((positionSubscriptionResponses[0].request).customerOrg, user.orgname)
                        assert.equal((positionSubscriptionResponses[0].request).customerId, user.username)
                        assert.equal((positionSubscriptionResponses[0].request).fromValueDate, "")
                        assert.equal((positionSubscriptionResponses[0].request).pnlCurrency, posData.pnlCurrency)
                        assert.equal((positionSubscriptionResponses[0].request).positionLevel, posLevel)
                        assert.equal((positionSubscriptionResponses[0].request).inclCustomerPositions, inclCustPos)
        	    	});

        	    });

// validate the non mandatory tags by leaving them blank
// clientReferenceId, PositionLevel are the only mandatory tags
// customerOrg required for org level and customerId required for user level positions
// not working without giving PnLCurrency

	   describe("PositionTCUser2: Subscription test - USER level with valueDate ", function () {

            //fromValueDate = "05/04/2020"
		    before(function (done) {
		                posLevel = "USER"
                        inclCustPos = false
  		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+""+'"}]}')
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("PositionTCUser2 : USER level with valueDate - res : " + JSON.stringify(res))
	    			if(res.positionSubscriptionResponses) {
		    			positionSubscriptionResponses = res.positionSubscriptionResponses
		    			reqId = positionSubscriptionResponses[0].positionRequestId
			    		console.log("PositionTCUser2: USER level with valueDate - reqId : " + reqId)
			    		console.log("PositionTCUser2: USER level with valueDate - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
				    	done()
				    }
			    }
		    });

    		it("PositionTCUser2: Subscription test for - USER level with valueDate", function () {
                assert.equal(positionSubscriptionResponses[0].status, "success")
                assert.equal(positionSubscriptionResponses[0].positionRequestId, reqId)
		    	assert.equal((positionSubscriptionResponses[0].request).clientReferenceId, 'html')
                assert.equal((positionSubscriptionResponses[0].request).customerOrg, user.orgname)
                assert.equal((positionSubscriptionResponses[0].request).customerId, user.username)
                assert.equal((positionSubscriptionResponses[0].request).fromValueDate, fromValueDate)
                assert.equal((positionSubscriptionResponses[0].request).pnlCurrency, posData.pnlCurrency)
                assert.equal((positionSubscriptionResponses[0].request).positionLevel, posLevel)
                assert.equal((positionSubscriptionResponses[0].request).inclCustomerPositions, inclCustPos)
	    	});

	    });

	   describe("PositionTCUser3: Subscription test - USER level with fromValueDate & toValueDate ", function () {
            posLevel = "USER"
            inclCustPos = false

		    before(function (done) {
  		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+""+'"}]}')
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("PositionTCUser3 : USER level with fromValueDate & toValueDate - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses) {
		    			positionSubscriptionResponses = res.positionSubscriptionResponses
		    			reqId = positionSubscriptionResponses[0].positionRequestId
			    		console.log("PositionTCUser3: USER level with fromValueDate & toValueDate - reqId : " + reqId)
			    		console.log("PositionTCUser3: USER level with fromValueDate & toValueDate - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
			    		done()
				    }
			    }
		    });

            //{"positionSubscriptionResponses":[{"positionRequestId":"-1455739743","request":{"clientReferenceId":"html","customerId":"Roopa","customerOrg":"BUBBLESUCD","fromValueDate":"05/04/2020","toValueDate":"05/04/2025","pnlCurrency":"USD","positionLevel":"ORG","inclCustomerPositions":false},"status":"success"}]}
    		it("PositionTCUser3: Subscription test for - USER level with fromValueDate & toValueDate", function () {
                assert.equal(positionSubscriptionResponses[0].status, "success")
                assert.equal(positionSubscriptionResponses[0].positionRequestId, reqId)
		    	assert.equal((positionSubscriptionResponses[0].request).clientReferenceId, 'html')
                assert.equal((positionSubscriptionResponses[0].request).customerOrg, user.orgname)
                assert.equal((positionSubscriptionResponses[0].request).customerId, user.username)
                assert.equal((positionSubscriptionResponses[0].request).fromValueDate, fromValueDate)
                assert.equal((positionSubscriptionResponses[0].request).toValueDate, toValueDate)
                assert.equal((positionSubscriptionResponses[0].request).pnlCurrency, posData.pnlCurrency)
                assert.equal((positionSubscriptionResponses[0].request).positionLevel, posLevel)
                assert.equal((positionSubscriptionResponses[0].request).inclCustomerPositions, inclCustPos)
	    	});
	    });

	   describe("PositionTCUser4: Subscription test - USER level with all tags ", function () {

		    before(function (done) {
		                posLevel = "USER"
                        inclCustPos = false
  		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+ inclCustPos +'"}]}')
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("PositionTCUser4 : USER level with fromValueDate & toValueDate - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses) {
		    			positionSubscriptionResponses = res.positionSubscriptionResponses
		    			reqId = positionSubscriptionResponses[0].positionRequestId
			    		console.log("PositionTCUser4: USER level with fromValueDate & toValueDate - reqId : " + reqId)
			    		console.log("PositionTCUser4: USER level with fromValueDate & toValueDate - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
			    		done()
				    }
			    }
		    });

    		it("PositionTCUser4: Subscription test for - USER level with all tags", function () {
                assert.equal(positionSubscriptionResponses[0].status, "success")
                assert.equal(positionSubscriptionResponses[0].positionRequestId, reqId)
		    	assert.equal((positionSubscriptionResponses[0].request).clientReferenceId, 'html')
                assert.equal((positionSubscriptionResponses[0].request).customerOrg, user.orgname)
                assert.equal((positionSubscriptionResponses[0].request).customerId, user.username)
                assert.equal((positionSubscriptionResponses[0].request).fromValueDate, fromValueDate)
                assert.equal((positionSubscriptionResponses[0].request).toValueDate, toValueDate)
                assert.equal((positionSubscriptionResponses[0].request).pnlCurrency, posData.pnlCurrency)
                assert.equal((positionSubscriptionResponses[0].request).positionLevel, posLevel)
                assert.equal((positionSubscriptionResponses[0].request).inclCustomerPositions, inclCustPos)
	    	});

	    });

       describe("PositionTCUser5: Positions test USER level ", function () {

                    before(function (done) {
        			                    posLevel = "USER"
                                        inclCustPos = false
 	              		  connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
            			    connection.onmessage = (e) => {
            				    let res = JSON.parse(e.data)
                				console.log("PositionTCUser5 : USER level with fromValueDate & toValueDate - res : " + JSON.stringify(res))
            	    			if (res.position) {
            		    			positions = res.position
            		    			reqId = res.position.requestId
            			    		console.log("PositionTCUser5: USER level  - reqId : " + reqId)
            			    		console.log("PositionTCUser5: USER level - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
            			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
            			    		done()
            				    }
            			    }
            		    });

        // {"position":{"side":"Short","symbol":"USD/JPY","level":"ORG","levelValue":"BUBBLESUCD",
        //"netOpenBase":"35,000,000.00","netOpenterm":"3,859,099,220","rate":"110.25998",
        //"realizedPnL":"(3,099.22)","unrealizedPnL":"71,610.33","pnlCurrency":"USD",
        //"children":[{"side":"Short","symbol":"USD/JPY","valueDate":"05/08/2020","level":"ORG",
            //"levelValue":"BUBBLESUCD","netOpenBase":"29,000,000.00","netOpenterm":"3,196,792,220","rate":"110.23421",
            //"realizedPnL":"0.00","unrealizedPnL":"52,543.86","pnlCurrency":"USD"},{"side":"Short","symbol":"USD/JPY",
            //"valueDate":"05/07/2020","level":"ORG","levelValue":"BUBBLESUCD","netOpenBase":"6,000,000.00","netOpenterm":"662,307,000",
            //"rate":"110.3845","realizedPnL":"(3,099.22)","unrealizedPnL":"19,066.47","pnlCurrency":"USD"}],"requestId":"180603134"}}

        	 it("PositionTCUser5: Positions test for - Positions test USER level",function() {
                                		console.log("PositionTCUser5: positions response - positions validation : " + JSON.stringify(positions))
                                        assert.exists(positions.symbol)
                        		    	assert.exists(positions.side)
                                        assert.exists(positions.netOpenBase)
                        			    assert.exists(positions.netOpenterm)
                        			    assert.exists(positions.rate)
                                        assert.exists(positions.level)
                                        assert.exists(positions.levelValue)
                                        assert.exists(positions.unrealizedPnL)
                                        assert.exists(positions.realizedPnL)
                                        assert.exists(positions.pnlCurrency)
                                        if (assert.exists(positions.side)) {
                                        try {
                                           if (assert.exists(positions.side)) {
                         		    	        if (positions.side == "Short" || positions.side == "Long") {
                        		    	            assert.isNotNull(positions.netOpenBase)
                        		    	            assert.isNotNull(positions.netOpenterm)
                        		    	        }
                        		    	    } else {
                        		    	        console.log("PositionTCUser5: ===========No available positions")
                        		    	    }

                        		    	    assert.isUndefined((positions.side))
                        		    	    } catch(e){ console.log("PositionTCUser5: =========" + e) }

                                        try {
                                    			    assert.notExists((positions.request).customerId)
                                        } catch (e) {console.log("PositionTCUser5: Subscription test - ORG level with basic tags - exception : " + e.message)}

                        		    	}
                        	    	});

            });

       describe("PositionTCUser6: Position test - USER level with customerId as * ", function () {

               		    before(function (done) {
                           posLevel = "USER"
                           inclCustPos = false
                               //if(this.connection === undefined) {return}
                 		    connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "*", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + "" + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+""+'"}]}')
               			    connection.onmessage = (e) => {
               				    let res = JSON.parse(e.data)
                   				console.log("PositionTCUser6 : USER level with basic tags - res : " + JSON.stringify(res))
               	    			if (res.positionSubscriptionResponses) {
               		    			positionSubscriptionResponses = res.positionSubscriptionResponses
               		    			reqId = positionSubscriptionResponses[0].positionRequestId
               			    		console.log("PositionTCUser6: USER level with basic tags - reqId : " + reqId)
               			    		console.log("PositionTCUser6: USER level with basic tags - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
               			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
               				    	done()
               				    }
                   		        if(this.positionSubscriptionResponses === undefined) {return}
               			    }
               		    });

                   		it("PositionTCUser6: Subscription test for -USER level with basic tags", function () {
                   		    if(this.positionSubscriptionResponses === undefined) {return}
                               assert.equal(positionSubscriptionResponses[0].status, "success")
                               assert.equal(positionSubscriptionResponses[0].positionRequestId, reqId)
               		    	assert.equal((positionSubscriptionResponses[0].request).clientReferenceId, 'html')
                               assert.equal((positionSubscriptionResponses[0].request).customerOrg, user.orgname)
                               assert.equal((positionSubscriptionResponses[0].request).customerId, user.username)
                               assert.equal((positionSubscriptionResponses[0].request).fromValueDate, "")
                               assert.equal((positionSubscriptionResponses[0].request).pnlCurrency, posData.pnlCurrency)
                               assert.equal((positionSubscriptionResponses[0].request).positionLevel, posLevel)
                               assert.equal((positionSubscriptionResponses[0].request).inclCustomerPositions, inclCustPos)
               	    	});

               	    });

    }

PositionTCUser();  //TCs 0 to 5    -- user0 fails


