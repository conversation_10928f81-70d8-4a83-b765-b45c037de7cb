const assert = require('chai').assert
const expect = require('chai').expect
const WebSocket = require('ws')
//const delay = require('delay')

const env = require('../../config/properties').env
const mdData = require('../../config/properties').mdData
const user = require('../../config/properties').user
const posData = require('../../config/properties').posData

let connection
let positionSubscriptionResponses
let positionUnsubscriptionResponses
let reqId
let posLevel
let inclCustPos
let today = new Date();
//let fromValueDate =  (today.getMonth()+1) + '/' + today.getDate()+ '/'+ today.getFullYear();
//let toValueDate = (today.getMonth()+1) + '/' + today.getDate()+ '/'+ (today.getFullYear()+10);
// current timestamp in milliseconds
let ts = Date.now();
let date_ob = new Date(ts);
let date = date_ob.getDate();
let month = date_ob.getMonth() + 1;
let year = date_ob.getFullYear();
let fromValueDate = month + "/" + date + "/" + year;
date = date_ob.getDate() + 2;
let toValueDate = month + "/" + date + "/" + year;

// Login credentials should be for MDF enabled org
// Org should have positions for positive scenarios to display


let wsconnect = function (done) {

        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let NegativeTCSet0 = function(){
// some of the scenarios in NegativeTC are not actually tcs that will fail, they pass and validation is done if pos request is successful or not.
// Actual positions are not validated since it is not possible to give right expected result
    describe("NegativeTCSet0: Positions negative scenario - ", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("Positions Login")
    	});

	    after(function () {
		    connection.close()
	    });

     describe("NegativeTC16: Invalid user", function () {
    //Invalid user like ABC , fixed original bug and position response has error code
    //New response - {"errors":[{"errorCode":2,"errorMessage":"Internal server error."}]}

		    before(function (done) {
		         posLevel = "USER"
                 inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + "ABC" + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("NegativeTC16 : subscription - res : " + JSON.stringify(res))
	    			if (res.errors) {
		    			positions = res.errors
			    		flag = false
			    		done()
				    }
			    }
		    });

    		it("NegativeTC16: Positions test",async function () {
    		  //await delay(12000)
    		    console.log("NegativeTC16 : positions - res : " + JSON.stringify(positions))
		    	assert.equal(positions[0].errorCode, '2')
                assert.equal(positions[0].errorMessage, "Internal server error.")
                //Old error message== errorMessage = "CustomerId ABC not found in  organizarion " + user.orgname
                //assert.equal(errorResponse.errorMessage, errorMessage)
            });

	    });

//	ref request   	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + "" + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+""+'"}]}')
       describe("NegativeTC14: Different user with USER posLevel", function () {
//  user should be a valid user different from logged in user
// should not get any position response since level os USER, but subscription response should be successful
// ** RIGHT NOW it is giving postions in response which is a bug, even when user is not CD
// It is agreed by Eng that it will give positions for the user specified in the request even it is different from logged in user
		    before(function (done) {
            posLevel = "USER"
            inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + "user2" + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
   				let flag = true
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("NegativeTC14: subscription - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses && flag) {
		    			positions = res.positionSubscriptionResponses
		    			positionSubscriptionResponses = (res.positionSubscriptionResponses)[0].request
    				    console.log("NegativeTC14 : positionSubscriptionResponses - res : " + JSON.stringify(positionSubscriptionResponses))
			    		flag = false
				    	done()
				    }
			    }
		    });

    		it("NegativeTC14: Positions test", function () {
		    	assert.equal(positionSubscriptionResponses.clientReferenceId, 'html')
                assert.equal(positionSubscriptionResponses.customerOrg, user.orgname)
                assert.equal(positionSubscriptionResponses.customerId, "user2")
                assert.equal(positionSubscriptionResponses.fromValueDate, fromValueDate)
                assert.equal(positionSubscriptionResponses.toValueDate, toValueDate)
                assert.equal(positionSubscriptionResponses.pnlCurrency, posData.pnlCurrency)
                assert.equal(positionSubscriptionResponses.positionLevel, posLevel)
                assert.equal(positionSubscriptionResponses.inclCustomerPositions, inclCustPos)
            });
	    });

       describe("NegativeTC15: Different user with ORG posLevel", function () {
//  user should be a valid user different from logged in user
// It is agreed by Eng that it will give positions for the org  specified in the request even it is different from logged in user since org level positions will be same irrespective of user
		    before(function (done) {
            posLevel = "ORG"
            inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + "user2" + '", "customerOrg":"' + user.orgname + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
   				let flag = true
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("NegativeTC15 : subscription - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses && flag) {
		    			positions = res.positionSubscriptionResponses
		    			positionSubscriptionResponses = (res.positionSubscriptionResponses)[0].request
    				    console.log("NegativeTC15 : positionSubscriptionResponses - res : " + JSON.stringify(positionSubscriptionResponses))
			    		flag = false
				    	done()
				    }
			    }
		    });

    		it("NegativeTC15: Positions test", function () {
		    	assert.equal(positionSubscriptionResponses.clientReferenceId, 'html')
                assert.equal(positionSubscriptionResponses.customerOrg, user.orgname)
                assert.equal(positionSubscriptionResponses.customerId, "user2")
                assert.equal(positionSubscriptionResponses.fromValueDate, fromValueDate)
                assert.equal(positionSubscriptionResponses.toValueDate, toValueDate)
                assert.equal(positionSubscriptionResponses.pnlCurrency, posData.pnlCurrency)
                assert.equal(positionSubscriptionResponses.positionLevel, posLevel)
                assert.equal(positionSubscriptionResponses.inclCustomerPositions, inclCustPos)
            });
	    });

        });
}

let NegativeTCSet1 = function(){
// some of the scenarios in NegativeTC are not actually tcs that will fail, they pass and validation is done if pos request is successful or not.
// Actual positions are not validated since it is not possible to give right expected result
    describe("NegativeTC: Positions negative scenario - ", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("Positions Login")
    	});

	    after(function () {
		    connection.close()
	    });

        describe("NegativeTC17: Different org other than logged in Org", function () {
//  Org (customerOrg1) which is a valid org in setup but differnt from logged in org
// XCN1139 org is used for login and XCN1046 is used  in the request
// If SD logged in org doesnt have permission to query, it should not fetch the positions,
// Right now, it gives error, this needs to be rechecked for SD flow

		    before(function (done) {
            posLevel = "ORG"
            inclCustPos = false
            customerOrg1 = "XCN1046"
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerOrg":"' + customerOrg1 + '", "customerId": "' + user.username + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
   				let flag = true
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("NegativeTC17 : subscription - res 1 : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses && flag) {
		    			positionSubscriptionResponses = (res.positionSubscriptionResponses)[0].request
		    			errorResponse = (res.positionSubscriptionResponses)[0].errors[0]
		    		console.log("NegativeTC17====positionSubscriptionResponses===" + JSON.stringify(positionSubscriptionResponses))
		    		console.log("NegativeTC17====errorResponse===" + JSON.stringify(errorResponse))
		    			reqId = positionSubscriptionResponses.requestId
			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
			    		flag = false
			    		done()
				    }
			    }
		    });

    		it("NegativeTC17: Positions test",async function () {
    		    ////await delay(9000)
		    	assert.equal(positionSubscriptionResponses.clientReferenceId, 'html')
                assert.equal(positionSubscriptionResponses.customerOrg, customerOrg1)
                assert.equal(positionSubscriptionResponses.fromValueDate, fromValueDate)
                assert.equal(positionSubscriptionResponses.toValueDate, toValueDate)
                assert.equal(positionSubscriptionResponses.pnlCurrency, posData.pnlCurrency)
                assert.equal(positionSubscriptionResponses.positionLevel, posLevel)
                assert.equal(positionSubscriptionResponses.inclCustomerPositions, inclCustPos)
                assert.equal(errorResponse.errorCode, "408")
                errorMessage = "User cannot subscribe for other Organization."
                assert.equal(errorResponse.errorMessage, errorMessage)
            });
	    });

        describe("NegativeTC18: Invalid org", function () {
//  Invalid org which doesnt exist in the setup
// gives org not found in subscription response. Initial but was fixed

		    before(function (done) {
            posLevel = "ORG"
            inclCustPos = false
		        customerOrg1 = "ABCOrg"
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerOrg":"' + customerOrg1 + '", "customerId": "' + user.username + '","fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
   				let flag = true

			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
    				console.log("NegativeTC18 : subscription - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses && flag) {
		    			positionSubscriptionResponses = (res.positionSubscriptionResponses)[0]
		    			errorResponse = (res.positionSubscriptionResponses)[0].errors[0]
			    		console.log("NegativeTC18: positions response - positionSubscriptionResponses : " + JSON.stringify(positionSubscriptionResponses))
			    		flag = false
			    		done()
				    }
			    }
		    });

    		it("NegativeTC18: Positions test",async function () {
    		    ////await delay(9000)
		    	assert.equal(positionSubscriptionResponses.request.clientReferenceId, 'html')
                assert.equal(positionSubscriptionResponses.request.customerOrg, customerOrg1)
                assert.equal(positionSubscriptionResponses.request.fromValueDate, fromValueDate)
                assert.equal(positionSubscriptionResponses.request.toValueDate, toValueDate)
                assert.equal(positionSubscriptionResponses.request.pnlCurrency, posData.pnlCurrency)
                assert.equal(positionSubscriptionResponses.request.positionLevel, posLevel)
                assert.equal(positionSubscriptionResponses.request.inclCustomerPositions, inclCustPos)
                assert.equal(errorResponse.errorCode, "407")
                errorMessage = "CustomerOrg " + customerOrg1 + " not found"
                assert.equal(errorResponse.errorMessage, errorMessage)
            });
	    });

         describe("NegativeTC19: Blank clientRefId", function () {
    //  Blank clientRefId - position subscription should fail, initial bug is fixed

		    before(function (done) {
            posLevel = "ORG"
            inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId":"' + '' + '", "customerOrg":"' + user.orgname + '", "customerId": "' + user.username + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
   				let flag = true
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
				    console.log("NegativeTC19 : subscription - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses && flag) {
		    			positionSubscriptionResponses = (res.positionSubscriptionResponses)[0].request
		    			errorResponse = (res.positionSubscriptionResponses)[0].errors[0]
		    			reqId = positionSubscriptionResponses.requestId
			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
			    		flag = false
				    	done()
				    }
			    }
		    });

    		it("NegativeTC19: Positions test",async function () {
    		    ////await delay(9000)
        		console.log("NegativeTC19: positions response - positions validation : " + JSON.stringify(positionSubscriptionResponses))
		    	assert.equal(positionSubscriptionResponses.clientReferenceId, '')
                assert.equal(positionSubscriptionResponses.customerOrg, user.orgname)
                assert.equal(positionSubscriptionResponses.fromValueDate, fromValueDate)
                assert.equal(positionSubscriptionResponses.toValueDate, toValueDate)
                assert.equal(positionSubscriptionResponses.pnlCurrency, posData.pnlCurrency)
                assert.equal(positionSubscriptionResponses.positionLevel, posLevel)
                assert.equal(positionSubscriptionResponses.inclCustomerPositions, inclCustPos)
                assert.equal(errorResponse.errorCode, "407")
                assert.equal(errorResponse.errorMessage, "ClientReferenceId is not valid.")
            });
	    });

       describe("NegativeTC20: Without clientRefId tag", function () {
//  without clientRefId  tag - exceptions are seen in uig logs, and no pos subscription response
// It should send subscription failure response
		    before(function (done) {
            posLevel = "ORG"
            inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"customerOrg":"' + user.orgname + '", "customerId": "' + user.username + '", "fromValueDate": "' + fromValueDate + '", "toValueDate": "' + toValueDate + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
   				let flag = true
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
	    			if (res.positionSubscriptionResponses && flag) {
		    			positionSubscriptionResponses = (res.positionSubscriptionResponses)[0].request
		    			errorResponse = (res.positionSubscriptionResponses)[0].errors[0]
		    			reqId = positionSubscriptionResponses.requestId
			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
			    		flag = false
			    		done()
				    }
			    }
		    });

    		it("NegativeTC20: Positions test",async function () {
    		    ////await delay(9000)
        		console.log("NegativeTC20: positions response - positions validation : " + JSON.stringify(positionSubscriptionResponses))
		    	//tag does'nt come --- assert.equal(positionSubscriptionResponses.clientReferenceId, '')
                assert.equal(positionSubscriptionResponses.customerOrg, user.orgname)
                assert.equal(positionSubscriptionResponses.fromValueDate, fromValueDate)
                assert.equal(positionSubscriptionResponses.toValueDate, toValueDate)
                assert.equal(positionSubscriptionResponses.pnlCurrency, posData.pnlCurrency)
                assert.equal(positionSubscriptionResponses.positionLevel, posLevel)
                assert.equal(positionSubscriptionResponses.inclCustomerPositions, inclCustPos)
                assert.equal(errorResponse.errorCode, "407")
                assert.equal(errorResponse.errorMessage, "ClientReferenceId is not valid.")
            });
	    });

        });
}

let NegativeTCSet2 = function(){
// some of the scenarios in NegativeTC are not actually tcs that will fail, they pass and validation is done if pos request is successful or not.
// Actual positions are not validated since it is not possible to give right expected result
    describe("NegativeTCSet2: Positions negative scenario - ", function(){
    	before(function (done) {
    		wsconnect(done);
    		console.log("Positions Login")
    	});

	    after(function () {
		    connection.close()
	    });

      describe("NegativeTC21: Without customerOrg tag for ORG level", function () {
        // Without customerOrg tags - UIG considers customerOrg as null and position subscription and response is Successful.
        // Shouldnt it give error?? or is it considering sessions org like in RestAPI
        // Update - it gives error now, tc modified,.

		    before(function (done) {
                posLevel = "ORG"
                inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username  + '", "fromValueDate": "' + "" + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+""+'"}]}')
   				let flag = true
			    connection.onmessage = (e) => {
			    let res = JSON.parse(e.data)
    				console.log("NegativeTC21: subscription - res : " + JSON.stringify(res))
		    			if (res.positionSubscriptionResponses) {
    		    			positionSubscriptionResponses = res.positionSubscriptionResponses
    		    			positions=positionSubscriptionResponses;
    		    			done()
    				    }
			    }
		    });

    		it("NegativeTC21: Positions test", async function () {
    		    ////await delay(9000)
        		console.log("NegativeTC21: positions validation : " + JSON.stringify(positionSubscriptionResponses))
        		assert.equal(posData.clientReferenceId, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).clientReferenceId)
                assert.equal(user.username, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerId)
                assert.equal(undefined, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerOrg)
                assert.equal(posLevel, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).positionLevel)
                assert.equal(inclCustPos, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).inclCustomerPositions)
                assert.equal('success', JSON.parse(JSON.stringify(positionSubscriptionResponses[0])).status)
            });
	    });

      describe("NegativeTC22: Without customerOrg tag for USER level", function () {
        // Without customerOrg tags - UIG considers customerOrg as null and position subscription and response is Successful.
        // Shouldnt it give error?? or is it considering sessions org like in RestAPI
        // Update - it gives error now, tc modified,.

		    before(function (done) {
 // 	    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
 //  	    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.user  + '", "customerOrg":"' + user.orgname + '", "pnlCurrency": "' + "" + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
                posLevel = "USER"
                inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username  + '", "fromValueDate": "' + "" + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+""+'"}]}')
   				let flag = true
			    connection.onmessage = (e) => {
			    let res = JSON.parse(e.data)
    				console.log("NegativeTC22: subscription - res : " + JSON.stringify(res))
		    			if (res.positionSubscriptionResponses) {
    		    			positionSubscriptionResponses = res.positionSubscriptionResponses
    		    			positions=positionSubscriptionResponses;
    		    			done()
    				    }
			    }
		    });

    		it("NegativeTC22: Positions test",async function () {
    		    ////await delay(9000)
                assert.equal(undefined, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerOrg)
        		assert.equal(posData.clientReferenceId, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).clientReferenceId)
                assert.equal(user.username, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerId)
                assert.equal(posLevel, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).positionLevel)
                assert.equal(posData.pnlCurrency, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).pnlCurrency)
                assert.equal(inclCustPos, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).inclCustomerPositions)
                assert.equal('success', JSON.parse(JSON.stringify(positionSubscriptionResponses[0])).status)
               });
	    });

	  describe("NegativeTC23: Blank PnLCurrency tag for ORG level", function () {
        //  subscription fails

		    before(function (done) {
		                posLevel = "ORG"
                        inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username  + '", "customerOrg":"' + user.orgname + '", "pnlCurrency": "' + "" + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
        		    console.log("NegativeTC23: Blank PnLCurrency tag for ORG level - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses) {
		    			positionSubscriptionResponses = res.positionSubscriptionResponses
//			    		connection.send('{"positionUnsubscriptions":[{"positionRequestId":' + parseInt(reqId) + '}]}')
//			    		flag = false
				    	done()
				    }
			    }
		    });

    		it("NegativeTC23: Positions test", async function () {
    		    //await delay(10000)
        		console.log("NegativeTC23: Blank PnLCurrency tag for ORG level - positions validation : " + JSON.stringify(positionSubscriptionResponses))
        		assert.equal(posData.clientReferenceId, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).clientReferenceId)
                assert.equal(user.username, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerId)
                assert.equal(user.orgname, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerOrg)
                assert.equal('', JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).pnlCurrency)
                assert.equal(posLevel, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).positionLevel)
                assert.equal(inclCustPos, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).inclCustomerPositions)
                assert.equal('406', JSON.parse(JSON.stringify((positionSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('PNL Currency is not valid.', JSON.parse(JSON.stringify((positionSubscriptionResponses[0].errors)[0])).errorMessage)
            });
	    });


      describe("NegativeTC24: Blank PnLCurrency tag for USER level", function () {

		    before(function (done) {
            posLevel = "USER"
            inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username  + '", "customerOrg":"' + user.orgname + '", "pnlCurrency": "' + "" + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
        		    console.log("NegativeTC24: Blank PnLCurrency tag for ORG level - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses) {
		    			positionSubscriptionResponses = res.positionSubscriptionResponses
				    	done()
				    }
			    }
		    });

    		it("NegativeTC24: Positions test", function () {
        		console.log("NegativeTC24: Blank PnLCurrency tag for ORG level - positions validation : " + JSON.stringify(positionSubscriptionResponses))
        		assert.equal(posData.clientReferenceId, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).clientReferenceId)
                assert.equal(user.username, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerId)
                assert.equal(user.orgname, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerOrg)
                assert.equal('', JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).pnlCurrency)
                assert.equal(posLevel, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).positionLevel)
                assert.equal(inclCustPos, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).inclCustomerPositions)
                assert.equal('406', JSON.parse(JSON.stringify((positionSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('PNL Currency is not valid.', JSON.parse(JSON.stringify((positionSubscriptionResponses[0].errors)[0])).errorMessage)
            });
	    });


      describe("NegativeTC25: No PnLCurrency tag for USER level", function () {

		    before(function (done) {
            posLevel = "USER"
            inclCustPos = false
   		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username  + '", "customerOrg":"' + user.orgname + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+inclCustPos+'"}]}')
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
        		    console.log("NegativeTC25: No PnLCurrency tag for ORG level - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses) {
		    			positionSubscriptionResponses = res.positionSubscriptionResponses
				    	done()
				    }
			    }
		    });

    		it("NegativeTC25: Positions test", function () {
        		console.log("Position: Blank PnLCurrency tag for ORG level - positions validation : " + JSON.stringify(positionSubscriptionResponses))
        		assert.equal(posData.clientReferenceId, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).clientReferenceId)
                assert.equal(user.username, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerId)
                assert.equal(user.orgname, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerOrg)
                assert.equal(posLevel, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).positionLevel)
                assert.equal(inclCustPos, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).inclCustomerPositions)
                assert.equal('success', JSON.parse(JSON.stringify(positionSubscriptionResponses[0])).status)
            });
	    });


      describe("NegativeTC26: inclCustomerPositions as true for USER level", function () {

		    before(function (done) {
            posLevel = "USER"
            inclCustPos = true
	    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerId": "' + user.username  + '", "customerOrg":"' + user.orgname + '","fromValueDate": "' + fromValueDate + '","positionLevel":"' + posLevel +'","pnlCurrency": "' + posData.pnlCurrency + '","inclCustomerPositions":"'+inclCustPos+'"}]}')
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
        		    console.log("NegativeTC26: Position subscription- res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses) {
		    			positionSubscriptionResponses = res.positionSubscriptionResponses
				    	done()
				    }
			    }
		    });

    		it("NegativeTC26: Positions test", function () {
        		console.log("NegativeTC26: positions validation : " + JSON.stringify(positionSubscriptionResponses))
        		assert.equal(posData.clientReferenceId, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).clientReferenceId)
                assert.equal(user.username, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerId)
                assert.equal(user.orgname, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerOrg)
                assert.equal(posLevel, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).positionLevel)
                assert.equal(inclCustPos, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).inclCustomerPositions)
                assert.equal('404', JSON.parse(JSON.stringify((positionSubscriptionResponses[0].errors)[0])).errorCode)
                assert.equal('The customer position can be only included at ORG/LE level.', JSON.parse(JSON.stringify((positionSubscriptionResponses[0].errors)[0])).errorMessage)
                });
	    });


      describe("NegativeTC27: Without customerId tag for ORG level", function () {
              // Without customerOrg tags - UIG considers customerOrg as null and position subscription and response is Successful.
              // Shouldnt it give error?? or is it considering sessions org like in RestAPI
              // Update - it gives error now, tc modified,.

      		    before(function (done) {
                      posLevel = "ORG"
                      inclCustPos = false
         		    	connection.send('{"positionSubscriptions" : [{"clientReferenceId": "' + posData.clientReferenceId + '", "customerOrg": "' + user.orgname  + '", "fromValueDate": "' + "" + '","pnlCurrency": "' + posData.pnlCurrency + '", "positionLevel":"' + posLevel +'" , "inclCustomerPositions":"'+""+'"}]}')
         				let flag = true
			    connection.onmessage = (e) => {
				    let res = JSON.parse(e.data)
        		    console.log("NegativeTC27: Subscription - res : " + JSON.stringify(res))
	    			if (res.positionSubscriptionResponses) {
		    			positionSubscriptionResponses = res.positionSubscriptionResponses
				    	done()
				    }
			    }
      		    });

          		it("NegativeTC27: Positions test",async function () {
          		    //await delay(12000)
        		console.log("NegativeTC27: positions validation : " + JSON.stringify(positionSubscriptionResponses))
        		assert.equal(posData.clientReferenceId, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).clientReferenceId)
                assert.equal(user.orgname, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerOrg)
                assert.equal(posLevel, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).positionLevel)
                assert.equal(inclCustPos, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).inclCustomerPositions)
                assert.equal('failed', JSON.parse(JSON.stringify(positionSubscriptionResponses[0])).status)
                assert.equal(undefined, JSON.parse(JSON.stringify(positionSubscriptionResponses[0].request)).customerId)
                 });
      	    });

    });
}

NegativeTCSet0();
NegativeTCSet1();
NegativeTCSet2();
//-- TC21 and TC22,  without customer org tag is a success




