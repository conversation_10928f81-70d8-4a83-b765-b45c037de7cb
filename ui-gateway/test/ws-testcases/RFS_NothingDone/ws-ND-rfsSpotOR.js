    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')
    const env = require('../../config/cqaproperties').env
    const rfsData = require('../../config/cqaproperties').rfsData
    const date = require('date-and-time');
    const now = new Date();
    const formattedDate = date.format(now, 'YYYY-MM-DD');
    const formattedDate2 = date.format(now, 'YYYY/MM/DD');

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let nothingDoneRequestResponsesList
    let rfsNDResponses

    let reqId = Math.floor(Math.random() * 100)

    let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

    let getBusinessDayDates = function() {
        let today = new Date();

        // Get today's date in the format YYYY-MM-DD
        let fromValueDate = new Date(today);
        let fromValueDate2 = new Date(today);

        // Function to check if a date is Saturday or Sunday
        function isWeekend(date) {
            return date.getDay() === 6 || date.getDay() === 0;  // 6 = Saturday, 0 = Sunday
        }

           // Adjust 'fromValueDate' if it falls on a weekend
            while (isWeekend(fromValueDate)) {
                fromValueDate.setDate(fromValueDate.getDate() + 1); // Move to next day
            }

            while (isWeekend(fromValueDate2)) {
                fromValueDate2.setDate(fromValueDate2.getDate() + 1); // Move to next day
            }

         fromValueDate = fromValueDate.getFullYear() + '-'
                            + (fromValueDate.getMonth() + 2).toString().padStart(2, '0') + '-'
                            + fromValueDate.getDate().toString().padStart(2, '0');
         fromValueDate2 = fromValueDate2.getFullYear() + '/'
                            + (fromValueDate2.getMonth() + 2).toString().padStart(2, '0') + '/'
                            + fromValueDate2.getDate().toString().padStart(2, '0');

        // Return both the 'from' and 'to' dates
        return {
            fromDate: fromValueDate,
            fromDate2: fromValueDate2
        };
    }


    let rfsSpotTC = function(){

     describe("RFS Spot Scenarios for Nothing Done ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });


          describe("SC1 RFS Spot 2-way Subscription - Two-way Nothing Done test", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                    console.log("RFS Spot Trade - Two-way quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: ["VISA","WFNAD","NTFX"],
                                 clOrderId: "SPOT_TwoWay_Trade_" + reqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                    console.log(wsreq)
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                offerQuoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + bidQuoteId)
                                console.log("RFS quote id" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    clOrderId: "NothingDone"+reqId,
                                    "requestId": systemReqId,
                                    "bidQuoteId": bidQuoteId,
                                    "offerQuoteId":offerQuoteId,
                                    "bidSpotSpread": 0.05,
                                    "offerSpotSpread": 0.04
                                }]
                                var wstradereq = { nothingDoneRequestList : tradereq }
                                console.log("RFS ND request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                            i++;
                        } else if (res.rfsTradeAck) {
                            rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsResponses) {
                            rfsNDResponses = res.rfsResponses[0]
                            done()
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                            done()
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        } else if (res.nothingDoneRequestResponsesList) {
                           nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                           //done()
                        }
                    }
                });

                it("SC1 RFS Nothing Responses test", function () {

                    let rfsNDResponse = rfsNDResponses
                    console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                    rfsMessage = rfsNDResponse.rfsMessage
                    console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                    assert.equal(rfsNDResponse.requestId,systemReqId)
                    assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                    assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"RFS Nothing Done Request for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT');

             });

                it("SC1 RFS Nothing List Responses test", function () {

                                    let rfsNDListResponse = nothingDoneRequestResponsesList
                                    console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                    assert.equal(rfsNDListResponse.requestId,systemReqId)
                                    assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                    assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                    assert.equal(bidQuoteId,rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                    assert.equal(0.05,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                    assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                    assert.equal(0.04,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")

                             });



            });

          describe("SC2 RFS Spot 2-way Subscription - Nothing Done - OfferQuoteId is sent, bidQuoteId is null", function ()  {
                            let dealtAmt = '1,000,000.00'

                            before(function (done) {
                                console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                                var subrequests = [{
                                             symbol : rfsData.symbol,
                                             amount : "1000000.0",
                                             dealtCurrency : rfsData.baseCcy,
                                             expiry: rfsData.expiry,
                                             nearValueDate : "SPOT",
                                             fixingDate : "" ,
                                             side : rfsData.sideType2Way,
                                             priceType : rfsData.priceTypeSpot,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: ["VISA","WFNAD","NTFX"],
                                             clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                console.log(wsreq)
                                connection.send(JSON.stringify(wsreq));
                                tradeDone = "false"
                                i=1
                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("res : " + JSON.stringify(res))

                                   if (res.rfsRates && i<5) {
                                        rate = res.rfsRates[0]
                                        if(rate.status === "A" && tradeDone === "false") {
                                            rfsActiveQuote = rate
                                            bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                            offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                            console.log("RFS quote id = " + bidQuoteId)
                                            console.log("RFS quote id" )
                                            console.log(rfsActiveQuote)
                                            systemReqId = rate.requestId
                                            var tradereq = [{
                                                clOrderId: "NothingDone"+reqId,
                                                "requestId": systemReqId,
                                                "bidQuoteId": null,
                                                "offerQuoteId":offerQuoteId,
                                                "bidSpotSpread": 0.05,
                                                "offerSpotSpread": 0.04
                                            }]
                                            var wstradereq = { nothingDoneRequestList : tradereq }
                                            console.log("RFS ND request sent : " )
                                            console.log(wstradereq)
                                            connection.send(JSON.stringify(wstradereq));
                                            tradeDone = "true"
                                        } else if (rate.status === "I") {
                                            rfsInactiveQuote = res.rfsRates[0]
                                         }
                                        i++;
                                    } else if (res.rfsTradeAck) {
                                        rfsTradeAck = res.rfsTradeAck[0]
                                    } else if (res.rfsResponses) {
                                        rfsNDResponses = res.rfsResponses[0]
                                        done()
                                    } else if (res.nothingDoneRequestResponsesList) {
                                       nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                    }
                                }
                            });

                it("SC2 OfferQuoteId is sent, bidQuoteId is null - RFS Nothing Responses test", function () {

                    let rfsNDResponse = rfsNDResponses
                    console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                    rfsMessage = rfsNDResponse.rfsMessage
                    console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                    assert.equal(rfsNDResponse.requestId,systemReqId)
                    assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                    assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"RFS Nothing Done Request for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date SPOT');

             });

                it("SC2 OfferQuoteId is sent, bidQuoteId is null - RFS Nothing List Responses test", function () {

                                    let rfsNDListResponse = nothingDoneRequestResponsesList
                                    console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                    assert.equal(rfsNDListResponse.requestId,systemReqId)
                                    assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                    assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                    assert.equal(rfsNDListResponse.bidQuoteId, null, "bidQuoteId is not correct")
                                    assert.equal(0.05,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                    assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                    assert.equal(0.04,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")

                             });
          });

          describe("SC3 RFS Spot Buy Subscription - one-way Offer Nothing Done ", function ()  {
                            let dealtAmt = '1,000,000.00'

                            before(function (done) {
                                console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                console.log("RFS Spot Buy Trade - One-way quote test -> reqId = " + reqId)
                                var subrequests = [{
                                             symbol : rfsData.symbol,
                                             amount : "1000000.0",
                                             dealtCurrency : rfsData.baseCcy,
                                             expiry: rfsData.expiry,
                                             nearValueDate : "SPOT",
                                             fixingDate : "" ,
                                             side : rfsData.sideTypeBuy,
                                             priceType : rfsData.priceTypeSpot,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: ["VISA","WFNAD","NTFX"],
                                             clOrderId: "SPOT_OneWay_Rate_OfferTrade_" + reqId
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("RFS Spot Buy Trade - One-way quote test ->  rfsSpotSubscriptions: request : ")
                                console.log(wsreq)
                                connection.send(JSON.stringify(wsreq));
                                tradeDone = "false"
                                i=1
                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("res : " + JSON.stringify(res))

                                    if (res.rfsRates && i<5) {
                                        rate = res.rfsRates[0]
                                        if(rate.status === "A" && tradeDone === "false") {
                                            rfsActiveQuote = rate
                                            offerQuoteId =rfsActiveQuote.offers[0].quoteId
                                            console.log("RFS quote id" )
                                            console.log(rfsActiveQuote)
                                            systemReqId = rate.requestId
                                            var tradereq = [{
                                                clOrderId: "NothingDone"+reqId,
                                                "requestId": systemReqId,
                                                "offerQuoteId":offerQuoteId,
                                                "offerSpotSpread": 2
                                            }]
                                            var wstradereq = { nothingDoneRequestList : tradereq }
                                            console.log("RFS ND request sent : " )
                                            console.log(wstradereq)
                                            connection.send(JSON.stringify(wstradereq));
                                            tradeDone = "true"
                                        } else if (rate.status === "I") {
                                            rfsInactiveQuote = res.rfsRates[0]
                                         }
                                        i++;
                                    } else if (res.rfsTradeAck) {
                                        rfsTradeAck = res.rfsTradeAck[0]
                                    } else if (res.rfsResponses) {
                                        rfsNDResponses = res.rfsResponses[0]
                                        done()
                                    } else if (res.rfsWithdrawAck) {
                                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                                        done()
                                    } else if (res.rfsResponses) {
                                        rfsWithdrawResponse = res.rfsResponses[0]
                                        done()
                                    } else if (res.nothingDoneRequestResponsesList) {
                                       nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                       //done()
                                    }
                                }
                            });

                            it("SC3 RFS Nothing Responses test", function () {

                                let rfsNDResponse = rfsNDResponses
                                console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                                rfsMessage = rfsNDResponse.rfsMessage
                                console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                                assert.equal(rfsNDResponse.requestId,systemReqId)
                                assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                                assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                               assert.exists(JSON.stringify(rfsMessage.eventTime))
                               assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                               assert.exists(JSON.stringify(rfsMessage.eventDetails))
                               expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"RFS Nothing Done Request for EUR/USD with Dealt currency EUR. Buy 1,000,000.00. Value Date SPOT');

                         });

                            it("SC3 RFS Nothing List Responses test", function () {

                                                let rfsNDListResponse = nothingDoneRequestResponsesList
                                                console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                                assert.equal(rfsNDListResponse.requestId,systemReqId)
                                                assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                                assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                                assert.equal(undefined,rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                                assert.equal(undefined,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                                assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                                assert.equal(2,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")

                                         });


                        });

          describe("SC4 RFS Spot Sell Subscription - one-way Bid Nothing Done ", function ()  {
                            let dealtAmt = '1,000,000.00'

                            before(function (done) {
                                console.log('*************************** RFS Spot Sell Trade - quote Subscription  ************************** ' + new Date());
                                console.log("RFS Spot Sell Trade - One-way quote test -> reqId = " + reqId)
                                var subrequests = [{
                                             symbol : rfsData.symbol,
                                             amount : "1000000.0",
                                             dealtCurrency : rfsData.baseCcy,
                                             expiry: rfsData.expiry,
                                             nearValueDate : "SPOT",
                                             fixingDate : "" ,
                                             side : rfsData.sideTypeSell,
                                             priceType : rfsData.priceTypeSpot,
                                             customerAccount : rfsData.customerAccount,
                                             customerOrg: rfsData.customerOrg,
                                             priceViewType: rfsData.aggregatedView,
                                             depth: 5,
                                             channel : rfsData.channel,
                                             providers: ["VISA","WFNAD","NTFX"],
                                             clOrderId: "SPOT_OneWay_Rate_SellTrade_" + reqId
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("RFS Spot Sell Trade - quote test ->  rfsSpotSubscriptions: request : ")
                                console.log(wsreq)
                                connection.send(JSON.stringify(wsreq));
                                tradeDone = "false"
                                i=1
                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("res : " + JSON.stringify(res))

                                    if (res.rfsRates && i<5) {
                                        rate = res.rfsRates[0]
                                        if(rate.status === "A" && tradeDone === "false") {
                                            rfsActiveQuote = rate
                                            bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                            console.log("RFS quote id" )
                                            console.log(rfsActiveQuote)
                                            systemReqId = rate.requestId
                                            var tradereq = [{
                                                clOrderId: "NothingDone"+reqId,
                                                "requestId": systemReqId,
                                                "bidQuoteId":bidQuoteId,
                                                "bidSpotSpread": 0.4,
                                            }]
                                            var wstradereq = { nothingDoneRequestList : tradereq }
                                            console.log("RFS ND request sent : " )
                                            console.log(wstradereq)
                                            connection.send(JSON.stringify(wstradereq));
                                            tradeDone = "true"
                                        } else if (rate.status === "I") {
                                            rfsInactiveQuote = res.rfsRates[0]
                                         }
                                        i++;
                                    } else if (res.rfsTradeAck) {
                                        rfsTradeAck = res.rfsTradeAck[0]
                                    } else if (res.rfsResponses) {
                                        rfsNDResponses = res.rfsResponses[0]
                                        done()
                                    } else if (res.rfsWithdrawAck) {
                                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                                        done()
                                    } else if (res.rfsResponses) {
                                        rfsWithdrawResponse = res.rfsResponses[0]
                                        done()
                                    } else if (res.nothingDoneRequestResponsesList) {
                                       nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                       //done()
                                    }
                                }
                            });

                            it("SC4 RFS Nothing Responses test", function () {

                                let rfsNDResponse = rfsNDResponses
                                console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                                rfsMessage = rfsNDResponse.rfsMessage
                                console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                                assert.equal(rfsNDResponse.requestId,systemReqId)
                                assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                                assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                               assert.exists(JSON.stringify(rfsMessage.eventTime))
                               assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                               assert.exists(JSON.stringify(rfsMessage.eventDetails))
                               expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"RFS Nothing Done Request for EUR/USD with Dealt currency EUR. Sell 1,000,000.00. Value Date SPOT');

                         });

                            it("SC4 RFS Nothing List Responses test", function () {

                                                let rfsNDListResponse = nothingDoneRequestResponsesList
                                                console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                                assert.equal(rfsNDListResponse.requestId,systemReqId)
                                                assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                                assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                                assert.equal(bidQuoteId,rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                                assert.equal(0.4,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                                assert.equal(undefined,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                                assert.equal(undefined,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")

                                         });


                        });

     });  //RFS Spot ws connect

    } //let rfsSpot

     let rfsOutrightTC = function(){

         describe("RFS Outright Scenarios for Nothing Done ", function () {

                before(function (done) {
                    wsconnect(done);
                });

                after(function () {
                    connection.close()
                });

              describe("SC1 RFS Outright 2-way Subscription - Two-way Nothing Done test", function ()  {
                                     let dealtAmt = '1,000,000.00'

                                   before(function (done) {
                                                    console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                                    console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                                                    var subrequests = [{
                                                                 symbol : rfsData.symbol,
                                                                 amount : "1000000.0",
                                                                 dealtCurrency : rfsData.baseCcy,
                                                                 expiry: rfsData.expiry,
                                                                 nearValueDate : "TOD",
                                                                 fixingDate : "" ,
                                                                 side : rfsData.sideType2Way,
                                                                 priceType : rfsData.priceTypeOR,
                                                                 customerAccount : rfsData.customerAccount,
                                                                 customerOrg: rfsData.customerOrg,
                                                                 priceViewType: rfsData.aggregatedView,
                                                                 depth: 5,
                                                                 channel : rfsData.channel,
                                                                 providers: ["VISA","WFNAD","NTFX"],
                                                                 clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                                                     }]
                                                    var wsreq = { rfsSubscriptions : subrequests }
                                                    console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                                    console.log(wsreq)
                                                    connection.send(JSON.stringify(wsreq));
                                                    tradeDone = "false"
                                                    i=1
                                                    connection.onmessage = (e) => {
                                                        res = JSON.parse(e.data)
                                                        console.log("res : " + JSON.stringify(res))

                                                       if (res.rfsRates && i<5) {
                                                            rate = res.rfsRates[0]
                                                            if(rate.status === "A" && tradeDone === "false") {
                                                                rfsActiveQuote = rate
                                                                bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                                                offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                                                console.log("RFS quote id = " + bidQuoteId)
                                                                console.log("RFS quote id" )
                                                                console.log(rfsActiveQuote)
                                                                systemReqId = rate.requestId
                                                                var tradereq = [{
                                                                    clOrderId: "NothingDone"+reqId,
                                                                    "requestId": systemReqId,
                                                                    "bidQuoteId": bidQuoteId,
                                                                    "offerQuoteId":offerQuoteId,
                                                                    "bidSpotSpread": -0.2,
                                                                    "offerSpotSpread": -0.5,
                                                                    "offerForwardSpread": 0.2
                                                                }]
                                                                var wstradereq = { nothingDoneRequestList : tradereq }
                                                                console.log("RFS ND request sent : " )
                                                                console.log(wstradereq)
                                                                connection.send(JSON.stringify(wstradereq));
                                                                tradeDone = "true"
                                                            } else if (rate.status === "I") {
                                                                rfsInactiveQuote = res.rfsRates[0]
                                                             }
                                                            i++;
                                                            } else if (res.rfsTradeAck) {
                                                                                                    rfsTradeAck = res.rfsTradeAck[0]
                                                                                                } else if (res.rfsResponses) {
                                                                                                    rfsNDResponses = res.rfsResponses[0]
                                                                                                    done()
                                                                                                } else if (res.rfsWithdrawAck) {
                                                                                                    rfsWithdrawAck = res.rfsWithdrawAck[0]
                                                                                                    done()
                                                                                                } else if (res.rfsResponses) {
                                                                                                    rfsWithdrawResponse = res.rfsResponses[0]
                                                                                                    done()
                                                                                                } else if (res.nothingDoneRequestResponsesList) {
                                                                                                   nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                                                                                   //done()
                                                                                                }

                               }
                                            });

                                    it("SC1 OfferQuoteId is sent, bidQuoteId is null - RFS Nothing Responses test", function () {

                                        let rfsNDResponse = rfsNDResponses
                                        console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                                        rfsMessage = rfsNDResponse.rfsMessage
                                        console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                                        assert.equal(rfsNDResponse.requestId,systemReqId)
                                        assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                                        assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                       assert.exists(JSON.stringify(rfsMessage.eventTime))
                                       assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                                       assert.exists(JSON.stringify(rfsMessage.eventDetails))
                                       expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"RFS Nothing Done Request for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date TOD');

                                 });

                                    it("SC1 OfferQuoteId is sent, bidQuoteId is null - RFS Nothing List Responses test", function () {

                                                        let rfsNDListResponse = nothingDoneRequestResponsesList
                                                        console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                                        assert.equal(rfsNDListResponse.requestId,systemReqId)
                                                        assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                                        assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                                        assert.equal(rfsNDListResponse.bidQuoteId, bidQuoteId, "bidQuoteId is not correct")
                                                        assert.equal(-0.2,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                                        assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                                        assert.equal(-0.5,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")
                                                        assert.equal(0.2,rfsNDListResponse.offerForwardSpread, "offerForwardSpread is not correct")


                                                 });
                              });

              describe("SC2 RFS OR 2-way Subscription - Nothing Done - OfferQuoteId is sent, bidQuoteId is null", function ()  {
                     let dealtAmt = '1,000,000.00'

                   before(function (done) {
                                    console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                    console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                                    var subrequests = [{
                                                 symbol : rfsData.symbol,
                                                 amount : "1000000.0",
                                                 dealtCurrency : rfsData.baseCcy,
                                                 expiry: rfsData.expiry,
                                                 nearValueDate : "TOD",
                                                 fixingDate : "" ,
                                                 side : rfsData.sideType2Way,
                                                 priceType : rfsData.priceTypeOR,
                                                 customerAccount : rfsData.customerAccount,
                                                 customerOrg: rfsData.customerOrg,
                                                 priceViewType: rfsData.aggregatedView,
                                                 depth: 5,
                                                 channel : rfsData.channel,
                                                 providers: ["VISA","WFNAD","NTFX"],
                                                 clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                                     }]
                                    var wsreq = { rfsSubscriptions : subrequests }
                                    console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                    console.log(wsreq)
                                    connection.send(JSON.stringify(wsreq));
                                    tradeDone = "false"
                                    i=1
                                    connection.onmessage = (e) => {
                                        res = JSON.parse(e.data)
                                        console.log("res : " + JSON.stringify(res))

                                       if (res.rfsRates && i<5) {
                                            rate = res.rfsRates[0]
                                            if(rate.status === "A" && tradeDone === "false") {
                                                rfsActiveQuote = rate
                                                bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                                offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                                console.log("RFS quote id = " + bidQuoteId)
                                                console.log("RFS quote id" )
                                                console.log(rfsActiveQuote)
                                                systemReqId = rate.requestId
                                                var tradereq = [{
                                                    clOrderId: "NothingDone"+reqId,
                                                    "requestId": systemReqId,
                                                    "bidQuoteId": null,
                                                    "offerQuoteId":offerQuoteId,
                                                    "bidSpotSpread": -0.2,
                                                    "offerSpotSpread": -0.5,
                                                    "offerForwardSpread": 0.2
                                                }]
                                                var wstradereq = { nothingDoneRequestList : tradereq }
                                                console.log("RFS ND request sent : " )
                                                console.log(wstradereq)
                                                connection.send(JSON.stringify(wstradereq));
                                                tradeDone = "true"
                                            } else if (rate.status === "I") {
                                                rfsInactiveQuote = res.rfsRates[0]
                                             }
                                            i++;
                                            } else if (res.rfsTradeAck) {
                                                                                    rfsTradeAck = res.rfsTradeAck[0]
                                                                                } else if (res.rfsResponses) {
                                                                                    rfsNDResponses = res.rfsResponses[0]
                                                                                    done()
                                                                                } else if (res.rfsWithdrawAck) {
                                                                                    rfsWithdrawAck = res.rfsWithdrawAck[0]
                                                                                    done()
                                                                                } else if (res.rfsResponses) {
                                                                                    rfsWithdrawResponse = res.rfsResponses[0]
                                                                                    done()
                                                                                } else if (res.nothingDoneRequestResponsesList) {
                                                                                   nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                                                                   //done()
                                                                                }

               }
                            });

                    it("SC2 OfferQuoteId is sent, bidQuoteId is null - RFS Nothing Responses test", function () {

                        let rfsNDResponse = rfsNDResponses
                        console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                        rfsMessage = rfsNDResponse.rfsMessage
                        console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                        assert.equal(rfsNDResponse.requestId,systemReqId)
                        assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                        assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                       assert.exists(JSON.stringify(rfsMessage.eventTime))
                       assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                       assert.exists(JSON.stringify(rfsMessage.eventDetails))
                       expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"RFS Nothing Done Request for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00. Value Date TOD');

                 });

                    it("SC2 OfferQuoteId is sent, bidQuoteId is null - RFS Nothing List Responses test", function () {

                                        let rfsNDListResponse = nothingDoneRequestResponsesList
                                        console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                        assert.equal(rfsNDListResponse.requestId,systemReqId)
                                        assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                        assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                        assert.equal(rfsNDListResponse.bidQuoteId, null, "bidQuoteId is not correct")
                                        assert.equal(-0.2,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                        assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                        assert.equal(-0.5,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")
                                        assert.equal(0.2,rfsNDListResponse.offerForwardSpread, "offerForwardSpread is not correct")


                                 });
              });

              describe("SC3 RFS Outright Broken date Buy Subscription - one-way Offer Nothing Done ", function ()  {
                                let dealtAmt = '1,000,000.00'
                    // Example of using the function
                let { fromDate, fromDate2 } = getBusinessDayDates();
                console.log('From Date:', fromDate);
                console.log('From Date2:', fromDate2);

                                before(function (done) {
                                    console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                    console.log("RFS Outright Buy Trade - Two-way quote test -> reqId = " + reqId)
                                    var subrequests = [{
                                                 symbol : rfsData.symbol,
                                                 amount : "1000000.0",
                                                 dealtCurrency : rfsData.baseCcy,
                                                 expiry: rfsData.expiry,
                                                 nearValueDate : fromDate,
                                                 fixingDate : "" ,
                                                 side : rfsData.sideTypeBuy,
                                                 priceType : rfsData.priceTypeOR,
                                                 customerAccount : rfsData.customerAccount,
                                                 customerOrg: rfsData.customerOrg,
                                                 priceViewType: rfsData.aggregatedView,
                                                 depth: 5,
                                                 channel : rfsData.channel,
                                                 providers: ["VISA","WFNAD","NTFX"],
                                                 clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                                     }]
                                    var wsreq = { rfsSubscriptions : subrequests }
                                    console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                    console.log(wsreq)
                                    connection.send(JSON.stringify(wsreq));
                                    tradeDone = "false"
                                    i=1
                                    connection.onmessage = (e) => {
                                        res = JSON.parse(e.data)
                                        console.log("res : " + JSON.stringify(res))

                                        if (res.rfsRates && i<5) {
                                            rate = res.rfsRates[0]
                                            if(rate.status === "A" && tradeDone === "false") {
                                                rfsActiveQuote = rate
                                                offerQuoteId =rfsActiveQuote.offers[0].quoteId
                                                console.log("RFS quote id" )
                                                console.log(rfsActiveQuote)
                                                systemReqId = rate.requestId
                                                var tradereq = [{
                                                    clOrderId: "NothingDone"+reqId,
                                                    "requestId": systemReqId,
                                                    "offerQuoteId":offerQuoteId,
                                                    "offerSpotSpread": 2,
                                                    "offerForwardSpread": 0.4
                                                }]
                                                var wstradereq = { nothingDoneRequestList : tradereq }
                                                console.log("RFS ND request sent : " )
                                                console.log(wstradereq)
                                                connection.send(JSON.stringify(wstradereq));
                                                tradeDone = "true"
                                            } else if (rate.status === "I") {
                                                rfsInactiveQuote = res.rfsRates[0]
                                             }
                                            i++;
                                        } else if (res.rfsTradeAck) {
                                            rfsTradeAck = res.rfsTradeAck[0]
                                        } else if (res.rfsResponses) {
                                            rfsNDResponses = res.rfsResponses[0]
                                            done()
                                        } else if (res.rfsWithdrawAck) {
                                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                                            done()
                                        } else if (res.rfsResponses) {
                                            rfsWithdrawResponse = res.rfsResponses[0]
                                            done()
                                        } else if (res.nothingDoneRequestResponsesList) {
                                           nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                           //done()
                                        }
                                    }
                                });

                                it("SC3 RFS Nothing Responses test", function () {

                                    let rfsNDResponse = rfsNDResponses
                                    console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                                    rfsMessage = rfsNDResponse.rfsMessage
                                    console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                                    assert.equal(rfsNDResponse.requestId,systemReqId)
                                    assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                                    assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                                   assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('RFS Nothing Done Request for EUR/USD with Dealt currency EUR. Buy 1,000,000.00. Value Date '+fromDate2.toString());

                             });

                                it("SC3 RFS Nothing List Responses test", function () {

                                                    let rfsNDListResponse = nothingDoneRequestResponsesList
                                                    console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                                    assert.equal(rfsNDListResponse.requestId,systemReqId)
                                                    assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                                    assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                                    assert.equal(undefined,rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                                    assert.equal(undefined,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                                    assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                                    assert.equal(2,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")
                                                    assert.equal(0.4,rfsNDListResponse.offerForwardSpread, "offerForwardSpread is not correct")


                                             });


                });

              describe("SC4 RFS Outright Sell Subscription - one-way Bid Nothing Done ", function ()  {
                                let dealtAmt = '1,000,000.00'
            // Example of using the function
            let { fromDate, fromDate2 } = getBusinessDayDates();
            console.log('From Date:', fromDate);
            console.log('From Date2:', fromDate2);
                                before(function (done) {
                                    console.log('*************************** RFS Spot Buy Trade - quote Subscription  ************************** ' + new Date());
                                    console.log("RFS Outright Sell Trade - Two-way quote test -> reqId = " + reqId)
                                    var subrequests = [{
                                                 symbol : rfsData.symbol,
                                                 amount : "1000000.0",
                                                 dealtCurrency : rfsData.baseCcy,
                                                 expiry: rfsData.expiry,
                                                 nearValueDate : fromDate,
                                                 fixingDate : "" ,
                                                 side : rfsData.sideTypeSell,
                                                 priceType : rfsData.priceTypeOR,
                                                 customerAccount : rfsData.customerAccount,
                                                 customerOrg: rfsData.customerOrg,
                                                 priceViewType: rfsData.aggregatedView,
                                                 depth: 5,
                                                 channel : rfsData.channel,
                                                 providers: ["VISA","WFNAD","NTFX"],
                                                 clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                                     }]
                                    var wsreq = { rfsSubscriptions : subrequests }
                                    console.log("RFS Outright Sell Trade - quote test ->  rfsSpotSubscriptions: request : ")
                                    console.log(wsreq)
                                    connection.send(JSON.stringify(wsreq));
                                    tradeDone = "false"
                                    i=1
                                    connection.onmessage = (e) => {
                                        res = JSON.parse(e.data)
                                        console.log("res : " + JSON.stringify(res))

                                        if (res.rfsRates && i<5) {
                                            rate = res.rfsRates[0]
                                            if(rate.status === "A" && tradeDone === "false") {
                                                rfsActiveQuote = rate
                                                bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                                console.log("RFS quote id" )
                                                console.log(rfsActiveQuote)
                                                systemReqId = rate.requestId
                                                var tradereq = [{
                                                    clOrderId: "NothingDone"+reqId,
                                                    "requestId": systemReqId,
                                                    "bidQuoteId":bidQuoteId,
                                                    "bidSpotSpread": 0.4,
                                                    "bidForwardSpread": 1.0
                                                }]
                                                var wstradereq = { nothingDoneRequestList : tradereq }
                                                console.log("RFS ND request sent : " )
                                                console.log(wstradereq)
                                                connection.send(JSON.stringify(wstradereq));
                                                tradeDone = "true"
                                            } else if (rate.status === "I") {
                                                rfsInactiveQuote = res.rfsRates[0]
                                             }
                                            i++;
                                        } else if (res.rfsTradeAck) {
                                            rfsTradeAck = res.rfsTradeAck[0]
                                        } else if (res.rfsResponses) {
                                            rfsNDResponses = res.rfsResponses[0]
                                            done()
                                        } else if (res.rfsWithdrawAck) {
                                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                                            done()
                                        } else if (res.rfsResponses) {
                                            rfsWithdrawResponse = res.rfsResponses[0]
                                            done()
                                        } else if (res.nothingDoneRequestResponsesList) {
                                           nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                           //done()
                                        }
                                    }
                                });

                                it("SC4 RFS Nothing Responses test", function () {

                                    let rfsNDResponse = rfsNDResponses
                                    console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                                    rfsMessage = rfsNDResponse.rfsMessage
                                    console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                                    assert.equal(rfsNDResponse.requestId,systemReqId)
                                    assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                                    assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                                   assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('RFS Nothing Done Request for EUR/USD with Dealt currency EUR. Sell 1,000,000.00. Value Date '+fromDate2.toString());

                             });

                                it("SC4 RFS Nothing List Responses test", function () {

                                                    let rfsNDListResponse = nothingDoneRequestResponsesList
                                                    console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                                    assert.equal(rfsNDListResponse.requestId,systemReqId)
                                                    assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                                    assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                                    assert.equal(bidQuoteId,rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                                    assert.equal(0.4,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")
                                                    assert.equal(1.0,rfsNDListResponse.bidForwardSpread, "bidForwardSpread is not correct")

                                                    assert.equal(undefined,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                                    assert.equal(undefined,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")

                                             });


                            });

         });  //RFS Outright ws connect

        } //let rfsOutright

rfsSpotTC();  // all pass in CQA
rfsOutrightTC();  // all pass in CQA