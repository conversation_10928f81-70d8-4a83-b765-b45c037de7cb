    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData
    const date = require('date-and-time');
    const now = new Date();
    const formattedDate = date.format(now, 'YYYY-MM-DD');

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let nothingDoneRequestResponsesList
    let rfsNDResponses

    let reqId = Math.floor(Math.random() * 100)

    let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

         let negScenarios2 = function (done, cookies)
         {
                 before(function (done) {
                                 wsconnect(done);
                  });

                  after(function () {
                              connection.close()
                 });

            describe("NSC1 RFS Spot Nothing Done - null request ID", function ()  {
                                let dealtAmt = '1,000,000.00'

                                before(function (done) {
                                    console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                    console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                                    var subrequests = [{
                                                 symbol : rfsData.symbol,
                                                 amount : "1000000.0",
                                                 dealtCurrency : rfsData.baseCcy,
                                                 expiry: rfsData.expiry,
                                                 nearValueDate : "SPOT",
                                                 fixingDate : "" ,
                                                 side : rfsData.sideType2Way,
                                                 priceType : rfsData.priceTypeSpot,
                                                 customerAccount : rfsData.customerAccount,
                                                 customerOrg: rfsData.customerOrg,
                                                 priceViewType: rfsData.aggregatedView,
                                                 depth: 5,
                                                 channel : rfsData.channel,
                                                 providers: ["VISA","WFNAD","NTFX"],
                                                 clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                                     }]
                                    var wsreq = { rfsSubscriptions : subrequests }
                                    console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                    console.log(wsreq)
                                    connection.send(JSON.stringify(wsreq));
                                    tradeDone = "false"
                                    i=1
                                    connection.onmessage = (e) => {
                                        res = JSON.parse(e.data)
                                        console.log("res : " + JSON.stringify(res))

                                        if (res.rfsRates && i<5) {
                                            rate = res.rfsRates[0]
                                            if(rate.status === "A" && tradeDone === "false") {
                                                rfsActiveQuote = rate
                                                bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                                offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                                console.log("RFS quote id = " + bidQuoteId)
                                                console.log("RFS quote id" )
                                                console.log(rfsActiveQuote)
                                                systemReqId = rate.requestId
                                                var tradereq = [{
                                                    clOrderId: "NothingDone"+reqId,
                                                    "requestId": null,
                                                    "bidQuoteId": bidQuoteId,
                                                    "offerQuoteId":offerQuoteId,
                                                    "bidSpotSpread": 0.05,
                                                    "offerSpotSpread": 0.04,
                                                    "bidForwardSpread": 0.0,
                                                    "offerForwardSpread": 0.0,
                                                    "bidFarForwardSpread":0,
                                                    "offerFarForwardSpread":0
                                                }]
                                                var wstradereq = { nothingDoneRequestList : tradereq }
                                                console.log("RFS ND request sent : " )
                                                console.log(wstradereq)
                                                connection.send(JSON.stringify(wstradereq));
                                                tradeDone = "true"
                                            } else if (rate.status === "I") {
                                                rfsInactiveQuote = res.rfsRates[0]
                                             }
                                            i++;
                                        } else if (res.rfsTradeAck) {
                                            rfsTradeAck = res.rfsTradeAck[0]
                                        } else if (res.rfsResponses) {
                                            rfsNDResponses = res.rfsResponses[0]
                                            done()
                                        } else if (res.nothingDoneRequestResponsesList) {
                                           nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                        }

                                    }
                                });

                                it("NSC1 RFS ND - null request ID test ", function () {

                                    let rfsNDResponse = rfsNDResponses
                                    console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                                    rfsMessage = rfsNDResponse.rfsMessage
                                    console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                                    assert.equal("NOTHING_DONE_REQUEST_FAILED",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                                    assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                                   assert.equal('"requestId:may not be null"', JSON.stringify(rfsNDResponse.errorCode), "errorCode didnt match")
                                    //assert.equal(rfsNDResponse.requestId,systemReqId)

                             });

                                after(function (done) {
                                    // {"rfsWithdrawRequests":[{"requestId":"G4796976d518465689568123f"}]}
                                    var withdrawReq = [{
                                                 "requestId" : systemReqId,
                                     }]
                                    var withdrawReqs = { rfsWithdrawRequests : withdrawReq }
                                    connection.send(JSON.stringify(withdrawReqs));
                                    done()
                                });

                            });

           describe("NSC3 RFS Spot Nothing Done - No subscription request Found", function ()  {
                        let dealtAmt = '1,000,000.00'

                        before(function (done) {
                            console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());

                            tradeDone = "false"
                            i=1
                            connection.onmessage = (e) => {
                                res = JSON.parse(e.data)
                                console.log("res : " + JSON.stringify(res))

                                if (res.rfsRates && i<5) {
                                    rate = res.rfsRates[0]
                                    if(rate.status === "A" && tradeDone === "false") {
                                        rfsActiveQuote = rate
                                        bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                        offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                        console.log("RFS quote id = " + bidQuoteId)
                                        console.log("RFS quote id" )
                                        console.log(rfsActiveQuote)
                                        systemReqId = rate.requestId
                                        var tradereq = [{
                                            clOrderId: "NothingDone"+reqId,
                                            "requestId": systemReqId,
                                            "bidQuoteId": bidQuoteId,
                                            "offerQuoteId":offerQuoteId,
                                            "bidSpotSpread": 0.05,
                                            "offerSpotSpread": 0.04,
                                            "bidForwardSpread": 0.0,
                                            "offerForwardSpread": 0.0,
                                            "bidFarForwardSpread":0,
                                            "offerFarForwardSpread":0
                                        }]
                                        var wstradereq = { nothingDoneRequestList : tradereq }
                                        console.log("RFS ND request sent : " )
                                        console.log(wstradereq)
                                        connection.send(JSON.stringify(wstradereq));
                                        tradeDone = "true"
                                    } else if (rate.status === "I") {
                                        rfsInactiveQuote = res.rfsRates[0]
                                     }
                                    i++;
                                } else if (res.rfsTradeAck) {
                                    rfsTradeAck = res.rfsTradeAck[0]
                                } else if (res.rfsResponses) {
                                    rfsNDResponses = res.rfsResponses[0]
                                    done()
                                } else if (res.nothingDoneRequestResponsesList) {
                                   nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                   //done()
                                }
                            }
                        });

                        it("NSC3 RFS ND - blank request ID test ", function () {
                            let rfsNDResponse = rfsNDResponses
                            console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                            rfsMessage = rfsNDResponse.rfsMessage
                            console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                            assert.equal("NOTHING_DONE_REQUEST_FAILED",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                            assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                            assert.equal('"NO_SUBSCRIPTION_REQUEST_FOUND"', JSON.stringify(rfsNDResponse.errorCode), "errorCode didnt match")
                     });

                                after(function (done) {
                                    // {"rfsWithdrawRequests":[{"requestId":"G4796976d518465689568123f"}]}
                                    var withdrawReq = [{
                                                 "requestId" : systemReqId,
                                     }]
                                    var withdrawReqs = { rfsWithdrawRequests : withdrawReq }
                                    connection.send(JSON.stringify(withdrawReqs));
                                    done()
                                });


                    });

           describe("NSC6 RFS Spot Nothing Done - invalid spreads", function ()  {
                                      let dealtAmt = '1,000,000.00'

                                      before(function (done) {
                                          console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                          console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                                          var subrequests = [{
                                                       symbol : rfsData.symbol,
                                                       amount : "1000000.0",
                                                       dealtCurrency : rfsData.baseCcy,
                                                       expiry: rfsData.expiry,
                                                       nearValueDate : "SPOT",
                                                       fixingDate : "" ,
                                                       side : rfsData.sideType2Way,
                                                       priceType : rfsData.priceTypeSpot,
                                                       customerAccount : rfsData.customerAccount,
                                                       customerOrg: rfsData.customerOrg,
                                                       priceViewType: rfsData.aggregatedView,
                                                       depth: 5,
                                                       channel : rfsData.channel,
                                                       providers: ["VISA","WFNAD","NTFX"],
                                                       clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                                           }]
                                          var wsreq = { rfsSubscriptions : subrequests }
                                          console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                          console.log(wsreq)
                                          connection.send(JSON.stringify(wsreq));
                                          tradeDone = "false"
                                          i=1
                                          connection.onmessage = (e) => {
                                              res = JSON.parse(e.data)
                                              console.log("res : " + JSON.stringify(res))

                                              if (res.rfsRates && i<5) {
                                                  rate = res.rfsRates[0]
                                                  if(rate.status === "A" && tradeDone === "false") {
                                                      rfsActiveQuote = rate
                                                      bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                                      offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                                      console.log("RFS quote id = " + bidQuoteId)
                                                      console.log("RFS quote id" )
                                                      console.log(rfsActiveQuote)
                                                      systemReqId = rate.requestId
                                                      var tradereq = [{
                                                          clOrderId: "NothingDone"+reqId,
                                                          "requestId": systemReqId,
                                                          "offerQuoteId":offerQuoteId,
                                                          "offerSpotSpread": "Invalid",
                                                          "offerForwardSpread": 0.0,
                                                          "offerFarForwardSpread":0
                                                      }]
                                                      var wstradereq = { nothingDoneRequestList : tradereq }
                                                      console.log("RFS ND request sent : " )
                                                      console.log(wstradereq)
                                                      connection.send(JSON.stringify(wstradereq));
                                                      tradeDone = "true"
                                                  } else if (rate.status === "I") {
                                                      rfsInactiveQuote = res.rfsRates[0]
                                                   }
                                                  i++;
                                              } else if (res.rfsTradeAck) {
                                                  rfsTradeAck = res.rfsTradeAck[0]
                                              } else if (res.rfsResponses) {
                                                  rfsNDResponses = res.rfsResponses[0]
                                              } else if (res.errors) {
                                                 resErrors = res.errors[0]
                                                  done()
                                              }
                                          }
                                      });

                                       it("NSC6  Invalid message Errors", function () {

                                                                      let resError = resErrors

                                                                      assert.equal("Not a valid request.",resErrors.errorMessage, "error message is not correct")
                                                                      assert.exists(resErrors.errorCode, "1", "error message is not correct")

                                                               });



//                                  after(function (done) {
//                                          // {"rfsWithdrawRequests":[{"requestId":"G4796976d518465689568123f"}]}
//                                          var withdrawReq = [{
//                                                       "requestId" : systemReqId,
//                                           }]
//                                          var withdrawReqs = { rfsWithdrawRequests : withdrawReq }
//                                          connection.send(JSON.stringify(withdrawReqs));
//                                          done()
//                                      })

                                  });

           describe("NSC4 RFS Spot Nothing Done - null quote ID", function ()  {
                                let dealtAmt = '1,000,000.00'

                                before(function (done) {
                                    console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                    console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                                    var subrequests = [{
                                                 symbol : rfsData.symbol,
                                                 amount : "1000000.0",
                                                 dealtCurrency : rfsData.baseCcy,
                                                 expiry: rfsData.expiry,
                                                 nearValueDate : "SPOT",
                                                 fixingDate : "" ,
                                                 side : rfsData.sideType2Way,
                                                 priceType : rfsData.priceTypeSpot,
                                                 customerAccount : rfsData.customerAccount,
                                                 customerOrg: rfsData.customerOrg,
                                                 priceViewType: rfsData.aggregatedView,
                                                 depth: 5,
                                                 channel : rfsData.channel,
                                                 providers: ["VISA","WFNAD","NTFX"],
                                                 clOrderId: "SPOT_TwoWay_Rate_NullQuoteID_" + reqId
                                     }]
                                    var wsreq = { rfsSubscriptions : subrequests }
                                    console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                    console.log(wsreq)
                                    connection.send(JSON.stringify(wsreq));
                                    tradeDone = "false"
                                    i=1
                                    connection.onmessage = (e) => {
                                        res = JSON.parse(e.data)
                                        console.log("res : " + JSON.stringify(res))

                                        if (res.rfsRates && i<5) {
                                            rate = res.rfsRates[0]
                                            if(rate.status === "A" && tradeDone === "false") {
                                                rfsActiveQuote = rate
                                                bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                                offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                                console.log("RFS quote id = " + bidQuoteId)
                                                console.log("RFS quote id" )
                                                console.log(rfsActiveQuote)
                                                systemReqId = rate.requestId
                                                var tradereq = [{
                                                    clOrderId: "NothingDone"+reqId,
                                                    "requestId": systemReqId,
                                                    "bidQuoteId": null,
                                                    "offerQuoteId":null,
                                                    "bidSpotSpread": 0.05,
                                                    "offerSpotSpread": 0.04,
                                                    "bidForwardSpread": 0.0,
                                                    "offerForwardSpread": 0.0,
                                                    "bidFarForwardSpread":0,
                                                    "offerFarForwardSpread":0
                                                }]
                                                var wstradereq = { nothingDoneRequestList : tradereq }
                                                console.log("RFS ND request sent : " )
                                                console.log(wstradereq)
                                                connection.send(JSON.stringify(wstradereq));
                                                tradeDone = "true"
                                            } else if (rate.status === "I") {
                                                rfsInactiveQuote = res.rfsRates[0]
                                             }
                                            i++;
                                        } else if (res.rfsTradeAck) {
                                            rfsTradeAck = res.rfsTradeAck[0]
                                        } else if (res.rfsResponses) {
                                            rfsNDResponses = res.rfsResponses[0]
                                            done()
                                        } else if (res.nothingDoneRequestResponsesList) {
                                           nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                        }

                                    }
                                });

                                it("NSC4 RFS ND - null quote ID test ", function () {

                                    let rfsNDResponse = rfsNDResponses
                                    console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                                    rfsMessage = rfsNDResponse.rfsMessage
                                    console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                                    assert.equal("NOTHING_DONE_REQUEST_FAILED",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                                    assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                   assert.equal('"INCORRECT_REQUEST_PARAMS"', JSON.stringify(rfsNDResponse.errorCode), "errorCode didnt match")

                             });

                                after(function (done) {
                                    // {"rfsWithdrawRequests":[{"requestId":"G4796976d518465689568123f"}]}
                                    var withdrawReq = [{
                                                 "requestId" : systemReqId,
                                     }]
                                    var withdrawReqs = { rfsWithdrawRequests : withdrawReq }
                                    connection.send(JSON.stringify(withdrawReqs));
                                    done()
                                });

                            });

       }  //negScenarios let

 negScenarios2();
