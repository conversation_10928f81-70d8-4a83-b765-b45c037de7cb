    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData
    const date = require('date-and-time');
    const now = new Date();
    const formattedDate = date.format(now, 'YYYY-MM-DD');

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let nothingDoneRequestResponsesList
    let rfsNDResponses

    let reqId = Math.floor(Math.random() * 100)

    let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

    let rfsFwdFwdTC = function(){

     describe("RFS Fwd Fwd Scenarios for Nothing Done ", function () {

                before(function (done) {
                    wsconnect(done);
                });

                after(function () {
                    connection.close()
                });


         describe("SC1 RFS FwdFwd 2-way Subscription - Two-way Nothing Done test", function ()  {
                    let dealtAmt = '1,000,000.00'

                    before(function (done) {
                        console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                        console.log("RFS Fwd Fwd Buy Trade - Two-way quote test -> reqId = " + reqId)
                        var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "1000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: ["VISA","WFNAD","NTFX"],
                                 clOrderId: parseInt(reqId)
                         }]
                        var wsreq = { rfsSubscriptions : subrequests }
                        console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                        console.log(wsreq)
                        connection.send(JSON.stringify(wsreq));
                        tradeDone = "false"
                        i=1
                        connection.onmessage = (e) => {
                            res = JSON.parse(e.data)
                            console.log("res : " + JSON.stringify(res))

                            if (res.rfsRates && i<5) {
                                rate = res.rfsRates[0]
                                if(rate.status === "A" && tradeDone === "false") {
                                    rfsActiveQuote = rate
                                    bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                    offerQuoteId =rfsActiveQuote.offers[0].quoteId
                                    console.log("RFS quote id = " + bidQuoteId)
                                    console.log("RFS quote id" )
                                    console.log(rfsActiveQuote)
                                    systemReqId = rate.requestId
                                    var tradereq = [{
                                        clOrderId: "NothingDone"+reqId,
                                        "requestId": systemReqId,
                                        "bidQuoteId": bidQuoteId,
                                        "offerQuoteId":offerQuoteId,
                                        "bidSpotSpread": 0.05,
                                        "offerSpotSpread": 0.04,
                                        "bidForwardSpread": 0.0,
                                        "offerForwardSpread": 0.0,
                                        "bidFarForwardSpread":0.234,
                                        "offerFarForwardSpread":0.124
                                    }]
                                    var wstradereq = { nothingDoneRequestList : tradereq }
                                    console.log("RFS ND request sent : " )
                                    console.log(wstradereq)
                                    connection.send(JSON.stringify(wstradereq));
                                    tradeDone = "true"
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                 }
                                i++;
                            } else if (res.rfsTradeAck) {
                                rfsTradeAck = res.rfsTradeAck[0]
                            } else if (res.rfsResponses) {
                                rfsNDResponses = res.rfsResponses[0]
                                done()
                            } else if (res.rfsWithdrawAck) {
                                rfsWithdrawAck = res.rfsWithdrawAck[0]
                                //done()
                            } else if (res.rfsResponses) {
                                rfsWithdrawResponse = res.rfsResponses[0]
                                //done()
                            } else if (res.nothingDoneRequestResponsesList) {
                               nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                               //done()
                            }
                        }
                    });

                    it("SC1 RFS Nothing Responses test", function () {

                        let rfsNDResponse = rfsNDResponses
                        console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                        rfsMessage = rfsNDResponse.rfsMessage
                        console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                        assert.equal(rfsNDResponse.requestId,systemReqId)
                        assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                        assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                       assert.exists(JSON.stringify(rfsMessage.eventTime))
                       assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                       assert.exists(JSON.stringify(rfsMessage.eventDetails))
                       expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"RFS Nothing Done Request for EUR/USD with Dealt currency EUR. 2-Way 1,000,000.00/1,000,000.00. Value Date 1W 2W."');

                 });

                    it("SC1 RFS Nothing List Responses test", function () {

                                        let rfsNDListResponse = nothingDoneRequestResponsesList
                                        console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                        assert.equal(rfsNDListResponse.requestId,systemReqId)
                                        assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                        assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                        assert.equal(bidQuoteId,rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                        assert.equal(0,rfsNDListResponse.bidForwardSpread, "bidForwardSpread is not correct")
                                        assert.equal(0.234,rfsNDListResponse.bidFarForwardSpread, "bidFarForwardSpread is not correct")
                                        assert.equal(0.05,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                        assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                        assert.equal(0.04,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")
                                        assert.equal(0,rfsNDListResponse.offerForwardSpread, "offerForwardSpread is not correct")
                                        assert.equal(0.124,rfsNDListResponse.offerFarForwardSpread, "offerFarForwardSpread is not correct")

                                 });



                });

         describe("SC2 RFS FwdFwd 2-way Subscription - One-way Mismatch buy Nothing Done test", function ()  {
                    let dealtAmt = '1,000,000.00'

                    before(function (done) {
                        console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                        console.log("RFS Fwd Fwd Buy Trade - Two-way quote test -> reqId = " + reqId)
                        var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "1W",
								 farDealtAmount : "2000000.0",
								 farValueDate : "2W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeFwdFwd,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: ["VISA","WFNAD","NTFX"],
                                 clOrderId: parseInt(reqId)
                         }]
                        var wsreq = { rfsSubscriptions : subrequests }
                        console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                        console.log(wsreq)
                        connection.send(JSON.stringify(wsreq));
                        tradeDone = "false"
                        i=1
                        connection.onmessage = (e) => {
                            res = JSON.parse(e.data)
                            console.log("res : " + JSON.stringify(res))

                            if (res.rfsRates && i<5) {
                                rate = res.rfsRates[0]
                                if(rate.status === "A" && tradeDone === "false") {
                                    rfsActiveQuote = rate
                                    bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                    offerQuoteId =rfsActiveQuote.offers[0].quoteId
                                    console.log("RFS quote id = " + bidQuoteId)
                                    console.log("RFS quote id" )
                                    console.log(rfsActiveQuote)
                                    systemReqId = rate.requestId
                                    var tradereq = [{
                                        clOrderId: "NothingDone"+reqId,
                                        "requestId": systemReqId,
                                        "offerQuoteId":offerQuoteId,
                                        "offerSpotSpread": 0.04,
                                        "offerForwardSpread": 0.0,
                                        "offerFarForwardSpread":0.124
                                    }]
                                    var wstradereq = { nothingDoneRequestList : tradereq }
                                    console.log("RFS ND request sent : " )
                                    console.log(wstradereq)
                                    connection.send(JSON.stringify(wstradereq));
                                    tradeDone = "true"
                                } else if (rate.status === "I") {
                                    rfsInactiveQuote = res.rfsRates[0]
                                 }
                                i++;
                            } else if (res.rfsTradeAck) {
                                rfsTradeAck = res.rfsTradeAck[0]
                            } else if (res.rfsResponses) {
                                rfsNDResponses = res.rfsResponses[0]
                                done()
                            } else if (res.rfsWithdrawAck) {
                                rfsWithdrawAck = res.rfsWithdrawAck[0]
                                //done()
                            } else if (res.rfsResponses) {
                                rfsWithdrawResponse = res.rfsResponses[0]
                            } else if (res.nothingDoneRequestResponsesList) {
                               nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                            }
                        }
                    });

                    it("SC2 RFS Nothing Responses test", function () {

                        let rfsNDResponse = rfsNDResponses
                        console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                        rfsMessage = rfsNDResponse.rfsMessage
                        console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                        assert.equal(rfsNDResponse.requestId,systemReqId)
                        assert.equal("NOTHING_DONE_REQUEST_SUCCESS",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                        assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                       assert.exists(JSON.stringify(rfsMessage.eventTime))
                       assert.equal('"RFS Nothing Done"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                       assert.exists(JSON.stringify(rfsMessage.eventDetails))
                       expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"RFS Nothing Done Request for EUR/USD with Dealt currency EUR. Sell 1,000,000.00/2,000,000.00. Value Date 1W 2W."');

                 });

                    it("SC2 RFS Nothing List Responses test", function () {

                                        let rfsNDListResponse = nothingDoneRequestResponsesList
                                        console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                        assert.equal(rfsNDListResponse.requestId,systemReqId)
                                        assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                        assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                        assert.equal(undefined,rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                        assert.equal(undefined,rfsNDListResponse.bidFarForwardSpread, "bidFarForwardSpread is not correct")

                                        assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                        assert.equal(0.04,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")
                                        assert.equal(0,rfsNDListResponse.offerForwardSpread, "offerForwardSpread is not correct")
                                        assert.equal(0.124,rfsNDListResponse.offerFarForwardSpread, "offerFarForwardSpread is not correct")

                                 });

                });



        });

     }  //rfsFwdFwdTC let

rfsFwdFwdTC();
