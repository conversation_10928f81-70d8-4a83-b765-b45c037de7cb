    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData
    const date = require('date-and-time');
    const now = new Date();
    const formattedDate = date.format(now, 'YYYY-MM-DD');

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors
    let nothingDoneRequestResponsesList
    let rfsNDResponses

    let reqId = Math.floor(Math.random() * 100)

    let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

    let negScenarios = function (done, cookies)
    {
            before(function (done) {
                            wsconnect(done);
             });

             after(function () {
                         connection.close()
            });

          describe("NSC5 RFS Spot Nothing Done - invalid quote ID", function ()  {
                                        let dealtAmt = '1,000,000.00'

                                        before(function (done) {
                                            console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                            console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                                            var subrequests = [{
                                                         symbol : rfsData.symbol,
                                                         amount : "1000000.0",
                                                         dealtCurrency : rfsData.baseCcy,
                                                         expiry: rfsData.expiry,
                                                         nearValueDate : "SPOT",
                                                         fixingDate : "" ,
                                                         side : rfsData.sideType2Way,
                                                         priceType : rfsData.priceTypeSpot,
                                                         customerAccount : rfsData.customerAccount,
                                                         customerOrg: rfsData.customerOrg,
                                                         priceViewType: rfsData.aggregatedView,
                                                         depth: 5,
                                                         channel : rfsData.channel,
                                                         providers: ["VISA","WFNAD","NTFX"],
                                                         clOrderId: "SPOT_TwoWay_Rate_InvalidQuoteID_" + reqId
                                             }]
                                            var wsreq = { rfsSubscriptions : subrequests }
                                            console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                            console.log(wsreq)
                                            connection.send(JSON.stringify(wsreq));
                                            tradeDone = "false"
                                            i=1
                                            connection.onmessage = (e) => {
                                                res = JSON.parse(e.data)
                                                console.log("res : " + JSON.stringify(res))

                                                if (res.rfsRates && i<5) {
                                                    rate = res.rfsRates[0]
                                                    if(rate.status === "A" && tradeDone === "false") {
                                                        rfsActiveQuote = rate
                                                        bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                                        offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                                        console.log("RFS quote id = " + bidQuoteId)
                                                        console.log("RFS quote id" )
                                                        console.log(rfsActiveQuote)
                                                        systemReqId = rate.requestId
                                                        var tradereq = [{
                                                            clOrderId: "NothingDone"+reqId,
                                                            "requestId": systemReqId,
                                                            "bidQuoteId": "bidQuoteIdInvalid",
                                                            "offerQuoteId":offerQuoteId,
                                                            "bidSpotSpread": 0.23,
                                                            "offerSpotSpread": 0.04,
                                                            "bidForwardSpread": 0.0,
                                                            "offerForwardSpread": 0.0,
                                                            "bidFarForwardSpread":0,
                                                            "offerFarForwardSpread":0
                                                        }]
                                                        var wstradereq = { nothingDoneRequestList : tradereq }
                                                        console.log("RFS ND request sent : " )
                                                        console.log(wstradereq)
                                                        connection.send(JSON.stringify(wstradereq));
                                                        tradeDone = "true"
                                                    } else if (rate.status === "I") {
                                                        rfsInactiveQuote = res.rfsRates[0]
                                                     }
                                                    i++;
                                                } else if (res.rfsTradeAck) {
                                                    rfsTradeAck = res.rfsTradeAck[0]
                                                } else if (res.rfsResponses) {
                                                    rfsNDResponses = res.rfsResponses[0]
                                                    done()
                                                } else if (res.nothingDoneRequestResponsesList) {
                                                   nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                                }
                                            }
                                        });

                                         it("NSC5 RFS Nothing List Responses test", function () {

                                                                        let rfsNDListResponse = nothingDoneRequestResponsesList
                                                                        console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                                                        assert.equal(rfsNDListResponse.requestId,systemReqId)
                                                                        assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                                                        assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                                                        assert.equal("bidQuoteIdInvalid",rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                                                        assert.equal(0,rfsNDListResponse.bidForwardSpread, "bidForwardSpread is not correct")
                                                                        assert.equal(0,rfsNDListResponse.bidFarForwardSpread, "bidFarForwardSpread is not correct")
                                                                        assert.equal(0.23,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                                                        assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                                                        assert.equal(0.04,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")
                                                                        assert.equal(0,rfsNDListResponse.offerForwardSpread, "offerForwardSpread is not correct")
                                                                        assert.equal(0,rfsNDListResponse.offerFarForwardSpread, "offerFarForwardSpread is not correct")

                                                                 });

                                            it("NSC5 RFS ND - invalid quote ID test ", function () {

                                                                        let rfsNDResponse = rfsNDResponses
                                                                        console.log("rfsNDResponse is :  " +JSON.stringify(rfsNDResponse))
                                                                        rfsMessage = rfsNDResponse.rfsMessage
                                                                        console.log("rfsMessage is :  " +JSON.stringify(rfsMessage))

                                                                        assert.equal("NOTHING_DONE_REQUEST_FAILED",rfsNDResponse.rfsEvent, "rfsEvent is not correct")
                                                                        assert.exists(rfsNDResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")
                                                                       assert.exists(rfsMessage.eventTime);
                                                                       assert.equal('"QUOTE_NOT_AVAILABLE"', JSON.stringify(rfsNDResponse.errorCode), "errorCode didnt match")

                                                                 });


                                    after(function (done) {
                                            // {"rfsWithdrawRequests":[{"requestId":"G4796976d518465689568123f"}]}
                                            var withdrawReq = [{
                                                         "requestId" : systemReqId,
                                             }]
                                            var withdrawReqs = { rfsWithdrawRequests : withdrawReq }
                                            connection.send(JSON.stringify(withdrawReqs));
                                            done()
                                        })

                                    });

          describe("NSC2 RFS Spot Nothing Done - blank req ID", function ()  {
                let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                    console.log("RFS Spot Buy Trade - Two-way quote test -> reqId = " + reqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: ["VISA","WFNAD","NTFX"],
                                 clOrderId: "SPOT_TwoWay_Rate_OfferTrade_" + reqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                    console.log(wsreq)
                    connection.send(JSON.stringify(wsreq));
                    tradeDone = "false"
                    i=1
                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("res : " + JSON.stringify(res))

                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]
                            if(rate.status === "A" && tradeDone === "false") {
                                rfsActiveQuote = rate
                                bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                offerQuoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + bidQuoteId)
                                console.log("RFS quote id" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    clOrderId: "NothingDone"+reqId,
                                    "requestId": '',
                                    "bidQuoteId": bidQuoteId,
                                    "offerQuoteId":offerQuoteId,
                                    "bidSpotSpread": 0.05,
                                    "offerSpotSpread": 0.04,
                                    "bidForwardSpread": 0.0,
                                    "offerForwardSpread": 0.0,
                                    "bidFarForwardSpread":0,
                                    "offerFarForwardSpread":0
                                }]
                                var wstradereq = { nothingDoneRequestList : tradereq }
                                console.log("RFS ND request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                            i++;
                        } else if (res.rfsTradeAck) {
                            rfsTradeAck = res.rfsTradeAck[0]
                        } else if (res.rfsResponses) {
                            rfsNDResponses = res.rfsResponses[0]
                        } else if (res.nothingDoneRequestResponsesList) {
                           nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                            done()
                        }
                    }
                });

                 it("NSC2 RFS Nothing List Responses test", function () {

                                                let rfsNDListResponse = nothingDoneRequestResponsesList
                                                console.log("rfsNDListResponse is :  " +JSON.stringify(rfsNDListResponse))

                                                assert.equal(rfsNDListResponse.requestId,'')
                                                assert.equal("received",rfsNDListResponse.status, "rfsEvent is not correct")
                                                assert.exists(rfsNDListResponse.clOrderId, " NothingDone"+reqId, "clOrderId doesnt exist")

                                                assert.equal(bidQuoteId,rfsNDListResponse.bidQuoteId, "bidQuoteId is not correct")
                                                assert.equal(0,rfsNDListResponse.bidForwardSpread, "bidForwardSpread is not correct")
                                                assert.equal(0,rfsNDListResponse.bidFarForwardSpread, "bidFarForwardSpread is not correct")
                                                assert.equal(0.05,rfsNDListResponse.bidSpotSpread, "bidSpotSpread is not correct")

                                                assert.equal(offerQuoteId,rfsNDListResponse.offerQuoteId, "offerQuoteId is not correct")
                                                assert.equal(0.04,rfsNDListResponse.offerSpotSpread, "offerSpotSpread is not correct")
                                                assert.equal(0,rfsNDListResponse.offerForwardSpread, "offerForwardSpread is not correct")
                                                assert.equal(0,rfsNDListResponse.offerFarForwardSpread, "offerFarForwardSpread is not correct")

                                         });


                after(function (done) {
                    // {"rfsWithdrawRequests":[{"requestId":"G4796976d518465689568123f"}]}
                    var withdrawReq = [{
                                 "requestId" : systemReqId,
                     }]
                    var withdrawReqs = { rfsWithdrawRequests : withdrawReq }
                    connection.send(JSON.stringify(withdrawReqs));
                    done()
                })

            });

         describe("SC3 RFS FwdFwd 2-way Subscription - One-way Mismatch Sell Nothing Done -- Inactive Rate test", function ()  {
                            let dealtAmt = '1,000,000.00'

                            before(function (done) {
                                console.log('*************************** RFS Spot Buy Trade - Two-way quote Subscription  ************************** ' + new Date());
                                console.log("RFS Fwd Fwd Buy Trade - Two-way quote test -> reqId = " + reqId)
                                var subrequests = [{
                                         symbol : rfsData.symbol,
                                         amount : "1000000.0",
                                         dealtCurrency : rfsData.baseCcy,
                                         expiry: rfsData.expiry,
                                         nearValueDate : "1W",
        								 farDealtAmount : "2000000.0",
        								 farValueDate : "2W",
                                         fixingDate : "" ,
        								 farFixingDate : "",
                                         side : rfsData.sideTypeSell,
                                         priceType : rfsData.priceTypeFwdFwd,
                                         customerAccount : rfsData.customerAccount,
                                         customerOrg: rfsData.customerOrg,
                                         priceViewType: rfsData.aggregatedView,
                                         depth: 5,
                                         channel : rfsData.channel,
                                         providers: ["VISA","WFNAD","NTFX"],
                                         clOrderId: parseInt(reqId)
                                 }]
                                var wsreq = { rfsSubscriptions : subrequests }
                                console.log("RFS Spot Buy Trade - Two-way quote test ->  rfsSpotSubscriptions: request : ")
                                console.log(wsreq)
                                connection.send(JSON.stringify(wsreq));
                                tradeDone = "false"
                                i=1
                                connection.onmessage = (e) => {
                                    res = JSON.parse(e.data)
                                    console.log("res : " + JSON.stringify(res))

                                    if (res.rfsRates && i<5) {
                                        rate = res.rfsRates[0]
                                        if(rate.status === "A" && tradeDone === "false") {
                                            rfsActiveQuote = rate
                                            bidQuoteId =rfsActiveQuote.bids[0].quoteId
                                            console.log("RFS quote id" )
                                            console.log(rfsActiveQuote)
                                            systemReqId = rate.requestId
                                            var tradereq = [{
                                                clOrderId: "NothingDone"+reqId,
                                                "requestId": systemReqId,
                                                "bidQuoteId":bidQuoteId,
                                                "bidSpotSpread": 0.04,
                                                "bidForwardSpread": 0.0,
                                                "bidFarForwardSpread":0.124
                                            }]
                                            var wstradereq = { nothingDoneRequestList : tradereq }
                                            console.log("RFS ND request sent : " )
                                            console.log(wstradereq)
                                            connection.send(JSON.stringify(wstradereq));
                                            tradeDone = "true"
                                        } else if (rate.status === "I") {
                                            rfsInactiveQuote = res.rfsRates[0]
                                            done()
                                         }
                                        i++;
                                    } else if (res.rfsTradeAck) {
                                        rfsTradeAck = res.rfsTradeAck[0]
                                    } else if (res.rfsResponses) {
                                        rfsNDResponses = res.rfsResponses[0]
                                    } else if (res.rfsWithdrawAck) {
                                        rfsWithdrawAck = res.rfsWithdrawAck[0]
                                    } else if (res.rfsResponses) {
                                        rfsWithdrawResponse = res.rfsResponses[0]
                                    } else if (res.nothingDoneRequestResponsesList) {
                                       nothingDoneRequestResponsesList = res.nothingDoneRequestResponsesList[0]
                                    }
                                }
                            });

                            it("SC3 RFS Inactive Quote test", function () {

                                                                 let rfsInactiveQuoteRes = rfsInactiveQuote
                                                                 console.log("rfsInactiveQuoteRes is :  " +JSON.stringify(rfsInactiveQuoteRes))

                                                                 assert.equal(rfsInactiveQuoteRes.requestId,systemReqId)
                                                                 assert.equal("FwdFwd",rfsInactiveQuoteRes.priceType, "priceType is not correct")
                                                                 assert.exists(rfsInactiveQuoteRes.symbol, "EUR/USD", "symbol is incorrect")

                                                                 assert.equal(0,rfsInactiveQuoteRes.effectiveTime, "effectiveTime is not correct")
                                                                 assert.equal(-1,rfsInactiveQuoteRes.ttl, "ttl is not correct")
                                                                 assert.equal("EUR",rfsInactiveQuoteRes.dealtCurrency, "dealtCurrency is not correct")
                                                                 assert.equal("I",rfsInactiveQuoteRes.status, "status is not correct")

                                                                     assert.exists(rfsInactiveQuoteRes.nearValueDate)
                                                                     assert.exists(rfsInactiveQuoteRes.farValueDate)
                                                                     assert.exists(rfsInactiveQuoteRes.bids)
                                                                     assert.exists(rfsInactiveQuoteRes.offers)
                                                                     assert.exists(rfsInactiveQuoteRes.mids)

                                                          });

         });

     }  //negScenarios let

negScenarios();
