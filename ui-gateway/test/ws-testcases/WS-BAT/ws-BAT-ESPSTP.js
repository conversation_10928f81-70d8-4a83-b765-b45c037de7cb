const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/cqaproperties').env
const orderData = require('../../config/cqaproperties').orderData
const userData = require('../../config/cqaproperties').user

let wsconnect = function (done) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		done()
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let orderReceived;
let orderPendingNew;
let orderNew;
let orderFilled;
let orderRejected;
let order ;

let STPTC = function () {
	describe("Market Order Scenarios", function () {

		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});


        let execInstString = "OkToCross"
        let execInst = '["OkToCross"]';
        let reqId = Math.floor(Math.random() * 100)

        describe("Send STP subscription", function() {
            let stpResponse;
            let stpOrder;

		   describe("Place Market Order -- Sell IOC ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			before(function (done) {

		        console.log('*************************** Send STP subscription ************************** ' + new Date());
            	stpOrder = '{"stpSubscription": {"organization":"'+env.org+'"}}'
                console.log("STP request : " + stpOrder);
				connection.send(stpOrder);

				console.log('*************************** Market Sell IOC ************************** ' + new Date());
            	order = '{"orders":[{"coId":"' + reqId +'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                console.log("order request : " +order);
				connection.send(order);

				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                   console.log("res  : " + JSON.stringify(res));
				if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                    }
                    else if (res.stpMessages) {
                            stpResponse = res.stpMessages[0];
                            console.log("STP response received ===" +JSON.stringify(stpResponse));
                            done();
                           }
				} //connection.onmessage
			}); //before

//2025-03-04T09:55:06.290Z: {"stpMessages": [{"orderId":"***********","tradeId":"FXI4383007231","tradeType":"Spot","tenor":"SPOT","tradeDate":"2025-03-04","valueDate":"2025-03-06","maker":false,"side":"Buy","status":"Verified","symbol":"EUR/USD","currency":"EUR","customerAccount":"pfAutomationWS","customerOrg":"pfAutomationWSuser1","user":"pfAutomationWSuser1@pfAutomationWSuser1","counterparty":"VISA","counterpartyLongName":"VISA","counterpartyAccount":"VISALE1","coId":"16","channel":"API/WS/ESP","executionTime":"2025-03-04 09:55:06,120 +0000","dealtAmount":1000000.0,"settledAmount":1188680.0,"baseAmount":1000000.0,"termAmount":1188680.0,"spotRate":1.18868,"rate":1.18868,"event":"NEW","channelCategory":"API","upi":"EUR_USD_SPOT","uti":"Y28912MCQRZU5ST13393IN4383007231"}]}

			it("test - STP responses", function (done) {
			console.log("Order Filled message "+JSON.stringify(orderFilled.status));
			  let status = orderFilled.status;
				if ( status == "FILLED") {
                    assert.equal(reqId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                    //STP assertions
                    assert.equal(stpResponse.customerOrg, orderFilled.org);
                    assert.equal(stpResponse.symbol, orderFilled.symbol);
                    assert.equal(orderFilled.execId, stpResponse.tradeId);
                    assert.equal(orderFilled.orderId, stpResponse.orderId);
                    assert.equal(orderFilled.counterParty, stpResponse.counterparty);
                    assert.equal(orderFilled.size, stpResponse.baseAmount);
                    assert.equal(orderFilled.side, stpResponse.side);
                    assert.equal(orderFilled.currency, stpResponse.currency);
                    assert.equal(orderFilled.org, stpResponse.customerOrg);
                    assert.equal(orderFilled.settlCurrAmt, stpResponse.termAmount);
                    assert.equal(orderFilled.userFullName, stpResponse.user);
                    assert.equal(orderFilled.settlCurrAmt, stpResponse.termAmount);
                    assert.equal(stpResponse.status, "Verified");
                    assert.equal(stpResponse.event, "NEW");
                    assert.equal("API", stpResponse.channelCategory);
                    assert.equal("EUR_USD_SPOT", stpResponse.upi);
                } else {
                    console.log ("order status - Order didnt get filled")
                }
                done()
			});  //It

    	});  //describe mkt order

            }) //describe Send STP subscription

	});  //describe ws connect
};  //let

STPTC();

