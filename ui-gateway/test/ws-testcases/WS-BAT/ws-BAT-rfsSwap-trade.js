//package test.ws-testcases.rfs;



    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')

    //const login = require('../login').login
    const env = require('../../config/properties').env
    const rfsData = require('../../config/properties').rfsData
    const userData = require('../../config/properties').user

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let rfsTrade
    let res
    let errors
    let reqId = Math.floor(Math.random() * 100)
    let dltAmtInQuote = 1000000

    // Login credentials should be for MDF enabled org
    // For marketdata scripts, org should be getting rates in MDF
    // Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

    let wsconnect = function (done, cookies) {
    	//const websocket_url = 'ws://' + env.kongHost + ':' + env.kongPort + '/v2/fxstream'
        const websocket_url = 'wss://' + env.hostname +  '/v2/fxstream'
	    connection = new WebSocket(websocket_url, [], {
		'headers': {
			'Host': env.apiHost,
			'apikey': env.apikey
		}
	})


        connection.onopen = () => {
            done()
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

    let rfsSwapTradeTC = function(){

        describe("RFS Swap trade", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });


            describe("Two-Way Rate - Offer Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - Two-Way Rate - Offer Trade test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_TwoWay_OfferTrade_" + reqId
                    console.log("Rfs Swap - Two-Way Rate - Offer Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Two-Way Rate - Offer Trade Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Two-Way Rate - Offer Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_Swap_TwoWay_OfferTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote Offer Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.termAmount, "termAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].spotRate,trade.spotRate, "spotRate is not correct")
                    assert.equal(rfsActiveQuote.bids[0].rate,trade.rate, "rate is not correct")
                    assert.equal(rfsActiveQuote.bids[0].forwardPoint,trade.forwardPoints, "forwardPoints is not correct")

                    assert.equal(rfsActiveQuote.offers[0].rate,trade.farRate, "farRate is not correct")
                    assert.equal(rfsActiveQuote.offers[0].forwardPoint,trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapPoints, "swapPoints is not there")
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.swapTrade, "swapTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeSwap,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("1W",trade.farTenor, "farTenor is not correct")
                    assert.equal(rfsActiveQuote.farValueDate,trade.farValueDate, "farValueDate is not correct")
                    assert.exists(trade.farSide)
                    assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime))
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails))
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 EUR vs.');
                    txt = "Sell " + rfsActiveQuote.bids[0].dealtAmount + ".00 EUR vs. " + rfsActiveQuote.bids[0].settledAmount + ".00 USD. Value Date: SPOT and Buy " + rfsActiveQuote.bids[0].dealtAmount+ ".00 EUR vs. " + rfsActiveQuote.offers[0].settledAmount + ".00 USD. Value Date: 1W against " + trade.tradeId + ". From: " + trade.counterParty + ", Counterparty "
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string(txt)
               });

            });

            describe("Two-Way Rate - Bid Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - Two-Way Rate - Bid Trade test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_TwoWay_BidTrade_" + reqId
                    console.log("Rfs Swap - Two-Way Rate - Bid Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Two-Way Rate - Bid Trade Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Two-Way Rate - Bid Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.offers[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_Swap_TwoWay_BidTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote Bid Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal(rfsActiveQuote.offers[1].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[1].settledAmount,trade.settledAmount, "settledAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[1].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[1].settledAmount,trade.termAmount, "termAmount is not correct")

                    assert.equal(rfsActiveQuote.offers[1].spotRate,trade.spotRate, "spotRate is not correct")
                    assert.equal(rfsActiveQuote.offers[1].rate,trade.rate, "rate is not correct")
                    assert.equal(rfsActiveQuote.offers[1].forwardPoint,trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "swapPoints is not there")

                    assert.equal(rfsActiveQuote.bids[1].rate,trade.farRate, "farRate is not there")
                    assert.equal(rfsActiveQuote.bids[1].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[1].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[1].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[1].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[1].forwardPoint,trade.farForwardPoints, "farForwardPoints is not there")

                    assert.exists(trade.swapTrade, "swapTrade is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)
                    assert.equal(rfsData.priceTypeSwap,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "farValueDate is not correct")
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.exists(trade.farTenor)
                    //assert.exists(trade.farValueDate)
                    assert.equal(rfsActiveQuote.farValueDate,trade.farValueDate, "farValueDate is not correct")
                    assert.exists(trade.farSide)
					assert.equal("Sell",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime))
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails))
                    txt = "Buy " + rfsActiveQuote.offers[1].dealtAmount + ".00 EUR vs. " + rfsActiveQuote.offers[1].settledAmount + ".00 USD. Value Date: SPOT and Sell " + rfsActiveQuote.bids[1].dealtAmount+ ".00 EUR vs. " + rfsActiveQuote.bids[1].settledAmount + ".00 USD. Value Date: 1W against " + trade.tradeId + ". From: " + trade.counterParty + ", Counterparty "
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string(txt)

               });

            });

            describe("One-Way Rate - Offer Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - One-Way Rate - Offer Trade test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_OneWay_OfferTrade_" + reqId
                    console.log("Rfs Swap - One-Way Rate - Offer Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - One-Way Rate - Offer Trade Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - One-Way Rate - Offer Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_Swap_OneWay_OfferTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - One-Way quote Offer Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.termAmount, "termAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].spotRate,trade.spotRate, "spotRate is not correct")
                    assert.equal(rfsActiveQuote.bids[0].rate,trade.rate, "rate is not correct")
                    assert.equal(rfsActiveQuote.bids[0].forwardPoint,trade.forwardPoints, "forwardPoints is not correct")

                    assert.equal(rfsActiveQuote.offers[0].rate,trade.farRate, "farRate is not correct")
                    assert.equal(rfsActiveQuote.offers[0].forwardPoint,trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapPoints, "swapPoints is not there")
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.swapTrade, "swapTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeSwap,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "valueDate is not correct")
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.equal("1W",trade.farTenor, "farTenor is not correct")
                    assert.equal(rfsActiveQuote.farValueDate,trade.farValueDate, "farValueDate is not correct")
                    assert.exists(trade.farSide)
                    assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime))
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails))
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 EUR vs.');
                    txt = "Sell " + rfsActiveQuote.bids[0].dealtAmount + ".00 EUR vs. " + rfsActiveQuote.bids[0].settledAmount + ".00 USD. Value Date: SPOT and Buy " + rfsActiveQuote.bids[0].dealtAmount+ ".00 EUR vs. " + rfsActiveQuote.offers[0].settledAmount + ".00 USD. Value Date: 1W against " + trade.tradeId + ". From: " + trade.counterParty + ", Counterparty "
                    //Sell 1000000.00 EUR vs. 1272020.00 USD. Value Date: SPOT and Buy 1000000.00 EUR vs. 1272020.10 USD. Value Date: 1W against FXI4296127085. From: VISA, Counterparty VISALE1. External ID:  Price verified.'
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string(txt)

               });

            });

            describe("One-Way Rate - Bid Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - One-Way Rate - Bid Trade test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_OneWay_BidTrade_" + reqId
                    console.log("Rfs Swap - One-Way Rate - Bid Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - One-Way Rate - Bid Trade Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - One-Way Rate - Bid Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.baseCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_Swap_OneWay_Trade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - One-Way quote Bid Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.dealtAmount, "dealtAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.settledAmount, "settledAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].dealtAmount,trade.baseAmount, "baseAmount is not correct")
                    assert.equal(rfsActiveQuote.offers[0].settledAmount,trade.termAmount, "termAmount is not correct")

                    assert.equal(rfsActiveQuote.offers[0].spotRate,trade.spotRate, "spotRate is not correct")
                    assert.equal(rfsActiveQuote.offers[0].rate,trade.rate, "rate is not correct")
                    assert.equal(rfsActiveQuote.offers[0].forwardPoint,trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "swapPoints is not there")

                    assert.equal(rfsActiveQuote.bids[0].rate,trade.farRate, "farRate is not there")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.farSettledAmount, "farSettledAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].dealtAmount,trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].settledAmount,trade.farTermAmount, "farTermAmount is not correct")
                    assert.equal(rfsActiveQuote.bids[0].forwardPoint,trade.farForwardPoints, "farForwardPoints is not there")

                    assert.exists(trade.swapTrade, "swapTrade is not correct")
                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)
                    assert.equal(rfsData.priceTypeSwap,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.equal(rfsActiveQuote.nearValueDate,trade.valueDate, "farValueDate is not correct")
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.baseCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.exists(trade.farTenor)
                    //assert.exists(trade.farValueDate)
                    assert.equal(rfsActiveQuote.farValueDate,trade.farValueDate, "farValueDate is not correct")
                    assert.exists(trade.farSide)
					assert.equal("Sell",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                    assert.exists(JSON.stringify(rfsMessage.eventTime))
                    assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                    assert.exists(JSON.stringify(rfsMessage.eventDetails))
                    txt = "Buy " + rfsActiveQuote.offers[0].dealtAmount + ".00 EUR vs. " + rfsActiveQuote.offers[0].settledAmount + ".00 USD. Value Date: SPOT and Sell " + rfsActiveQuote.bids[0].dealtAmount+ ".00 EUR vs. " + rfsActiveQuote.bids[0].settledAmount + ".00 USD. Value Date: 1W against " + trade.tradeId + ". From: " + trade.counterParty + ", Counterparty "
                    expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string(txt)


               });

            });

            describe("Two-Way Rate - Offer Term Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - Two-Way Rate - Offer Term Trade test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_TwoWay_OfferTermTrade_" + reqId
                    console.log("Rfs Swap - Two-Way Rate - Offer Term Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Two-Way Rate - Offer Term Trade Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Two-Way Rate - Offer Term Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_Swap_TwoWay_OfferTermTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote Offer Term Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000",trade.dealtAmount, "dealtAmount is not correct")
                    assert.exists(trade.settledAmount)
                    assert.equal("1000000",trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.baseAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.exists(trade.rate)
                    assert.equal("0",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "swapPoints is not there")
                    assert.exists(trade.farRate, "farRate is not there")
                    assert.equal("1000000",trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.exists(trade.farSettledAmount, "farSettledAmount is not there")
                    assert.equal("1000000",trade.farTermAmount, "farTermAmount is not correct")
                    assert.exists(trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapTrade, "swapTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeSwap,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.exists(trade.farTenor)
                    assert.exists(trade.farValueDate)
                    assert.exists(trade.farSide)
                    assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 USD vs.');
               });

            });

            describe("Two-Way Rate - Bid Term Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - Two-Way Rate - Bid Term Trade test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_TwoWay_BidTermTrade_" + reqId
                    console.log("Rfs Swap - Two-Way Rate - Bid Term Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - Two-Way Rate - Bid Term Trade Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - Two-Way Rate - Bid Term Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_Swap_TwoWay_BidTermTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - Two-way quote Bid Term Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000",trade.dealtAmount, "dealtAmount is not correct")
                    assert.exists(trade.settledAmount)
                    assert.equal("1000000",trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.baseAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.exists(trade.rate)
                    assert.equal("0",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "swapPoints is not there")
                    assert.exists(trade.farRate, "farRate is not there")
                    assert.equal("1000000",trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.exists(trade.farSettledAmount, "farSettledAmount is not there")
                    assert.equal("1000000",trade.farTermAmount, "farTermAmount is not correct")
                    assert.exists(trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapTrade, "swapTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeSwap,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.exists(trade.farTenor)
                    assert.exists(trade.farValueDate)
                    assert.exists(trade.farSide)
					assert.equal("Sell",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD vs.');
               });

            });

            describe("One-Way Rate - Offer Term Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - One-Way Rate - Offer Term Trade test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_OneWay_OfferTermTrade_" + reqId
                    console.log("Rfs Swap - One-Way Rate - Offer Term Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeBuy,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - One-Way Rate - Offer Term Trade Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - One-Way Rate - Offer Term Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "BUY",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_Swap_OneWay_OfferTermTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - One-Way quote Offer Term Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000",trade.dealtAmount, "dealtAmount is not correct")
                    assert.exists(trade.settledAmount)
                    assert.equal("1000000",trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.baseAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.exists(trade.rate)
                    assert.equal("0",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "swapPoints is not there")
                    assert.exists(trade.farRate, "farRate is not there")
                    assert.equal("1000000",trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.exists(trade.farSettledAmount, "farSettledAmount is not there")
                    assert.equal("1000000",trade.farTermAmount, "farTermAmount is not correct")
                    assert.exists(trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapTrade, "swapTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeSwap,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Sell",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.exists(trade.farTenor)
                    assert.exists(trade.farValueDate)
                    assert.exists(trade.farSide)
                    assert.equal("Buy",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Sell 1000000.00 USD vs.');
               });

            });

            describe("One-Way Rate - Bid Term Trade test ", function () {
               let dealtAmt = '1,000,000.00'

                before(function (done) {
                    console.log('*************************** rfs Swap - One-Way Rate - Bid Term Trade test ************************** ' + new Date());
                    tempReqId = "RFS_Swap_OneWay_BidTermTrade_" + reqId
                    console.log("Rfs Swap - One-Way Rate - Bid Term Trade -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.termCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
								 farDealtAmount : "1000000.0",
								 farValueDate : "1W",
                                 fixingDate : "" ,
								 farFixingDate : "",
                                 side : rfsData.sideTypeSell,
                                 priceType : rfsData.priceTypeSwap,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs Swap - One-Way Rate - Bid Term Trade Test ->  rfsSwapSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1
                    tradeDone = false

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs Swap - One-Way Rate - Bid Term Trade  -> res : " + JSON.stringify(res))
                        if (res.rfsRates && i<5) {
                            rate = res.rfsRates[0]

                            if(rate.status === "A" && tradeDone === false) {
                                rfsActiveQuote = rate
                                quoteId =rfsActiveQuote.bids[0].quoteId
                                console.log("RFS quote id = " + quoteId)
                                console.log("RFS quote" )
                                console.log(rfsActiveQuote)
                                systemReqId = rate.requestId
                                var tradereq = [{
                                    quoteId: quoteId,
                                    side: "SELL",
                                    symbol: rfsData.symbol,
                                    dealtCurrency: rfsData.termCcy,
                                    tradeChannel: "DNET/RFS/BB",
                                    clOrderId: tempReqId
                                }]
                                var wstradereq = { rfsTrades : tradereq }
                                console.log("Trade request sent : " )
                                console.log(wstradereq)
                                connection.send(JSON.stringify(wstradereq));
                                tradeDone = "true"
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             }
                        } else if (res.rfsTradeAck) {
                             rfsTradeAck = res.rfsTradeAck[0]
                         } else if (res.rfsTradeResponses) {
                             rfsTradeResponses = res.rfsTradeResponses[0]
                             rfsTrade = res.rfsTradeResponses[0]
                             done()
                         } else if (res.rfsWithdrawAck) {
                             rfsWithdrawAck = res.rfsWithdrawAck[0]
                             done()
                         } else if (res.rfsResponses) {
                             rfsWithdrawResponse = res.rfsResponses[0]
                             done()
                         }
                    }
                });

                it("RFS_Swap_OneWay_BidTermTrade test", function () {
                    let trade = rfsTrade.trades[0]
                    rfsMessage = rfsTrade.rfsMessage
                    console.log("RFS Spot Buy Trade - One-Way quote Bid Term Trade  -> Trade details : " + JSON.stringify(rfsTrade))
                    console.log(rfsTrade)

                    assert.exists(trade.executionTime)
                    assert.equal("1000000",trade.dealtAmount, "dealtAmount is not correct")
                    assert.exists(trade.settledAmount)
                    assert.equal("1000000",trade.termAmount, "termAmount is not correct")
                    assert.exists(trade.baseAmount)
                    assert.exists(trade.spotRate)
                    assert.exists(trade.rate)
                    assert.exists(trade.rate)
                    assert.equal("0",trade.forwardPoints, "forwardPoints is not correct")
                    assert.exists(trade.swapPoints, "swapPoints is not there")
                    assert.exists(trade.farRate, "farRate is not there")
                    assert.equal("1000000",trade.farDealtAmount, "farDealtAmount is not correct")
                    assert.exists(trade.farSettledAmount, "farSettledAmount is not there")
                    assert.equal("1000000",trade.farTermAmount, "farTermAmount is not correct")
                    assert.exists(trade.farBaseAmount, "farBaseAmount is not correct")
                    assert.exists(trade.farForwardPoints, "farForwardPoints is not correct")
                    assert.exists(trade.swapTrade, "swapTrade is not correct")

                    assert.exists(trade.orderId)
                    assert.exists(trade.tradeId)

                    assert.equal(rfsData.priceTypeSwap,trade.tradeType, "tradeType is not Spot")
                    assert.equal("SPOT",trade.tenor, "tenor is not Spot")
                    assert.exists(trade.tradeDate)
                    assert.exists(trade.valueDate)
                    assert.equal(false,trade.maker, "maker value is not correct")
                    assert.equal("Buy",trade.orderSide, "orderSide is not correct")
                    assert.equal("Verified",trade.status, "status is not correct")
                    assert.equal(rfsData.symbol,trade.symbol, "symbol is not correct")
                    assert.equal(rfsData.termCcy,trade.dealtIns, "dealtIns is not correct")
                    assert.equal(rfsData.customerAccount,trade.customerAccount, "customerAccount is not correct")
                    assert.equal(rfsData.customerOrg,trade.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.trader, "trader is not correct")
                    assert.exists(trade.counterParty)
                    assert.exists(trade.cptyLongName)
                    assert.exists(trade.cptyTradeId)
                    assert.exists(trade.counterPartyAccount)
                    assert.exists(trade.farTenor)
                    assert.exists(trade.farValueDate)
                    assert.exists(trade.farSide)
					assert.equal("Sell",trade.farSide, "farSide is not correct")

                    //assert.exists(trade.counterpartyBLEI)
                    assert.exists(trade.UPI)
                    assert.exists(trade.UTI)
                    assert.exists(trade.externalRequestId)
                    assert.exists(trade.requestId, "requestId doesnt exist")
                    assert.equal(false,trade.isnet, "isnet value is not correct")
                    assert.equal(false,trade.isMidMarket, "isMidMarket value is not correct")
                    assert.exists(trade.channel, "channel doesnt exist")
                    assert.exists(trade.header.referenceId)
                    assert.equal(rfsData.customerOrg,trade.header.customerOrg, "customerOrg is not correct")
                    assert.equal(userData.username,trade.header.customerId, "customerId is not correct")
                    assert.equal(rfsData.customerAccount,trade.header.customerAccount, "customerAccount is not correct")

                   assert.exists(JSON.stringify(rfsMessage.eventTime))
                   assert.equal('"RFS Trade Verified"', JSON.stringify(rfsMessage.eventName), "EventName didnt match")
                   assert.exists(JSON.stringify(rfsMessage.eventDetails))
                   expect(JSON.stringify(rfsMessage.eventDetails)).to.have.string('"Buy 1000000.00 USD vs.');
               });

            });

        });
    };

rfsSwapTradeTC();
