const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/cqaproperties').env
const orderData = require('../../config/cqaproperties').orderData
const userData = require('../../config/cqaproperties').user

let wsconnect = function (done) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		done()
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

let orderReceived;
let orderPendingNew;
let orderNew;
let orderFilled;
let orderRejected;
let order ;

let MarketOrderTC = function () {
	describe("Market Order Scenarios", function () {


		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});


        let execInstString = "OkToCross"
        let execInst = ["OkToCross"];
        let reqId = "request-" + Math.floor(Math.random() * 100000);

		describe("Place Market Order -- Sell IOC ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

			before(function (done) {
				console.log('*************************** Market Sell IOC ************************** ' + new Date());
            	//order = '{"orders":[{"coId":"' + reqId +'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                order = {
                    "orders": [{
                        "coId": reqId,
                        "currency": orderData.baseCurrency_EURUSD,
                        "size": orderData.size,
                        "type": orderData.MARKET,
                        "side": orderData.SELL,
                        "symbol": orderData.symbol_EURUSD,
                        "execFlags": execInst,
                        "timeInForce": orderData.IOC
                    }]
                }
                console.log("order request : " + JSON.stringify(order));
				connection.send(JSON.stringify(order));
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                   console.log("order  : " + JSON.stringify(res.orderResponses));
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}else if(orderResponse.status === 'CANCELED'){
                            orderrejected = orderResponse;
                            done();
                        }
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest));
                            done()
                    }
				}
			});

			it("test - Sell IOC", function () {
                console.log("test - Sell IOC >>>> =" + JSON.stringify(orderFilled))
				if (orderFilled) {
                    assert.equal(reqId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.settlCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                    //assert.equal(userData.orgname, orderFilled.org);
                    //assert.equal(execInst, orderFilled.execFlags); not needed to return
                } else {
                    assert.fail("order status - Order didnt get filled");
                    console.log ("order status - Order didnt get filled")
                }
			});
		});



	});
};

MarketOrderTC();

