const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/cqaproperties').env
const benckmarkData = require('../../config/cqaproperties').benckmarkData

let fxbenchmarkSubscriptionResponses
let fxbenchmarkUnsubscriptionResponses
let fxbenchmark
let benchmarkRequest

//Use this command to run -- npx mocha --timeout 20000 ws-testcases\WS-BAT\ws-BAT-benchmark-test.js --exit

// below are needed for schema validation
const Validator = require('jsonschema').Validator;
const v = new Validator();
const benchmarkSchema = require('../../config/top10/benchmark-schema')
let res

let wsconnect = function (done) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

	connection.onopen = () => {
		console.log('WS connected successfully: ' + new Date());
		setTimeout(function () { done(); }, 5000);
	}

	connection.onerror = (error) => {
		console.log(`WebSocket error: ${error}`)
	}
}

// benchmark rates are obtained from broker FXB and stream benchmark in Top10
let benchmarkTC = function(){
    describe("Benchmark  ", function(){

	    before(function (done) {
            wsconnect(done);
	    });

    	after(function (done) {
    		connection.close()
    		done()
    	});


    	describe("Benchmark - Subscription test ", function () {
    		before(function (done) {
    			connection.onmessage = (e) => {
    				let res = JSON.parse(e.data)
    			 	console.log("res =" + JSON.stringify(res))
    				if (res.fxbenchmarkSubscriptionResponses) {
						if(res.fxbenchmarkSubscriptionResponses[0].request.symbol !== benckmarkData.symbol) return;
    					fxbenchmarkSubscriptionResponses = res.fxbenchmarkSubscriptionResponses
                        console.log('fxbenchmarkSubscriptionResponses : ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
    					done()
    				}
    			}
     			connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
    		});

    		after(function (done) {
                connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
                this.timeout(0);
                done()
           	});

    		it("subscription test", function(){
        		console.log('subscription response: ' + JSON.stringify(fxbenchmarkSubscriptionResponses))
    			assert.equal(fxbenchmarkSubscriptionResponses[0].status, 'success')
    		});
     	});

        describe("Benchmark - Rate test ", function () {

            		before(function (done) {
            			connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol2 + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
            			connection.onmessage = (e) => {

            				res = JSON.parse(e.data)
            				console.log("res =" + JSON.stringify(res))
            				if (res.fxbenchmark) {
            					let temp = res.fxbenchmark;
                                console.log('fxbenchmark : ' + JSON.stringify(temp))
								if(temp.currencyPair !== benckmarkData.symbol2) return;
								fxbenchmark = temp
            					done();
            				}

                  			}
             		});

            		after(function (done) {
                        connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol2 + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
                        done()
                   	});

     				it("Benchmark rate test", function(){
                                    		console.log('fxbenchmark response: ' + JSON.stringify(fxbenchmark))
                                			assert.equal(benckmarkData.symbol2, fxbenchmark.currencyPair)
                                			assert.exists(fxbenchmark.rate)
                                			assert.exists(fxbenchmark.timestamp)
                                			assert.exists(fxbenchmark.guid)
                                		});

             	}); //Close rate test

        describe("Benchmark - UnSubscription test ", function () {
            before(function (done) {
             	 connection.send('{"fxbenchmarkSubscriptions":[{"symbol":"' + benckmarkData.symbol3 + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
                 connection.send('{"fxbenchmarkUnsubscriptions":[{"symbol":"' + benckmarkData.symbol3 + '","priceSource":"' + benckmarkData.priceSource + '"}]}')
                        connection.onmessage = (e) => {
                                           let res = JSON.parse(e.data)
                            				console.log("console log -- unsubscription res :" + JSON.stringify(res))
                            				if (res.fxbenchmarkUnsubscriptionResponses) {
												if(res.fxbenchmarkUnsubscriptionResponses[0].request.symbol !== benckmarkData.symbol3) return;
                            					fxbenchmarkUnsubscriptionResponses = res.fxbenchmarkUnsubscriptionResponses
                                                done();
                                   				}
                                          }
                                 }); //close before

                 it("Unsubscription test it function", function(){
                        console.log('Unsubscription response test: ' + JSON.stringify(fxbenchmarkUnsubscriptionResponses))
                        assert.equal(fxbenchmarkUnsubscriptionResponses[0].status, 'success')
                                                          			            		//done();
                                                          			            		//this.timeout(0);
                      }); //close it


            	    after(function(done){
            		    console.log("Inside after function -- exiting now")
                        //this.timeout(0);
                        done();
                       }) //close after

         	}); //close unsubacripion test


 	});
};

benchmarkTC();
