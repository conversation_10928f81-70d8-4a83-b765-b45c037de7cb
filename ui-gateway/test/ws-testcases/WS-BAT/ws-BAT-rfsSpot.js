    const assert = require('chai').assert
    const expect = require('chai').expect

    const WebSocket = require('ws')
    const env = require('../../config/cqaproperties').env
    const rfsData = require('../../config/cqaproperties').rfsData

    let connection
    let rateSubscriptionResponses
    let rateUnsubscriptionResponses
    let systemReqId
    let rfsWithdrawResponse
    let rfsInactiveQuote
    let rfsActiveQuote
    let rfsSubscriptionAck
    let res
    let errors

    let reqId = Math.floor(Math.random() * 100)

    // Login credentials should be for MDF enabled org
    // For marketdata scripts, org should be getting rates in MDF
    // Aggregation method requested in query should be same as that of the one configured in Orgs LRs page

    let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

    let rfsSpotTC = function(){

        describe("RFS Spot ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });

            //{ "rfsSubscriptions" : [ { "symbol": "EUR/USD", "amount": "1000000.0", "dealtCurrency": "EUR", "expiry": 15, "nearValueDate": "SPOT", "farDealtAmount" : "","farValueDate" : "", "fixingDate" : "" , "farFixingDate" : "", "side": "BUY", "priceType": "Swap", "customerAccount": "pfOrg", "customerOrg": "pfOrg", "priceViewType": 1, "depth": 2, "channel": "DNET/RFS/BB", "providers": ["NTFX","MSFX","SG","SUCD","UBS","WFNA"], "clOrderId": "view1MultiLP2" } ] }
            describe("Two-Way Rate test ", function () {
                let dealtAmt = '1000000'

                before(function (done) {
                    console.log('*************************** rfs spot - Two-Way Rate test ************************** ' + new Date());
                    tempReqId = "RFS_Spot_TwoWay_RateTest_" + reqId
                    console.log("Rfs spot - Two-Way RateTest -> reqId = " + tempReqId)
                    var subrequests = [{
                                 symbol : rfsData.symbol,
                                 amount : "1000000.0",
                                 dealtCurrency : rfsData.baseCcy,
                                 expiry: rfsData.expiry,
                                 nearValueDate : "SPOT",
                                 fixingDate : "" ,
                                 side : rfsData.sideType2Way,
                                 priceType : rfsData.priceTypeSpot,
                                 customerAccount : rfsData.customerAccount,
                                 customerOrg: rfsData.customerOrg,
                                 priceViewType: rfsData.aggregatedView,
                                 depth: 5,
                                 channel : rfsData.channel,
                                 providers: rfsData.providers,
                                 clOrderId: tempReqId
                     }]
                    var wsreq = { rfsSubscriptions : subrequests }
                    console.log("Rfs spot - Two-Way RateTest ->  rfsSpotSubscriptions: request : " + JSON.stringify(wsreq))
                    connection.send(JSON.stringify(wsreq));
                    i = 1

                    connection.onmessage = (e) => {
                        res = JSON.parse(e.data)
                        console.log("Rfs spot - Two-Way RateTest -> res : " + JSON.stringify(res))
                        if (res.rfsRates) {
                            rate = res.rfsRates[0]
                            if(i < 3 && rate.status === "A") {
                                rfsActiveQuote = rate
                                i= i + 1
                                systemReqId = rate.requestId
                            } else if (rate.status === "I") {
                                rfsInactiveQuote = res.rfsRates[0]
                             } else if (i ===3) {
                                connection.send('{"rfsWithdrawRequests":[{"requestId":"' + systemReqId + '"}]}')
                                i++
                             }
                        } else if (res.rfsWithdrawAck) {
                            rfsWithdrawAck = res.rfsWithdrawAck[0]
                        } else if (res.rfsResponses) {
                            rfsWithdrawResponse = res.rfsResponses[0]
                            done()
                        }
                    }
                });  //before
// quote : {"requestId":"G4796976d517c0c3da847340","priceType":"Spot","effectiveTime":0,"symbol":"EUR/USD","ttl":10,"dealtCurrency":"EUR","status":"A","nearSettleDate":"09/24/2021","bids":[{"legType":0,"quoteId":"G-4796976e3-17c0c3da8d4-WFNA-7e-pfOrg-WFNA-1632292939992","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,640.00","provider":"WFNA","rate":1.18664,"spotRate":1.18664,"forwardPoint":0,"midRate":0},{"legType":0,"quoteId":"G-4796976cf-17c0c3da8b6-UBSA-305-pfOrg-UBS-1632292939968","type":"BID","dealtAmount":"1,000,000.00","settledAmount":"1,186,200.00","provider":"UBS","rate":1.1862,"spotRate":1.1862,"forwardPoint":0,"midRate":0}],"offers":[{"legType":0,"quoteId":"G-4796976cf-17c0c3da8b6-UBSA-305-pfOrg-UBS-1632292939968","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,530.00","provider":"UBS","rate":1.18653,"spotRate":1.18653,"forwardPoint":0,"midRate":0},{"legType":0,"quoteId":"G-4796976e3-17c0c3da8d4-WFNA-7e-pfOrg-WFNA-1632292939992","type":"OFFER","dealtAmount":"1,000,000.00","settledAmount":"1,186,550.00","provider":"WFNA","rate":1.18655,"spotRate":1.18655,"forwardPoint":0,"midRate":0}],"mids":[]}

                it("Rate test", function () {
                    console.log("Rfs spot - Two-Way RateTest -> rfsActiveQuote : " )
                    console.log(rfsActiveQuote)
                    let bidsArray = rfsActiveQuote.bids
                    let bidRate = bidsArray[0]
                    let offerArray = rfsActiveQuote.offers
                    let offerRate = offerArray[0]
                    assert.exists(rfsActiveQuote.requestId)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.exists(rfsActiveQuote.ttl)
                    // bid rate validation
                    assert.exists(bidRate.quoteId)
                    assert.exists(bidRate.settledAmount)
                    assert.notEqual("0",bidRate.settledAmount, "settAmt is zero")
                    assert.exists(bidRate.provider)
                    assert.isNotNull(bidRate.provider)
                    assert.exists(bidRate.rate)
                    assert.notEqual("0",bidRate.rate, "rate is zero")
                    assert.exists(bidRate.spotRate)
                    assert.notEqual("0",bidRate.spotRate, "spotRate is zero")
                    assert.exists(bidRate.forwardPoint)
                    assert.equal("0",bidRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(bidRate.midRate)
                    // offer rate validation
                    assert.exists(offerRate.quoteId)
                    assert.exists(offerRate.settledAmount)
                    assert.notEqual("0",offerRate.settledAmount, "settAmt is zero")
                    assert.exists(offerRate.provider)
                    assert.isNotNull(offerRate.provider)
                    assert.exists(offerRate.rate)
                    assert.notEqual("0",offerRate.rate, "rate is zero")
                    assert.exists(offerRate.spotRate)
                    assert.notEqual("0",offerRate.spotRate, "spotRate is zero")
                    assert.exists(offerRate.forwardPoint)
                    assert.equal("0",offerRate.forwardPoint, "forwardPoint is not zero")
                    assert.exists(offerRate.midRate)

                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.exists(rfsActiveQuote.effectiveTime)
                    assert.equal(rfsData.symbol, rfsActiveQuote.symbol)
                    assert.equal(rfsData.priceTypeSpot, rfsActiveQuote.priceType)
                    assert.equal(rfsData.baseCcy, rfsActiveQuote.dealtCurrency)
                    assert.equal('A', rfsActiveQuote.status)

                    assert.equal('BID', bidRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, bidRate.dealtAmount)
                    assert.equal('OFFER', offerRate.type) //rfsActiveQuote.bids[0].dealtCurrency)
                    assert.equal(dealtAmt, offerRate.dealtAmount)
               });  //it

            });  //2-way rate test

           });  //RFS Spot ws connect


        } //let

rfsSpotTC();