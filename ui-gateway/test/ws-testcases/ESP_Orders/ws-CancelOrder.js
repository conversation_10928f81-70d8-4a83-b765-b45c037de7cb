	const assert = require('chai').assert

	const WebSocket = require('ws')
	const env = require('../../config/properties').env
	const orderData = require('../../config/properties').orderData
	const userData = require('../../config/properties').user

	let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'

        connection = new WebSocket(websocket_url, [], {
            'headers': {
                'Host': env.apiHost,
                'apikey': env.apikey
            }
        })

        connection.onopen = () => {
            console.log('WS connected successfully: ' + new Date());
            setTimeout(function () { done(); }, 5000);
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
	}

	let orderReceived;
	let orderPendingNew;
	let orderNew;
	let orderFilled;
	let orderrejected;
	let order;
	let Cancelres;
	let orderPendingCancel, orderCancel, orderCancelReceived;

	let CancelOrderTC = function () {
	    describe("Cancel Order - ", function () {

            before(function (done) {
                wsconnect(done);
            });

            after(function () {
                connection.close()
            });

            describe("CancelOrder test ", function () {
                let orderReceived;
                let orderPendingNew;
                let orderNew;
                let orderFilled;
                let orderrejected;
                let order,Cancelorder;
                let coId =  new Date(). getTime();

                before(function (done)
                {
                    console.log('*************************** Cancel Order API ************************** ' + new Date());
                    console.log('*** coId **** ' + coId);
                    //order = '{"orders":[{"coId":"MrktIOCSell2","currency":"' + orderData.currency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                    order = '{"orders":[{"coId":"'+coId+ '","currency":"EUR", "execFlags" : [], "size":"1000000", "type":"Limit","price":"1.5", "side":"Sell", "symbol":"EUR/USD", "timeInForce":"GTC"}]}';
                    console.log("order request : " + order);
                    connection.send(order);

// These orders are placed for cancelAll api, these orders will get placed and be ready to cancelAll

                    connection.onmessage = (e) => {
                        let res = JSON.parse(e.data)
                        console.log("order  : " + JSON.stringify(res.orderResponses));
                        if (res.orderResponses) {
                            let orderResponse = res.orderResponses[0];
                            if (orderResponse.status === 'RECEIVED') {
                                orderReceived = orderResponse;
                                console.log("order Received : " + JSON.stringify(orderNew));
                            } else if (orderResponse.status === 'PENDING_NEW') {
                                orderPendingNew = orderResponse;
                                console.log("order Pending New : " + JSON.stringify(orderPendingNew));
                            }else if (orderResponse.status === 'NEW') {
                                orderNew = orderResponse;
                                console.log("order New : " + JSON.stringify(orderNew));
                                done()
                            }
                        } else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                            console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                        }
                    }
                });

                after(function (done){
                    done();
                });

                it("Market Sell IOC Order Test", function () {
                    if (orderPendingNew) {
                        assert.equal(coId, orderPendingNew.coId);
                        assert.equal(orderData.LIMIT, orderPendingNew.type);
                        assert.equal(orderData.GTC, orderPendingNew.timeInForce);
                        assert.equal(orderData.SELL, orderPendingNew.side);
                        assert.equal(orderData.baseCurrency_EURUSD, orderPendingNew.currency);
                        assert.equal(orderData.symbol_EURUSD, orderPendingNew.symbol);
                        assert.equal(orderData.ACTION, orderPendingNew.action);
                        assert.equal("1000000", orderPendingNew.leavesQty);
                    } else {
                        assert.equal('True', 'False')
                        console.log ("order status - Order got filled")
                    }
                });

                describe("Cancel order test", function() {
                    before(function (done) {
                        //Placing an order cancel request
                        Cancelorder = '{"orders":[{"action":"cancel", "coId":"'+coId+'", "requestId":"req7", "side":"Sell", "symbol":"EUR/USD", "size":"1000000"}]}';
                        console.log("CancelOrder request :" + Cancelorder)
                        connection.send(Cancelorder);
                        connection.onmessage = (e) => {
                            Cancelres = JSON.parse(e.data)
                            console.log("Cancel order response: " +JSON.stringify(Cancelres.orderResponses));
                            if (Cancelres.orderResponses) {
                                let orderResponse = Cancelres.orderResponses[0];
                            if (orderResponse.status === 'RECEIVED') {
                                orderCancelReceived = orderResponse;
                                console.log("order Received : " + JSON.stringify(orderCancelReceived));
                            } else if (orderResponse.status === 'PENDING_CANCEL') {
                                orderPendingCancel = orderResponse;
                                console.log("order Pending Cancel : " + JSON.stringify(orderPendingCancel));
                            }else if (orderResponse.status === 'CANCELED') {
                                orderCancel = orderResponse;
                                console.log("order Cancel : " + JSON.stringify(orderCancel));
                                done()
                                }
                            } else if (Cancelres.errors) {
                                InvalidOrderRequest = Cancelres.errors;
                                console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest));
                                done()
                            }
                        }
                    });

                    it("Cancel Order Test", function () {
                        if (orderCancel) {
                            //assert.equal(coId, orderCancel.coId);
                            assert.equal(orderData.LIMIT, orderCancel.type);
                            assert.equal(orderData.GTC, orderCancel.timeInForce);
                            assert.equal(orderData.symbol_EURUSD, orderCancel.symbol);
                            assert.equal("cancel", orderCancelReceived.action);
                            assert.equal("cancel", orderCancel.action);
                            assert.equal(orderNew.orderId, orderCancel.orderId);
                            assert.equal(orderNew.leavesQty, orderCancel.leavesQty);
                        } else {
                            console.log ("order status is not cancelled")
                            assert.equal('True', 'False')
                        }
                    });

                });

            });

 	});
}
	CancelOrderTC();
