const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const orderData = require('../../config/properties').orderData
const userData = require('../../config/properties').user

// sample Market msgs
// Order request: {"orders":[{"coId":"MrktDAYSell","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"DAY"}]}
//Order response: {"coId":"MrktIOCSell","type":"Market","timeInForce":"IOC","side":"Sell","currency":"EUR","symbol":"EUR/USD","size":100000,"org":"XCN1046","execFlags":["OkToCross"],"averagePrice":1.19352,"cumQty":100000,"execId":"FXI9081553819",
//"lastPrice":1.19352,"lastQty":100000,"orderId":"4743693784","valueDate":"20201103","tradeDate":"20201101","settlCurrAmt":119352,"settlCurrency":"USD","executionType":"TRADE","leavesQty":0,"counterParty":"JPM","action":"place","status":"Filled"}
// Check user has permission to place DAY orders

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

let orderReceived;
let orderPendingNew;
let orderNew;
let orderFilled;
let orderrejected;
let order ;

let MarketOrderTC = function () {
	describe("Market Order - ", function () {


		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});

        reqId = Math.floor(Math.random() * 1000)


        //let execInstString = "OkToCross"
        let execInstString = "" // change based on PLT-4178, OKToCross is the default behaviour so it is no longer part of execInst
        let execInst = '["OkToCross"]';


		describe("Market Sell IOC Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

			before(function (done) {
                coId = "MrktIOCSell"+parseInt(reqId)
                console.log("coId = " + coId)
				console.log('*************************** Market Sell IOC ************************** ' + new Date());
            	order = '{"orders":[{"coId":"'  + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                   console.log("order  : " + JSON.stringify(res.orderResponses));
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Sell IOC Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency); //PLT-4178
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True', 'False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe(" Market Sell GTC Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

        	before(function (done) {
				console.log('*************************** Market Sell GTC ************************** ' + new Date());
                coId = "MrktGTCSell"+parseInt(reqId)
                console.log("coId = " + coId)
            	order = '{"orders":[{"coId":"'+ coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }

				}
			});

			it("Market Sell GTC Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.GTC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    // assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True', 'False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Sell FOK Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

        	before(function (done) {
				console.log('*************************** Market Sell GTC ************************** ' + new Date());
                coId = "MrktFOKSell"+parseInt(reqId)
                console.log("coId = " + coId)
            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.FOK + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Sell FOK Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.FOK, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                   // assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Sell GTT Order Test ", function () {
//				connection.send('{"orders":[{"coId":"MrktGTTBuy","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTT"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "";
            let execInst = '["OkToCross"]';

        	before(function (done) {
				console.log('*************************** Market Sell GTT ************************** ' + new Date());
                coId = "MrktGTTSell"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId +'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTT + '","expiryTime":"' + orderData.ExpiryTime + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Sell GTT Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.GTT, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.expiryTime);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                  //  assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Sell DAY Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "";
            let execInst = '["OkToCross"]';

        	before(function (done) {
				console.log('*************************** Market Sell DAY ************************** ' + new Date());
                coId = "MrktDAYSell"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Sell DAY Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                 //   assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Buy IOC ExecFlag OKToCross BP Order Test ", function () {
//			connection.send('{"orders":[{"coId":"MrktIOCBuyExecFlagOkToCrossBP","currency":"EUR", "size":"100000", "type":"Market", "side":"Buy", "symbol":"EUR/USD", "execFlags" : ["OkToCross", "BestPrice"], "timeInForce":"IOC"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "";
            let execInst = '["OkToCross", "BestPrice"]';

        	before(function (done) {
				console.log('*************************** Mrkt IOC Buy ExecFlag - OkToCross BP ************************** ' + new Date());
                coId = "MrktIOCBuyExecFlagOkToCrossBP"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy IOC ExecFlag OKToCross BP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                 //   assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Buy GTC ExecFlag OKToCross PP Order Test ", function () {
//			connection.send('{"orders":[{"coId":"MrktGTCBuyExecFlagOkToCrossPP","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross", "ProviderPriority"], "timeInForce":"GTC"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "";
            let execInst = '["OkToCross", "ProviderPriority"]';

        	before(function (done) {
				console.log('*************************** Mrkt Buy GTC Buy ExecFlag - OkToCross PP ************************** ' + new Date());
                coId = "MrktGTCBuyExecFlagOkToCrossPP"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy GTC ExecFlag OKToCross PP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.GTC, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                 //   assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Buy FOK ExecFlag OKToCross TP Order Test ", function () {
//				connection.send('{"orders":[{"coId":"MrktFOKBuyExecFlagOkToCrossTP","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross", "TimePriority"], "timeInForce":"FOK"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "";
            let execInst = '["OkToCross", "TimePriority"]';

        	before(function (done) {
				console.log('*************************** Mrkt FOK Buy ExecFlag - OkToCross TP ************************** ' + new Date());
                coId = "MrktFOKBuyExecFlagOkToCrossTP"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.FOK + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy FOK ExecFlag OKToCross TP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.FOK, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                 //   assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market DAY Buy ExecFlag OKToCross SP Order Test ", function () {
            // connection.send('{"orders":[{"coId":"MrktDAYBuyExecFlagOkToCrossSP","currency":"EUR", "size":"100000", "type":"Market", "side":"Buy", "symbol":"EUR/USD", "execFlags" : ["OkToCross", "SizePriority"], "timeInForce":"DAY"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "";
            let execInst = '["OkToCross", "SizePriority"]';

        	before(function (done) {
				console.log('*************************** Mrkt DAY Buy ExecFlag - OkToCross SP ************************** ' + new Date());
                coId = "MrktDAYBuyExecFlagOkToCrossSP"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId +'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy DAY ExecFlag OKToCross SP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                //    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('true','faslse');
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market DAY Buy Term ExecFlag OKToCross Order Test ", function () {
        //connection.send('{"orders":[{"coId":"MrktGTCTermCcy","currency":"USD", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')            let execInstString = "OkToCross";
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

            let execInst = '["OkToCross"]';

        	before(function (done) {
				console.log('*************************** Mrkt DAY Buy Term ExecFlag - OkToCross ************************** ' + new Date());
                coId = "MrktDAYBuyTermExecFlagOkToCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.termCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy DAY Term ExecFlag OKToCross Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.termCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                //    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('true','false')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

        describe("Market DAY Buy V4 NO_OKToCross Order Test ", function () {
        // Order request gets rejected since OKToCross is a mandatory tag
        // As per the design, OKToCross is a mandatory tag and all orders first goes to EMS, only unmatched orders go to ME
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInst = '["BestPrice"]';

        	before(function (done) {
				console.log('*************************** Market DAY Sell V4 ExecFlag - OkToCross ************************** ' + new Date());
                coId = "MrktDAYBuyExecFlagNoCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"o1rders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
                    } else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()

					}
				}
			});

			it("Market DAY Buy V4 NO_OKToCross Order Test", function () {
				console.log('Order response: ' + JSON.stringify(InvalidOrderRequest));
                if(InvalidOrderRequest) {
                    console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
                    assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                    assert.equal('1', InvalidOrderRequest[0].errorCode);
                } else {
                    console.log("unexpected response");
                    assert.equal("true","false");
                }
			});
		});


        describe("Market DAY Buy Term V4 NO_OKToCross Order Test ", function () {
        // OKToCross is a mandatory tag, it doesnt matter whether it is Base or Term
        //connection.send('{"orders":[{"coId":"MrktGTCTermCcy","currency":"USD", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')            let execInstString = "OkToCross";

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInst = '["BestPrice"]';
            let execInstString = "";

        	before(function (done) {
				console.log('*************************** Market DAY Sell Term V4 ExecFlag - OkToCross ************************** ' + new Date());
                coId = "MrktDAYBuyTermExecFlagNoCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.termCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
					}
				}
			});

			it("Market DAY Buy Term V4 NO_OKToCross Order Test", function () {
				if (orderFilled) {
    				console.log('orderFilled response: ' + JSON.stringify(orderFilled));
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.termCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                  //  assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else if (orderRejected) {
    				console.log('orderRejected response: ' + JSON.stringify(orderRejected));
                    assert.equal(coId, orderRejected.coId);
                    assert.equal(orderData.MARKET, orderRejected.type);
                    assert.equal(orderData.DAY, orderRejected.timeInForce);
                    assert.equal(orderData.BUY, orderRejected.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderRejected.currency);
                    assert.equal(orderData.symbol_EURUSD, orderRejected.symbol);
                    assert.equal(orderData.size, orderRejected.size);
                    assert.equal(userData.orgname, orderRejected.org);
                    assert.equal(execInstString, orderRejected.execFlags);
                    assert.equal('0', orderRejected.averagePrice);
                    assert.equal('0', orderRejected.cumQty);
                    assert.equal('0', orderRejected.execId);
                    assert.equal('0', orderRejected.lastPrice);
                    assert.equal('0', orderRejected.lastQty);
                    assert.equal('0', orderRejected.orderId);
                    assert.equal(orderData.REJECTED, orderRejected.executionType);
                    assert.equal("0", orderRejected.leavesQty);
                    assert.equal(orderData.ACTION, orderRejected.action);
                    assert.equal(orderData.REJECTED, orderRejected.status);
                    assert.equal("RequestValidationError.ExecInstNotSupported", orderRejected.reason);
                } else {
                    assert.equal('True','False');
                    console.log ("order status - Order didnt get filled")
                }
			});
		});


	});
};
        // Negative scenarios
let NegativeMarketOrderTC = function () {
	describe("Negative Market Order TCs - ", function () {
		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});

        let execInstString = ""
        let execInst = '["OkToCross"]';
        let reqId = Math.floor(Math.random() * 1000);

		describe(" Negative Scenario - Null Currency ", function () {
        // This is not working as expected
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Null Currency ************************** ' + new Date());
                coId = "NullCurrency"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
                            done()
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" Negative Scenario - Null Currency ", function () {
				console.log('Order response : ' + JSON.stringify(orderData.RECEIVED));
//				assert.equal('RequestValidationError.InvalidDealtCcy', orderFailed.reason);
                assert.equal(orderData.RECEIVED, orderReceived.status);
			});
		});

		describe(" Negative Scenario - Blank coID ", function () {
        // This is not working as expected, no response comes from UIG
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Blank coID ************************** ' + new Date());
				let order = '{"orders":[{"coId":"","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" Negative Scenario - Blank coID ", function () {
				console.log('Order response : ' + JSON.stringify(orderFailed.reason));
                assert.equal(orderData.FAILED, orderFailed.status);
                assert.equal("Invalid coId, client order id can not be null or blank", orderFailed.reason);
			});
		});

        describe("Market DAY Sell NO ExecInst Order Test ", function () {
        // Order request gets rejected since OKToCross is a mandatory tag
        	before(function (done) {
				console.log('*************************** Market DAY Sell NO ExecInst ************************** ' + new Date());
                coId = "MrktDAYBuyExecFlagNoCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
                    } else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("orderFilled =" + JSON.stringify(InvalidOrderRequest))
                        //done()

					}
				}
			});

			it("Market DAY Sell NO ExecInst Order Test", function () {
				if (orderReceived) {
				console.log('Order response : ' + JSON.stringify(orderData.RECEIVED));
                assert.equal(orderData.RECEIVED, orderReceived.status);
                }
			});
		});

		describe(" Negative Scenario - Invalid Currency ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Currency ************************** ' + new Date());
                coId = "InvalidCurrency"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"XXX", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Invalid Currency ", function () {
				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidDealtCcy', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

		describe(" Negative Scenario - Invalid CP ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid CP ************************** ' + new Date());
                coId = "MrktGTCInvalidCP"+parseInt(reqId)
                console.log("coId = " + coId)

				connection.send('{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"XXX/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')
				console.log('{"order request ":[{"coId":"MrktGTCInvalidCP1","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"XXX/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Invalid CP ", function () {
				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidCurrencyPair', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});


		describe(" Negative Scenario - Incorrect Case Symbol ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Symbol ************************** ' + new Date());
                coId = "IncorrectCaseSymbol"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"MARKET", "side":"Sell", "symbol":"eur/usd", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Incorrect Case Symbol ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - Invalid Amount ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Amount ************************** ' + new Date());
                coId = "InvalidAmount"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"InvalidAmount","currency":"EUR", "size":"x00000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Invalid Amount ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - Null Amount ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Null Amount ************************** ' + new Date());
                coId = "NullAmount"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":" ", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Null Amount ", function () {
				console.log('Order response : ' + JSON.stringify(orderFailed.reason));
                assert.equal(orderData.FAILED, orderFailed.status);
            });
		});


		describe(" Negative Scenario - Invalid TIF ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid TIF ************************** ' + new Date());
                coId = "MrktGTCInvalidTIF"+parseInt(reqId)
                console.log("coId = " + coId)

				connection.send('{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"TIF"}]}')
				console.log('{"orders":[{"coId":"MrktGTCInvalidTIF","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"TIF"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
 						if (orderResponse.status === 'RECEIVED') {
 							orderReceived = orderResponse;
                             console.log("order Received : " + JSON.stringify(orderReceived));
 						} else if (orderResponse.status === 'PENDING_NEW') {
 							orderPendingNew = orderResponse;
                             console.log("order Pending New : " + JSON.stringify(orderPendingNew));
 						} else if (orderResponse.status === 'NEW') {
 							orderNew = orderResponse;
                             console.log("order New : " + JSON.stringify(orderNew));
 						} else if (orderResponse.status === 'FILLED') {
 							orderFilled = orderResponse;
 							console.log("orderFilled =" + JSON.stringify(orderFilled))
 							done()
 						} else if (orderResponse.status === 'REJECTED') {
 							orderRejected = orderResponse;
 							console.log("orderRejected =" + JSON.stringify(orderRejected))
 							done()
 						}
 					} else if (res.errors) {
 						    InvalidOrderRequest = res.errors;
 						    console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
 					}
				}
			});

			it(" Negative Scenario - Invalid TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - Incorrect Case TIF ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case TIF ************************** ' + new Date());
                coId = "IncorrectCaseTIF"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Market", "side":"SELL", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"gtc"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Incorrect Case TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - Invalid side ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid side  ************************** ' + new Date());
                coId = "MrktGTCInvalidSide"+parseInt(reqId)
                console.log("coId = " + coId)

				connection.send('{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Market", "side":"Sebi", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')
				console.log('{"orders":[{"coId":"MrktGTCInvalidSide","currency":"EUR", "size":"100000", "type":"Market", "side":"Sebi", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
 						if (orderResponse.status === 'RECEIVED') {
 							orderReceived = orderResponse;
                             console.log("order Received : " + JSON.stringify(orderReceived));
 						} else if (orderResponse.status === 'PENDING_NEW') {
 							orderPendingNew = orderResponse;
                             console.log("order Pending New : " + JSON.stringify(orderPendingNew));
 						} else if (orderResponse.status === 'NEW') {
 							orderNew = orderResponse;
                             console.log("order New : " + JSON.stringify(orderNew));
 						} else if (orderResponse.status === 'FILLED') {
 							orderFilled = orderResponse;
 							console.log("orderFilled =" + JSON.stringify(orderFilled))
 							done()
 						} else if (orderResponse.status === 'REJECTED') {
 							orderRejected = orderResponse;
 							console.log("orderRejected =" + JSON.stringify(orderRejected))
 							done()
 						}
 					} else if (res.errors) {
 						    InvalidOrderRequest = res.errors;
 						    console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
 					}
				}
			});

			it(" Negative Scenario - Invalid TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - Incorrect Case Side ", function () {
			let res;
			let orderResponses;
            reqId = Math.floor(Math.random() * 1000)

			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Side ************************** ' + new Date());
                coId = "IncorrectCaseSideNeg"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Market", "side":"SELL", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Incorrect Case Side ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - NoCross ", function () {
        // Not able to simulate the scenario where order doesnt get matched in EMS and gets forwarded to ME.
        // When OKToCross is not specified, order request doesnt go thru
			let res;
			let execInst = '["BestPrice"]';
            reqId = Math.floor(Math.random() * 1000)

			before(function (done) {
				console.log('*************************** Negative Scenario - NoCross ************************** ' + new Date());
                coId = "MrktDAYBuyExecFlagNoCrossNeg"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
				}
			});

			it("Negative Scenario - NoCross ", function () {
				console.log('Order response : ' + JSON.stringify(orderFilled.status));
				//assert.equal('RequestValidationError.ExecInstNotSupported', orderRejected.reason);
				assert.equal('FILLED', orderFilled.status);
                //assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});


		describe(" Negative Scenario - Invalid Trade Type ", function () {
			let res;
			let orderResponses;
            reqId = Math.floor(Math.random() * 1000)

			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Trade Type ************************** ' + new Date());
                coId = "InvalidTradeTypeNeg"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"MarX", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Invalid Trade Type ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - Incorrect Case Trade Type ", function () {
			let res;
			let orderResponses;
            reqId = Math.floor(Math.random() * 1000)

			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Trade Type ************************** ' + new Date());
                coId = "IncorrectCaseTradeType"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+ coId + '","currency":"EUR", "size":"100000", "type":"MARKET", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Incorrect Case Trade Type ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});

	});
};

MarketOrderTC();
NegativeMarketOrderTC();

