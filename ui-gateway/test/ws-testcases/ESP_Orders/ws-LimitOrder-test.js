 const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const orderData = require('../../config/properties').orderData
const userData = require('../../config/properties').user

// sample Limit msgs
// Limit order request: {"orders":[{"coId":"LimitDAYSell","currency":"USD", "size":"100000", "type":"Limit","price":"1.18", "side":"Sell", "symbol":"USD/JPY", "timeInForce":"GTC"}]}
// order response ={"coId":"LmtIOCSell","type":"Limit","timeInForce":"IOC","side":"Sell","currency":"EUR","symbol":"EUR/USD","size":100000,
//"org":"XCN1046","price":1.1,"execFlags":["OkToCross"],"averagePrice":1.19352,"cumQty":100000,"execId":"FXI9081760782","lastPrice":1.19352,
//"lastQty":100000,"orderId":"4743776745","valueDate":"20201106","tradeDate":"20201104","settlCurrAmt":119352,"settlCurrency":"USD",
//"executionType":"TRADE","leavesQty":0,"counterParty":"JPM","action":"place","status":"Filled"}

// expiryTime should be specified incase of GTT orders, otherwise EMS treats it as GTC. UIG cannot reject the request when expiry time is not specified for GTT orders.

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

let LimitOrderTC = function () {
	describe("Limit Order - ", function () {
		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});

        reqId = Math.floor(Math.random() * 1000)

		describe("Limit Sell IOC Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
            let execInstString = ""
            let execInst = '[]';

			before(function (done) {
				console.log('*************************** Limit Sell IOC ************************** ' + new Date());
                coId = "LmtIOCSell"+parseInt(reqId)
                console.log("coId = " + coId)

               	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitSellPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});


			it("Limit Sell IOC Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.averagePrice);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency); // has been removed PLT-4178
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe(" Limit Sell GTC Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
            let execInstString = ""
            let execInst = '[]';

        	before(function (done) {
				console.log('*************************** Limit Sell GTC ************************** ' + new Date());
                coId = "LmtGTCSell"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitSellPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					}
				}
			});

			it("Limit Sell GTC Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.GTC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency); // PLT-4178
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Limit Sell FOK Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
            let execInstString = ""
            let execInst = '[]';

        	before(function (done) {
				console.log('*************************** Limit Sell GTC ************************** ' + new Date());
                coId = "LmtFOKSell"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitSellPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.FOK + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					}
				}
			});

			it("Limit Sell FOK Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.FOK, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});

		});

		describe("Limit Sell DAY Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
            let execInstString = "";
            let execInst = '[]';

        	before(function (done) {
				console.log('*************************** Limit Sell DAY ************************** ' + new Date());
                coId = "LmtDAYSell"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitSellPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					}
				}
			});

			it("Limit Sell DAY Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});


		});

		describe("Limit Sell GTT Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            let execInstString = "";
            let execInst = '[]';

        	before(function (done) {
				console.log('*************************** Limit Sell GTT ************************** ' + new Date());
                coId = "LmtGTTSell"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitSellPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTT + '","expiryTime":"' + orderData.ExpiryTime + '"}]}'
            	console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					}
				}
			});

			it("Limit Sell GTT Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.GTT, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.expiryTime);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Limit Buy IOC ExecFlag OKToCross BP Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
            //let execInstString = "BestPrice,OkToCross"; // Changed after the api cleanup
            let execInstString = "";
            let execInst = '["BestPrice"]';

        	before(function (done) {
				console.log('*************************** Lmt IOC Buy ExecFlag - OkToCross BP ************************** ' + new Date());
                coId = "LmtIOCBuyExecFlagOkToCrossBP"+parseInt(reqId)
                console.log("coId = " + coId)

               	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitBuyPrice + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					}
				}
			});

			it("Limit Buy IOC ExecFlag OKToCross BP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Limit Buy GTC ExecFlag OKToCross PP Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            //let execInstString = "OkToCross,ProviderPriority";// changed after api clean up
            let execInstString = "";
            let execInst = '["OkToCross", "ProviderPriority"]';

        	before(function (done) {
				console.log('*************************** Lmt Buy GTC Buy ExecFlag - OkToCross PP ************************** ' + new Date());
                coId = "LmtGTCBuyExecFlagOkToCrossPP"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitBuyPrice + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					}
				}
			});

			it("Limit Buy GTC ExecFlag OKToCross PP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.GTC, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Limit Buy FOK ExecFlag OKToCross TP Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            // let execInstString = "OkToCross,TimePriority"; // changed after api cleanup
            let execInstString = "";
            let execInst = '["OkToCross", "TimePriority"]';

        	before(function (done) {
				console.log('*************************** Lmt FOK Buy ExecFlag - OkToCross TP ************************** ' + new Date());
                coId = "LmtFOKBuyExecFlagOkToCrossTP"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitBuyPrice + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.FOK + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					}
				}
			});

			it("Limit Buy FOK ExecFlag OKToCross TP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.FOK, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Limit GTT Buy ExecFlag OKToCross SP Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            // let execInstString = "OkToCross,SizePriority"; // changed after api cleanup
            let execInstString = "";
            let execInst = '["OkToCross", "SizePriority"]';

        	before(function (done) {
				console.log('*************************** Lmt GTT Buy ExecFlag - OkToCross SP ************************** ' + new Date());
                coId = "LmtGTTBuyExecFlagOkToCrossSP"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitBuyPrice + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTT  + '","expiryTime":"' + orderData.ExpiryTime + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					}
				}
			});

			it("Limit Buy GTT ExecFlag OKToCross SP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.GTT, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.expiryTime);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Limit DAY Buy Term ExecFlag OKToCross Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
            let execInst = '[]';
            let execInstString = ""

        	before(function (done) {
				console.log('*************************** Lmt DAY Buy Term ExecFlag - OkToCross ************************** ' + new Date());
                coId = "LmtDAYBuyTermExecFlagOkToCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.termCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitSellPrice + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
    				}	else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
                }
			});

			it("Limit Buy DAY Term ExecFlag OKToCross Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

        describe("Limit DAY Sell Term V4 Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            let execInst = '["BestPrice"]';
            // let execInstString = "BestPrice" // changed after api clean up
            let execInstString = ""

        	before(function (done) {
				console.log('*************************** Lmt DAY Sell Term V4 ExecFlag - OkToCross ************************** ' + new Date());
                coId = "LmtDAYBuyTermExecFlagNoCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.termCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitBuyPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
					}
				}
			});

			it("Lmt DAY Sell Term V4 ExecFlag OKToCross Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.LIMIT, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                   // assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency); // has been removed PLT-4178
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else if (orderRejected) {
    				console.log('orderRejected response: ' + JSON.stringify(orderRejected));
                    assert.equal('LmtDAYBuyTermExecFlagNoCross', orderRejected.coId);
                    assert.equal(orderData.LIMIT, orderRejected.type);
                    assert.equal(orderData.DAY, orderRejected.timeInForce);
                    assert.equal(orderData.BUY, orderRejected.side);
                    assert.equal(orderData.symbol_EURUSD, orderRejected.symbol);
                    assert.equal(orderData.size, orderRejected.size);
                    assert.equal(userData.orgname, orderRejected.org);
                    assert.equal(execInstString, orderRejected.execFlags);
                    assert.equal('0', orderRejected.averagePrice);
                    assert.equal('0', orderRejected.cumQty);
                    assert.equal('0', orderRejected.execId);
                    assert.equal('0', orderRejected.lastPrice);
                    assert.equal('0', orderRejected.lastQty);
                    assert.equal('0', orderRejected.orderId);
                    assert.equal(orderData.REJECTED, orderRejected.executionType);
                    assert.equal("0", orderRejected.leavesQty);
                    assert.equal(orderData.ACTION, orderRejected.action);
                    assert.equal(orderData.REJECTED, orderRejected.status);
                    assert.equal("Request.Validation.Term.Currency.Directed.Order.Not.Allowed", orderRejected.reason);
                } else {
                    assert.equal('True','False');
                    console.log ("order status - Order didnt get filled")
                }
            });
		});

	});
}

// Negative scenarios
let NegativeLimitOrderTC = function () {
 	describe("Negative Limit Order TCs - ", function () {
 		before(function (done) {
 			wsconnect(done);
 		});

 		after(function () {
 			connection.close()
 		});
        reqId = Math.floor(Math.random() * 100)
        // This is not working as expected, there is not response for the submitted order
        // UIG is receiving the order rejection as InvalidDealtCcy, but client is not receiving it.
		describe(" Negative Scenario - Null Currency ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Null Currency ************************** ' + new Date());
                coId = "NullCurrency"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"", "size":"100000", "type":"Limit", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
                            done()
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Null Currency ", function () {
				console.log('Order response : ' + JSON.stringify(orderReceived.status));
                assert.equal(orderData.RECEIVED, orderReceived.status);
			});
		});

        // This is not working as expected
		describe(" Negative Scenario - Blank coID ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Blank coID ************************** ' + new Date());
				let order = '{"orders":[{"coId":"","currency":"EUR", "size":"100000", "type":"Limit", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Blank coID ", function () {
				console.log('Order response : ' + JSON.stringify(orderFailed.reason));
                assert.equal(orderData.FAILED, orderFailed.status);
                assert.equal('Invalid coId, client order id can not be null or blank', orderFailed.reason);
			});
		});

		describe(" Negative Scenario - Invalid Currency ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderFailed;
            let orderRejected;
            let order ;

			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Currency ************************** ' + new Date());
                coId = "InvalidCurrency"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"XXX", "size":"100000", "type":"Limit", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Invalid Currency ", function () {
//                console.log('Order response : ' + JSON.stringify(orderFailed.reason));
//                assert.equal(orderData.FAILED, orderFailed.status);

   				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidDealtCcy', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

		describe(" Negative Scenario - Invalid CP ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid CP ************************** ' + new Date());
                coId = "LmtGTCInvalidCP"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Limit", "side":"Sell", "symbol":"XXX/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Invalid CP ", function () {
				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidCurrencyPair', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

		describe(" Negative Scenario - Incorrect Case Symbol ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Symbol ************************** ' + new Date());
                coId = "IncorrectCaseSymbol"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Limit", "price":"1.1", "side":"Sell", "symbol":"eur/usd", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Incorrect Case Symbol ", function () {
				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidCurrencyPair', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

		describe(" Negative Scenario - Invalid Amount ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Amount ************************** ' + new Date());
                coId = "InvalidAmount"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"x00000", "type":"Limit", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Invalid Amount ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" Negative Scenario - Null Amount ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Null Amount ************************** ' + new Date());
                coId = "NullAmount"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":" ", "type":"Limit", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Null Amount ", function () {
				console.log('Order response : ' + JSON.stringify(orderFailed.reason));
                assert.equal(orderData.FAILED, orderFailed.status);
            });
		});

		describe(" Negative Scenario - Invalid TIF ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid TIF ************************** ' + new Date());
                coId = "LmtGTCInvalidTIF"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+ coId + '","currency":"EUR", "size":"100000", "type":"Limit", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"TIF"}]}'
				connection.send(order)
				console.log("order request : " + order)

				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
 						if (orderResponse.status === 'RECEIVED') {
 							orderReceived = orderResponse;
                             console.log("order Received : " + JSON.stringify(orderReceived));
 						} else if (orderResponse.status === 'PENDING_NEW') {
 							orderPendingNew = orderResponse;
                             console.log("order Pending New : " + JSON.stringify(orderPendingNew));
 						} else if (orderResponse.status === 'NEW') {
 							orderNew = orderResponse;
                             console.log("order New : " + JSON.stringify(orderNew));
 						} else if (orderResponse.status === 'FILLED') {
 							orderFilled = orderResponse;
 							console.log("orderFilled =" + JSON.stringify(orderFilled))
 							done()
 						} else if (orderResponse.status === 'REJECTED') {
 							orderRejected = orderResponse;
 							console.log("orderRejected =" + JSON.stringify(orderRejected))
 							done()
 						}
 					} else if (res.errors) {
 						    InvalidOrderRequest = res.errors;
 						    console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
 					}
				}
			});

			it("LimitOrder - Negative Scenario - Invalid TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" Negative Scenario - Incorrect Case TIF ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case TIF ************************** ' + new Date());
                coId = "IncorrectCaseTIF"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Limit", "price":"1.1", "side":"SELL", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"gtc"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Incorrect Case TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" Negative Scenario - Invalid side ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid side  ************************** ' + new Date());
                coId = "InvalidSide"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Limit", "side":"Sebi", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order)
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
 						if (orderResponse.status === 'RECEIVED') {
 							orderReceived = orderResponse;
                             console.log("order Received : " + JSON.stringify(orderReceived));
 						} else if (orderResponse.status === 'PENDING_NEW') {
 							orderPendingNew = orderResponse;
                             console.log("order Pending New : " + JSON.stringify(orderPendingNew));
 						} else if (orderResponse.status === 'NEW') {
 							orderNew = orderResponse;
                             console.log("order New : " + JSON.stringify(orderNew));
 						} else if (orderResponse.status === 'FILLED') {
 							orderFilled = orderResponse;
 							console.log("orderFilled =" + JSON.stringify(orderFilled))
 							done()
 						} else if (orderResponse.status === 'REJECTED') {
 							orderRejected = orderResponse;
 							console.log("orderRejected =" + JSON.stringify(orderRejected))
 							done()
 						}
 					} else if (res.errors) {
 						    InvalidOrderRequest = res.errors;
 						    console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
 					}
				}
			});

			it("LimitOrder - Negative Scenario - Invalid TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" Negative Scenario - Incorrect Case Side ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Side ************************** ' + new Date());
                coId = "IncorrectCaseSide"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"Limit", "price":"1.1", "side":"SELL", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Incorrect Case Side ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" Negative Scenario - NoCross ", function () {
        // the scenario where order doesnt get matched in EMS and gets forwarded to ME. - Not correct
        // OKToCross is now the default ExecFlag in UIG. We will have NoCross option in future if order should not get matched in EMS

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let execInst = '["BestPrice"]';
			let execInstString = "BestPrice";

			before(function (done) {
				console.log('*************************** Negative Scenario - NoCross ************************** ' + new Date());
                coId = "LmtDAYBuyExecFlagNoCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"' + coId + '","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.LIMIT + '", "price":"' + orderData.LimitBuyPrice +  '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					i=1
 					if (res.orderResponses && i < 30 ) {
 						let orderResponse = res.orderResponses[0];
 						i++
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW' ) {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    } else {
                        console.log("Order is still in NEW state")
                        done()
                    }
				}
			});

			it("LimitOrder - Negative Scenario - NoCross ", function () {
                    console.log("Order is still in NEW state");
                    assert.equal(orderData.FILLED, orderFilled.status);
			});
		});

		describe(" Negative Scenario - Invalid Trade Type ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Trade Type ************************** ' + new Date());
                coId = "InvalidTradeType"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"MarX", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Invalid Trade Type ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});

		describe(" Negative Scenario - Incorrect Case Trade Type ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
			let res;
			let orderResponses;

			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Trade Type ************************** ' + new Date());
                coId = "IncorrectCaseTradeType"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"' + coId + '","currency":"EUR", "size":"100000", "type":"LIMIT", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("LimitOrder - Negative Scenario - Incorrect Case Trade Type ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});

		});

	});
}

LimitOrderTC();
NegativeLimitOrderTC();