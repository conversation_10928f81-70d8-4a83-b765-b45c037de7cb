const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const orderData = require('../../config/properties').orderData
const userData = require('../../config/properties').user


let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

let orderReceived;
let orderPendingNew;
let orderNew;
let orderFilled;
let orderrejected;
let order;

let OrderNotesTC = function () {
	describe("Market Order - ", function () {

		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});


        let execInstString = "OkToCross"
        let execInst = '["OkToCross"]';
        let reqId = Math.floor(Math.random() * 1000)

		describe("Market Sell IOC Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

			before(function (done) {
				console.log('*************************** Market Sell IOC ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktIOCSell'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                   console.log("order  : " + JSON.stringify(res.orderResponses));
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Sell IOC Order Test", function () {
				if (orderFilled) {
                    assert.equal('MrktIOCSell'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    //assert.equal([], orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True', 'False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe(" Market Sell GTC Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

        	before(function (done) {
				console.log('*************************** Market Sell GTC ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktGTCSell'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }

				}
			});

			it("Market Sell GTC Order Test", function () {
				if (orderFilled) {
                    assert.equal('MrktGTCSell'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.GTC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True', 'False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Sell FOK Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

        	before(function (done) {
				console.log('*************************** Market Sell GTC ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktFOKSell'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.FOK + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Sell FOK Order Test", function () {
				if (orderFilled) {
                    assert.equal('MrktFOKSell'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.FOK, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    //assert.equal([], orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                   // assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Sell GTT Order Test ", function () {
//				connection.send('{"orders":[{"coId":"MrktGTTBuy","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTT"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "OkToCross";
            let execInst = '["OkToCross"]';

        	before(function (done) {
				console.log('*************************** Market Sell GTT ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktGTTSell'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTT + '","expiryTime":"' + orderData.ExpiryTime + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Sell GTT Order Test", function () {
				if (orderFilled) {
                    assert.equal('MrktGTTSell'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.GTT, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.expiryTime);
                    //assert.equal([], orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Sell DAY Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "OkToCross";
            let execInst = '["OkToCross"]';

        	before(function (done) {
				console.log('*************************** Market Sell DAY ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktDAYSell'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Sell DAY Order Test", function () {
				if (orderFilled) {
                    assert.equal('MrktDAYSell'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInst, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Buy IOC ExecFlag OKToCross BP Order Test ", function () {
//			connection.send('{"orders":[{"coId":"MrktIOCBuyExecFlagOkToCrossBP","currency":"EUR", "size":"100000", "type":"Market", "side":"Buy", "symbol":"EUR/USD", "execFlags" : ["OkToCross", "BestPrice"], "timeInForce":"IOC"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "OkToCross,BestPrice";
            let execInst = '["OkToCross", "BestPrice"]';

        	before(function (done) {
				console.log('*************************** Mrkt IOC Buy ExecFlag - OkToCross BP ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktIOCBuyExecFlagOkToCrossBP'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.IOC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy IOC ExecFlag OKToCross BP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal('MrktIOCBuyExecFlagOkToCrossBP'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                   // assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Buy GTC ExecFlag OKToCross PP Order Test ", function () {
//			connection.send('{"orders":[{"coId":"MrktGTCBuyExecFlagOkToCrossPP","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross", "ProviderPriority"], "timeInForce":"GTC"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "OkToCross,ProviderPriority";
            let execInst = '["OkToCross", "ProviderPriority"]';

        	before(function (done) {
				console.log('*************************** Mrkt Buy GTC Buy ExecFlag - OkToCross PP ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktGTCBuyExecFlagOkToCrossPP'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy GTC ExecFlag OKToCross PP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal('MrktGTCBuyExecFlagOkToCrossPP'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.GTC, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInst, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market Buy FOK ExecFlag OKToCross TP Order Test ", function () {
//				connection.send('{"orders":[{"coId":"MrktFOKBuyExecFlagOkToCrossTP","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross", "TimePriority"], "timeInForce":"FOK"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "OkToCross,TimePriority";
            let execInst = '["OkToCross", "TimePriority"]';

        	before(function (done) {
				console.log('*************************** Mrkt FOK Buy ExecFlag - OkToCross TP ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktFOKBuyExecFlagOkToCrossTP'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.FOK + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy FOK ExecFlag OKToCross TP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal('MrktFOKBuyExecFlagOkToCrossTP'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.FOK, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInst, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('True','False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market DAY Buy ExecFlag OKToCross SP Order Test ", function () {
            // connection.send('{"orders":[{"coId":"MrktDAYBuyExecFlagOkToCrossSP","currency":"EUR", "size":"100000", "type":"Market", "side":"Buy", "symbol":"EUR/USD", "execFlags" : ["OkToCross", "SizePriority"], "timeInForce":"DAY"}]}')
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInstString = "OkToCross,SizePriority";
            let execInst = '["OkToCross", "SizePriority"]';

        	before(function (done) {
				console.log('*************************** Mrkt DAY Buy ExecFlag - OkToCross SP ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktDAYBuyExecFlagOkToCrossSP'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy DAY ExecFlag OKToCross SP Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal('MrktDAYBuyExecFlagOkToCrossSP'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInst, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('true','faslse');
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe("Market DAY Buy Term ExecFlag OKToCross Order Test ", function () {
        //connection.send('{"orders":[{"coId":"MrktGTCTermCcy","currency":"USD", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')            let execInstString = "OkToCross";
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;

            let execInst = '["OkToCross"]';

        	before(function (done) {
				console.log('*************************** Mrkt DAY Buy Term ExecFlag - OkToCross ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktDAYBuyTermExecFlagOkToCross'+parseInt(reqId)+'","currency":"' + orderData.termCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Market Buy DAY Term ExecFlag OKToCross Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal('MrktDAYBuyTermExecFlagOkToCross'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.termCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInst, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                    ///assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    assert.equal('true','false')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

        describe("Market DAY Buy V4 NO_OKToCross Order Test ", function () {
        // Order request gets rejected since OKToCross is a mandatory tag
        // As per the design, OKToCross is a mandatory tag and all orders first goes to EMS, only unmatched orders go to ME
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInst = '["BestPrice"]';

        	before(function (done) {
				console.log('*************************** Market DAY Sell V4 ExecFlag - OkToCross ************************** ' + new Date());
            	order = '{"o1rders":[{"coId":"MrktDAYBuyExecFlagNoCross'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
                    } else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()

					}
				}
			});

			it("Market DAY Buy V4 NO_OKToCross Order Test", function () {
				console.log('Order response: ' + JSON.stringify(InvalidOrderRequest));
                if(InvalidOrderRequest) {
                    console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
                    assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                    assert.equal('1', InvalidOrderRequest[0].errorCode);
                } else {
                    console.log("unexpected response");
                    assert.equal("true","false");
                }
			});
		});


        describe("Market DAY Buy Term V4 NO_OKToCross Order Test ", function () {
        // OKToCross is a mandatory tag, it doesnt matter whether it is Base or Term
        //connection.send('{"orders":[{"coId":"MrktGTCTermCcy","currency":"USD", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')            let execInstString = "OkToCross";

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order ;
            let execInst = '["BestPrice"]';
            let execInstString = "BestPrice";

        	before(function (done) {
				console.log('*************************** Market DAY Sell Term V4 ExecFlag - OkToCross ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktDAYBuyTermExecFlagNoCross'+parseInt(reqId)+'","currency":"' + orderData.termCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
					}
				}
			});

			it("Market DAY Buy Term V4 NO_OKToCross Order Test", function () {
				if (orderFilled) {
    				console.log('orderFilled response: ' + JSON.stringify(orderFilled));
                    assert.equal('MrktDAYBuyTermExecFlagNoCross'+parseInt(reqId), orderFilled.coId);
                    assert.equal(orderData.MARKET, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.termCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInst, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    //assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else if (orderRejected) {
    				console.log('orderRejected response: ' + JSON.stringify(orderRejected));
                    assert.equal('MrktDAYBuyTermExecFlagNoCross', orderRejected.coId);
                    assert.equal(orderData.MARKET, orderRejected.type);
                    assert.equal(orderData.DAY, orderRejected.timeInForce);
                    assert.equal(orderData.BUY, orderRejected.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderRejected.currency);
                    assert.equal(orderData.symbol_EURUSD, orderRejected.symbol);
                    assert.equal(orderData.size, orderRejected.size);
                    assert.equal(userData.orgname, orderRejected.org);
                    assert.equal(execInstString, orderRejected.execFlags);
                    assert.equal('0', orderRejected.averagePrice);
                    assert.equal('0', orderRejected.cumQty);
                    assert.equal('0', orderRejected.execId);
                    assert.equal('0', orderRejected.lastPrice);
                    assert.equal('0', orderRejected.lastQty);
                    assert.equal('0', orderRejected.orderId);
                    assert.equal(orderData.REJECTED, orderRejected.executionType);
                    assert.equal("0", orderRejected.leavesQty);
                    assert.equal(orderData.ACTION, orderRejected.action);
                    assert.equal(orderData.REJECTED, orderRejected.status);
                    assert.equal("RequestValidationError.ExecInstNotSupported", orderRejected.reason);
                } else {
                    assert.equal('True','False');
                    console.log ("order status - Order didnt get filled")
                }
			});
		});


	});
};

let NegativeOrderNotesTC = function () {
	describe("Negative Market Order TCs - ", function () {
		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});

        let execInstString = "OkToCross"
        let execInst = '["OkToCross"]';
        let reqId = Math.floor(Math.random() * 1000)

		describe(" Negative Scenario - Null Currency ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Null Currency ************************** ' + new Date());
				let order = '{"orders":[{"coId":"NullCurrency'+parseInt(reqId)+'","currency":"", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
                            done()
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRehected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" Negative Scenario - Null Currency ", function () {
				console.log('Order response : ' + JSON.stringify(orderReceived.reason));
//				assert.equal('RequestValidationError.InvalidDealtCcy', orderFailed.reason);
                assert.equal(orderData.RECEIVED, orderReceived.status);
			});
		});

		describe(" Negative Scenario - Blank coID ", function () {
        // This is not working as expected, no response comes from UIG
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Blank coID ************************** ' + new Date());
				let order = '{"orders":[{"coId":"","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" Negative Scenario - Blank coID ", function () {
				console.log('Order response : ' + JSON.stringify(orderFailed.reason));
                assert.equal(orderData.FAILED, orderFailed.status);
			});
		});

         describe("Market DAY Sell NO ExecInst Order Test ", function () {
        	before(function (done) {
				console.log('*************************** Market DAY Sell NO ExecInst ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktDAYBuyExecFlagNoCross'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
                            done()
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						}
                    } else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
					}
				}
			});

			it("Market DAY Sell NO ExecInst Order Test", function () {
				console.log('Order response : ' + JSON.stringify(orderReceived.reason));
                assert.equal(orderData.RECEIVED, orderReceived.status);
			});
		});

		describe(" Negative Scenario - Invalid Currency ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Currency ************************** ' + new Date());
				let order = '{"orders":[{"coId":"InvalidCurrency'+parseInt(reqId)+'","currency":"XXX", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" Negative Scenario - Invalid Currency ", function () {
				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidDealtCcy', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

		describe(" Negative Scenario - Invalid CP ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid CP ************************** ' + new Date());
				connection.send('{"orders":[{"coId":"MrktGTCInvalidCP'+parseInt(reqId)+'","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"XXX/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')
				console.log('{"order request ":[{"coId":"MrktGTCInvalidCP1","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"XXX/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" Negative Scenario - Invalid CP ", function () {
				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidCurrencyPair', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

		describe(" Negative Scenario - Incorrect Case Symbol ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Symbol ************************** ' + new Date());
				let order = '{"orders":[{"coId":"IncorrectCaseSymbol'+parseInt(reqId)+'","currency":"EUR", "size":"100000", "type":"MARKET", "side":"Sell", "symbol":"eur/usd", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Incorrect Case Symbol ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


	describe(" Negative Scenario - Invalid Amount ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Amount ************************** ' + new Date());
				let order = '{"orders":[{"coId":"InvalidAmount'+parseInt(reqId)+'","currency":"EUR", "size":"x00000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Invalid Amount ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - Null Amount ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Null Amount ************************** ' + new Date());
				let order = '{"orders":[{"coId":"NullAmount'+parseInt(reqId)+'","currency":"EUR", "size":" ", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" Negative Scenario - Null Amount ", function () {
				console.log('Order response : ' + JSON.stringify(orderFailed.reason));
                assert.equal(orderData.FAILED, orderFailed.status);
            });
		});


		describe(" Negative Scenario - Invalid TIF ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid TIF ************************** ' + new Date());
				connection.send('{"orders":[{"coId":"MrktGTCInvalidTIF'+parseInt(reqId)+'","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"TIF"}]}')
				console.log('{"orders":[{"coId":"MrktGTCInvalidTIF","currency":"EUR", "size":"100000", "type":"Market", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"TIF"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
 						if (orderResponse.status === 'RECEIVED') {
 							orderReceived = orderResponse;
                             console.log("order Received : " + JSON.stringify(orderReceived));
 						} else if (orderResponse.status === 'PENDING_NEW') {
 							orderPendingNew = orderResponse;
                             console.log("order Pending New : " + JSON.stringify(orderPendingNew));
 						} else if (orderResponse.status === 'NEW') {
 							orderNew = orderResponse;
                             console.log("order New : " + JSON.stringify(orderNew));
 						} else if (orderResponse.status === 'FILLED') {
 							orderFilled = orderResponse;
 							console.log("orderFilled =" + JSON.stringify(orderFilled))
 							//done()
 						} else if (orderResponse.status === 'REJECTED') {
 							orderRejected = orderResponse;
 							console.log("orderRejected =" + JSON.stringify(orderRejected))
 							//done()
 						}
 					} else if (res.errors) {
 						    InvalidOrderRequest = res.errors;
 						    console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
 					}
				}
			});

			it(" Negative Scenario - Invalid TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});

		describe(" Negative Scenario - Incorrect Case TIF ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case TIF ************************** ' + new Date());
				let order = '{"orders":[{"coId":"IncorrectCaseTIF'+parseInt(reqId)+'","currency":"EUR", "size":"100000", "type":"Market", "side":"SELL", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"gtc"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Incorrect Case TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - Invalid side ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid side  ************************** ' + new Date());
				connection.send('{"orders":[{"coId":"MrktGTCInvalidSide"'+parseInt(reqId)+'","currency":"EUR", "size":"100000", "type":"Market", "side":"Sebi", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')
				console.log('{"orders":[{"coId":"MrktGTCInvalidSide","currency":"EUR", "size":"100000", "type":"Market", "side":"Sebi", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
 						if (orderResponse.status === 'RECEIVED') {
 							orderReceived = orderResponse;
                             console.log("order Received : " + JSON.stringify(orderReceived));
 						} else if (orderResponse.status === 'PENDING_NEW') {
 							orderPendingNew = orderResponse;
                             console.log("order Pending New : " + JSON.stringify(orderPendingNew));
 						} else if (orderResponse.status === 'NEW') {
 							orderNew = orderResponse;
                             console.log("order New : " + JSON.stringify(orderNew));
 						} else if (orderResponse.status === 'FILLED') {
 							orderFilled = orderResponse;
 							console.log("orderFilled =" + JSON.stringify(orderFilled))
 							//done()
 						} else if (orderResponse.status === 'REJECTED') {
 							orderRejected = orderResponse;
 							console.log("orderRejected =" + JSON.stringify(orderRejected))
 							//done()
 						}
 					} else if (res.errors) {
 						    InvalidOrderRequest = res.errors;
 						    console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
 					}
				}
			});

			it(" Negative Scenario - Invalid TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});

		describe(" Negative Scenario - Incorrect Case Side ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Side ************************** ' + new Date());
				let order = '{"orders":[{"coId":"IncorrectCaseSide'+parseInt(reqId)+'","currency":"EUR", "size":"100000", "type":"Market", "side":"SELL", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Incorrect Case Side ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});


		describe(" Negative Scenario - NoCross ", function () {
        // Not able to simulate the scenario where order doesnt get matched in EMS and gets forwarded to ME.
        // When OKToCross is not specified, order request doesnt go thru
			let res;
			let execInst = '["NoCross"]';
			before(function (done) {
				console.log('*************************** Negative Scenario - NoCross ************************** ' + new Date());
            	order = '{"orders":[{"coId":"MrktDAYBuyExecFlagNoCross1'+parseInt(reqId)+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.MARKET + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
				}
			});

			it(" Negative Scenario - NoCross ", function () {
//				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
//				assert.equal('RequestValidationError.ExecInstNotSupported', orderRejected.reason);
//                assert.equal(orderData.REJECTED, orderRejected.status);
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});

		describe(" Negative Scenario - Invalid Trade Type ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid Trade Type ************************** ' + new Date());
				let order = '{"orders":[{"coId":"InvalidTradeType'+parseInt(reqId)+'","currency":"EUR", "size":"100000", "type":"MarX", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Invalid Trade Type ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});

		describe(" Negative Scenario - Incorrect Case Trade Type ", function () {
			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Incorrect Case Trade Type ************************** ' + new Date());
				let order = '{"orders":[{"coId":"IncorrectCaseTradeType'+parseInt(reqId)+'","currency":"EUR", "size":"100000", "type":"MARKET", "side":"Sell", "symbol":"EUR/USD", "execFlags" : ["OkToCross"], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it(" Negative Scenario - Incorrect Case Trade Type ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});

	});

};

OrderNotesTC();
NegativeOrderNotesTC();

