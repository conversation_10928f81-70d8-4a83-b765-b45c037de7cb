const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const orderData = require('../../config/properties').orderData
const userData = require('../../config/properties').user

// =========================== stop order amend has to be done

let wsconnect = function (done, cookies) {
    const websocket_url = 'wss://' + env.hostname +  '/v2/fxstream'
    connection = new WebSocket(websocket_url, [], {
        'headers': {
			'Host': env.apiHost,
			'apikey': env.apikey
        }
    })

    connection.onopen = () => {

        done()
    }

    connection.onerror = (error) => {
        console.log(`WebSocket error: ${error}`)
    }
}

let orderReceived;
let orderPendingNew;
let orderNew;
let orderFilled;
let orderrejected;
let order ;
let reqId;

let AmendOrderTC = function () {
	describe("Amend Order -  ", function () {


		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});


        let execInstString = "OkToCross"
        let execInst = '["OkToCross"]';

		describe("Amend Order - amend price to Fill ", function () {
		    reqId = Math.floor(Math.random() * 100)

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderRejected;
            let order;
            let AmendOrder;
            let orderReplaced;

			before(function (done) {
				console.log('*************************** ParentOrder ************************** ' + new Date());
                order = '{"orders":[{"coId":"ParentOrderTest1_' + reqId + '", "symbol":"EUR/USD", "side":"Buy", "type":"Limit","price":"0.5", "size":"2000", "currency":"EUR", "execFlags" : ["OkToCross"], "timeInForce":"GTC", "maxShow" : "100"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                    console.log("order  - res : " + JSON.stringify(res.orderResponses));
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
							done()
						}
				    }
				} // connection.on message
			}); //before parent order

            describe("AmendOrder Order Test ", function () {
                before(function (done) {
                 // amend price such that it gets filled
                 //{"orderAmendRequests":[{"price":"2.1","size":"1000","notes":"amend 2 - price","coId":"ord1", "orderId":"91613323072"}]}
                    Amendorder = '{"orders":[{"action":"amend","originalCoId":"ParentOrderTest1_' + reqId + '","coId":"amendreqTest1_' + reqId + '","size":"1000","price":"1.3"}]}';
                    console.log("Amendorder request : " + Amendorder);
                    connection.send(Amendorder);
                    connection.onmessage = (e) => {
                        let res = JSON.parse(e.data)
                       console.log("Amend Order - amend price to Fill - res : " + JSON.stringify(res));
                        if (res.orderResponses) {
                            let orderResponse = res.orderResponses[0];
                            if (orderResponse.status === 'RECEIVED') {
                                orderReceived = orderResponse;
                                console.log("Amend Order - amend price to Fill - Received : " + JSON.stringify(orderNew));
                            } else if (orderResponse.status === 'PENDING_NEW') {
                                orderPendingNew = orderResponse;
                                console.log("Amend Order - amend price to Fill - Pending New : " + JSON.stringify(orderPendingNew));
                            } else if (orderResponse.status === 'NEW') {
                                orderNew = orderResponse;
                                console.log("Amend Order - amend price to Fill - New : " + JSON.stringify(orderNew));
                            } else if  (orderResponse.status == 'REPLACED'){
                                orderReplaced = orderResponse;
                                console.log("Amend Order - amend price to Fill - replaced: " + JSON.stringify(orderReplaced));
                            } else if (orderResponse.status === 'FILLED') {
                                orderFilled = orderResponse;
                                console.log("Amend Order - amend price to Fill - orderFilled =" + JSON.stringify(orderFilled));
                                done();
                            } else if (res.errors) {
                                InvalidOrderRequest = res.errors;
                                console.log("Amend Order - amend price to Fill - InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest));
                                done();
                            } //else if ends
                        } //on message amend order
                    }
				 });//on message before

                it("Amend Order - amend price to Fill", function () {

                            if (orderFilled) {
                            console.log("Amend Order - amend price to Fill - entered filled")
                                //assert.equal('ParentOrder6', orderFilled.coId);
                                assert.equal("Limit", orderFilled.type);
                                assert.equal(orderData.GTC, orderFilled.timeInForce);
                                assert.equal(orderData.BUY, orderFilled.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                                assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                                assert.equal(orderData.size, orderFilled.size);
                                assert.equal(userData.orgname, orderFilled.org);
                                //assert.equal(execInstString, orderFilled.execFlags);
                                assert.exists(orderFilled.price);
                                assert.exists(orderFilled.averagePrice);
                                assert.equal(orderData.size, orderFilled.cumQty);
                                assert.exists(orderFilled.execId);
                                assert.exists(orderFilled.lastPrice);
                                assert.exists(orderFilled.orderId);
                                assert.exists(orderFilled.valueDate);
                                assert.exists(orderFilled.tradeDate);
                                assert.exists(orderFilled.settlCurrAmt);
                                assert.equal(orderData.executionType, orderFilled.executionType);
                                assert.equal("0", orderFilled.leavesQty);
                                assert.exists(orderFilled.counterParty);
                                assert.exists(orderFilled.counterPartyAccount);
                                assert.exists(orderFilled.userFullName);
                                assert.exists(orderFilled.transactTime);
                                assert.equal(orderData.size, orderFilled.lastQty);
                                assert.equal(orderData.ACTION, orderFilled.action);
                                assert.equal(orderData.FILLED, orderFilled.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get filled")
                            }
                        });

            });  //Amend order describe
		}); //describe parent order

		describe("Amend Order - Amend Price", function () {
		    reqId = Math.floor(Math.random() * 1000)
		    //Correct newPrice once the issue is fixed
            let origPrice = "0.5"
            let origAmt = 1000
            let newPrice = "1.3"
            let newAmt = 1000

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order;
            let AmendOrder;
            let orderReplaced;

			before(function (done) {
				console.log('*************************** Amend Order - Amend Price - ParentOrder ************************** ' + new Date());
                order = '{"orders":[{"coId":"ParentOrderTest2_' + reqId + '", "symbol":"EUR/USD", "side":"Buy", "type":"Limit","price":"' + origPrice + '", "size":"' + origAmt + '", "currency":"EUR", "execFlags" : ["OkToCross"], "timeInForce":"GTC", "maxShow" : "100"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                    console.log("order  - res : " + JSON.stringify(res.orderResponses));
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("Amend Order - Amend Price - order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("Amend Order - Amend Price - order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("Amend Order - Amend Price - order New : " + JSON.stringify(orderNew));
							done()
						}
				    }
				} // connection.on message
			}); //before parent order

            describe("AmendOrder Order Test ", function () {
            // TC fails - PLT-4927 & PLT-4928/29
                before(function (done) {
                 // amend price such that it gets filled
                 //{"orderAmendRequests":[{"price":"2.1","size":"1000","notes":"amend 2 - price","coId":"ord1", "orderId":"91613323072"}]}
                    Amendorder = '{"orders":[{"action":"amend","originalCoId":"ParentOrderTest2_' + reqId + '","coId":"amendreqTest2_' + reqId + '","size":"'+ newAmt + '","price":"' + newPrice +'"}]}';
                    console.log("Amendorder request : " + Amendorder);
                    connection.send(Amendorder);
                    connection.onmessage = (e) => {
                        let res = JSON.parse(e.data)
                       console.log("==============Amend Order - Amend Price - res : " + JSON.stringify(res));
                        if (res.orderResponses) {
                            let orderResponse = res.orderResponses[0];
                            if (orderResponse.status === 'RECEIVED') {
                                orderReceived = orderResponse;
                                console.log("Amend Order - Amend Price - order Received : " + JSON.stringify(orderReceived));
                            } else if (orderResponse.status === 'PENDING_NEW') {
                                orderPendingNew = orderResponse;
                                console.log("Amend Order - Amend Price - Pending New : " + JSON.stringify(orderPendingNew));
                            } else if (orderResponse.status === 'PENDING_REPLACE') {
                                orderPendingReplace = orderResponse;
                                console.log("Amend Order - Amend Price - Pending Replace : " + JSON.stringify(orderPendingReplace));

                            } else if (orderResponse.status === 'NEW') {
                                orderNew = orderResponse;
                                console.log("Amend Order - Amend Price - New : " + JSON.stringify(orderNew));
                                done()
                            } else if  (orderResponse.status == 'REPLACED'){
                                orderReplaced = orderResponse;
                                console.log("Amend Order - Amend Price - replaced: " + JSON.stringify(orderReplaced));
                            } else if (orderResponse.status === 'FILLED') {
                                orderFilled = orderResponse;
                                console.log("Amend Order - Amend Price - orderFilled =" + JSON.stringify(orderFilled));
                                done();
                            } else if (res.errors) {
                                InvalidOrderRequest = res.errors;
                                console.log("Amend Order - Amend Price - InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest));
                                done();
                            } //else if ends
                        } //on message amend order
                    }
				 });//on message before

                it("Amend Order - Amend Price", function () {
                // Parent order - "size":"1000", "price":"0.5"
                // Amended order - "size":"1000","price":"1.3"
//Amend Order - Amend Price - order Received : {"coId":"amendreqTest2_86","originalCoId":"ParentOrderTest2_86","size":1000,
//"org":"pfAutomationWSuser1","price":1.3,"clientOrderTime":"Apr 3, 2025 4:24:15 PM","tradeChannel":"API/WS/ESP",
//"userFullName":"pfAutomationWSuser1@pfAutomationWSuser1","action":"amend","status":"RECEIVED"}
                            if (orderReceived) {
                            // order received msg in original order and amend request are different
                            console.log("Amend Order - Amend Price - orderReceivedAmend Order - Amend Price ")
                                assert.equal("amendreqTest2_"+reqId,orderReceived.coId);
                                assert.equal("ParentOrderTest2_"+reqId, orderReceived.originalCoId);
                                //assert.equal("Limit", orderReceived.type);
                                //assert.equal(orderData.GTC, orderReceived.timeInForce);
                                //assert.equal(orderData.BUY, orderReceived.side);
                                //assert.equal(orderData.baseCurrency_EURUSD, orderReceived.currency);
                                //assert.equal(orderData.symbol_EURUSD, orderReceived.symbol);
                                assert.equal(newAmt, orderReceived.size);
                                assert.equal(userData.orgname, orderReceived.org);
                               // assert.exists(orderReceived.account);
                                assert.equal(newPrice, orderReceived.price);
                                //assert.equal("0", orderReceived.stopPrice);
                                assert.exists(orderReceived.clientOrderTime);
                                assert.exists(orderReceived.tradeChannel);
                                assert.exists(orderReceived.userFullName);
                                assert.equal("amend",orderReceived.action);
                                assert.equal("RECEIVED",orderReceived.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get filled")

                            }
                            if (orderReplaced) {
                            //{"coId":"amendreqTest2_12","originalCoId":"ParentOrderTest2_12","type":"Limit","timeInForce":"GTC","side":"Buy","currency":"EUR",
                             //"symbol":"EUR/USD","size":1000,"account":"pfAutomationWS","price":1.3,"maxShow":1000,"averagePrice":0,"cumQty":0,
                             //"execId":"FXI4390416705","lastPrice":0,"orderId":"***********","executionType":"REPLACE","leavesQty":1000,"userFullName":"pfAutomationWSuser1@pfAutomationWSuser1",
                             //"action":"amend","status":"REPLACED"}
                                console.log("Amend Order - Amend Price - orderReplaced")
                               assert.exists(orderReplaced.coId);
                                //assert.equal("ParentOrderTest2_"+reqId, orderReplaced.originalCoId);
                                assert.equal("Limit", orderReplaced.type);
                                assert.equal(orderData.GTC, orderReplaced.timeInForce);
                                assert.equal(orderData.BUY, orderReplaced.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderReplaced.currency);
                                assert.equal(orderData.symbol_EURUSD, orderReplaced.symbol);
                                assert.equal(newAmt, orderReplaced.size);
                                userFullName = userData.username + "@" + userData.orgname;
                                assert.equal(userFullName, orderReplaced.userFullName);
								assert.equal(newPrice, orderReplaced.price);
                                assert.equal("0",orderReplaced.averagePrice);
                                assert.equal("0",orderReplaced.cumQty);
                                assert.exists(orderReplaced.execId);
                                assert.equal("0",orderReplaced.lastPrice);
                                assert.exists(orderReplaced.orderId);
                                assert.equal("REPLACE",orderReplaced.executionType);
                                assert.equal(newAmt,orderReplaced.leavesQty);
                                assert.equal("amend",orderReplaced.action);
                                assert.equal("REPLACED",orderReplaced.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get filled")
                            }
                            if (orderPendingReplace) {
                            //2023.11.21, 09:10:19.0911 UTC -> {"orderResponses":[{"coId":"21NovOrder4_a","originalCoId":"21NovOrder4","type":"Limit",
                            //"timeInForce":"GTC","side":"Buy","currency":"EUR","symbol":"EUR/USD","size":9000000.0,"org":"spoorthy","price":0.8,
                            //"execFlags":[],"averagePrice":0.0,"cumQty":0.0,"execId":"0","orderId":"NONE","executionType":"PENDING_REPLACE",
                            //"leavesQty":9000000.0,"userFullName":"user1@spoorthy","action":"amend","status":"PENDING_REPLACE"}]}
                                console.log("Amend Order - Amend Price - orderPendingReplace");
                                console.log("===============================Amend Order - Amend Price - orderPendingReplace" + JSON.stringify(orderPendingReplace));

                               assert.exists(orderReplaced.coId);
                                //assert.equal("ParentOrderTest2_"+reqId, orderReplaced.originalCoId);
                                assert.equal("Limit", orderPendingReplace.type);
                                assert.equal(orderData.GTC, orderPendingReplace.timeInForce);
                                assert.equal(orderData.BUY, orderPendingReplace.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderPendingReplace.currency);
                                assert.equal(orderData.symbol_EURUSD, orderPendingReplace.symbol);
                                assert.equal(newAmt, orderPendingReplace.size);
                                assert.equal(origPrice,orderPendingReplace.price);
                                assert.equal("0",orderPendingReplace.averagePrice);
                                assert.equal("0",orderPendingReplace.cumQty);
                                assert.exists(orderPendingReplace.execId);
                                assert.equal("0",orderReplaced.lastPrice);
                                assert.exists(orderPendingReplace.orderId);
                                assert.equal("PENDING_REPLACE",orderPendingReplace.executionType);
                                assert.equal(orderData.size,orderPendingReplace.leavesQty);
                                assert.equal("amend",orderPendingReplace.action);
                                assert.equal("PENDING_REPLACE",orderPendingReplace.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get replaced")
                            }

                            if (orderNew) {
                                console.log("Amend Order - Amend Price - orderNew")
                               assert.exists(orderNew.coId);
                                //assert.equal("ParentOrderTest2_"+reqId, orderNew.originalCoId);
                                assert.equal("Limit", orderNew.type);
                                assert.equal(orderData.GTC, orderNew.timeInForce);
                                assert.equal(orderData.BUY, orderNew.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderNew.currency);
                                assert.equal(orderData.symbol_EURUSD, orderNew.symbol);
                                assert.equal(newAmt, orderNew.size);
                                assert.equal(origPrice,orderNew.price);
                                assert.equal("0",orderNew.averagePrice);
                                assert.equal("0",orderNew.cumQty);
                                assert.exists(orderNew.execId);
                                assert.equal("0",orderNew.lastPrice);
                                assert.exists(orderNew.orderId);
                                assert.equal("NEW",orderNew.executionType);
                                assert.equal(orderData.size,orderNew.leavesQty);
                                assert.equal("place",orderNew.action);
                                assert.equal("NEW",orderNew.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get replaced")
                            }

                });

            });  //Amend order describe
		}); //describe parent order

		describe("Amend Order - Amend Amount", function () {
		    reqId = Math.floor(Math.random() * 100)
		    //Correct newPrice once the issue is fixed
            let origPrice = "0.5"
            let origAmt = 1000
            let newPrice = "1.5"
            let newAmt = 2000

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order;
            let AmendOrder;
            let orderReplaced;

			before(function (done) {
				console.log('Amend Order - Amend Amount - ParentOrder ************************** ' + new Date());
                order = '{"orders":[{"coId":"ParentOrderTest3_' + reqId + '", "symbol":"EUR/USD", "side":"Buy", "type":"Limit","price":"' + origPrice + '", "size":"' + origAmt + '", "currency":"EUR", "execFlags" : ["OkToCross"], "timeInForce":"GTC", "maxShow" : "100"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                    console.log("Amend Order - Amend Amount  - res : " + JSON.stringify(res.orderResponses));
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("Amend Order - Amend Amount -  Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("Amend Order - Amend Amount -  Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("Amend Order - Amend Amount -  New : " + JSON.stringify(orderNew));
							done()
						}
				    }
				} // connection.on message
			}); //before parent order

            describe("AmendOrder Order Test ", function () {
            // TC fails - PLT-4927 & PLT-4928/29
                before(function (done) {
                 // amend Amount
                    Amendorder = '{"orders":[{"action":"amend","originalCoId":"ParentOrderTest3_' + reqId + '","coId":"amendreqTest3_' + reqId + '","size":"'+ newAmt + '","price":"' + newPrice +'"}]}';
                    console.log("Amend Order - Amend Amount -  request : " + Amendorder);
                    connection.send(Amendorder);
                    connection.onmessage = (e) => {
                        let res = JSON.parse(e.data)
                       console.log("Amend Order - Amend Amount - res : " + JSON.stringify(res));
                        if (res.orderResponses) {
                            let orderResponse = res.orderResponses[0];
                            if (orderResponse.status === 'RECEIVED') {
                                orderReceived = orderResponse;
                                console.log("Amend Order - Amend Amount - Received : " + JSON.stringify(orderReceived));
                            } else if (orderResponse.status === 'PENDING_NEW') {
                                orderPendingNew = orderResponse;
                                console.log("Amend Order - Amend Amount - Pending New : " + JSON.stringify(orderPendingNew));
                            } else if (orderResponse.status === 'PENDING_REPLACE') {
                                orderPendingReplace = orderResponse;
                                console.log("Amend Order - Amend Amount - Pending Replace : " + JSON.stringify(orderPendingReplace));

                            } else if (orderResponse.status === 'NEW') {
                                orderNew = orderResponse;
                                console.log("Amend Order - Amend Amount -  New : " + JSON.stringify(orderNew));
                                done()
                            } else if  (orderResponse.status == 'REPLACED'){
                                orderReplaced = orderResponse;
                                console.log("Amend Order - Amend Amount - replaced: " + JSON.stringify(orderReplaced));
                            } else if (orderResponse.status === 'FILLED') {
                                orderFilled = orderResponse;
                                console.log("Amend Order - Amend Amount - orderFilled =" + JSON.stringify(orderFilled));
                                done();
                            } else if (res.errors) {
                                InvalidOrderRequest = res.errors;
                                console.log("Amend Order - Amend Amount - InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest));
                                done();
                            } //else if ends
                        } //on message amend order
                    }
				 });//on message before

                it("Amend Order - Amend Amount", function () {
                // Parent order - "size":"1000", "price":"0.5"
                // Amended order - "size":"2000","price":"0.5"

                            if (orderReceived) {
                            // orderReceived when a new order is placed and the one when an order is amended are lil different. Few tags are not present in the msg received when order is amended
                            console.log("Amend Order - Amend Amount - orderReceived")
                                assert.equal("amendreqTest3_"+reqId,orderReceived.coId);
                                assert.equal("ParentOrderTest3_"+reqId, orderReceived.originalCoId);
                                //assert.equal("Limit", orderReceived.type);
                                //assert.equal(orderData.GTC, orderReceived.timeInForce);
                                //assert.equal(orderData.BUY, orderReceived.side);
                                //assert.equal(orderData.baseCurrency_EURUSD, orderReceived.currency);
                                //assert.equal(orderData.symbol_EURUSD, orderReceived.symbol);
                                assert.equal(newAmt, orderReceived.size);
                                assert.equal(userData.orgname, orderReceived.org);
                                //assert.exists(orderReceived.account);
                                assert.equal(newPrice, orderReceived.price);
                                //assert.equal("0", orderReceived.stopPrice);
                                assert.exists(orderReceived.clientOrderTime);
                                assert.exists(orderReceived.tradeChannel);
                                assert.exists(orderReceived.userFullName);
                                assert.equal("amend",orderReceived.action);
                                assert.equal("RECEIVED",orderReceived.status);                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get filled")

                            }
                            if (orderReplaced) {
                                console.log("Amend Order - Amend Amount -  orderReplaced")
                               assert.exists(orderReplaced.coId);
                                //assert.equal("ParentOrderTest2_"+reqId, orderReplaced.originalCoId);
                                assert.equal("Limit", orderReplaced.type);
                                assert.equal(orderData.GTC, orderReplaced.timeInForce);
                                assert.equal(orderData.BUY, orderReplaced.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderReplaced.currency);
                                assert.equal(orderData.symbol_EURUSD, orderReplaced.symbol);
                                assert.equal(newAmt, orderReplaced.size);
                                //assert.equal(userData.orgname, orderReplaced.org);
								assert.equal(newPrice, orderReplaced.price);
                                assert.equal("0",orderReplaced.averagePrice);
                                assert.equal("0",orderReplaced.cumQty);
                                assert.exists(orderReplaced.execId);
                                assert.equal("0",orderReplaced.lastPrice);
                                assert.exists(orderReplaced.orderId);
                                assert.equal("REPLACE",orderReplaced.executionType);
                                assert.equal(newAmt,orderReplaced.leavesQty);
                                assert.equal("amend",orderReplaced.action);
                                assert.equal("REPLACED",orderReplaced.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get filled")
                            }
                            if (orderPendingReplace) {
                                console.log("Amend Order - Amend Amount - orderPendingReplace")
                               assert.exists(orderPendingReplace.coId);
                                //assert.equal("ParentOrderTest2_"+reqId, orderReplaced.originalCoId);
                                assert.equal("Limit", orderPendingReplace.type);
                                assert.equal(orderData.GTC, orderPendingReplace.timeInForce);
                                assert.equal(orderData.BUY, orderPendingReplace.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderPendingReplace.currency);
                                assert.equal(orderData.symbol_EURUSD, orderPendingReplace.symbol);
                                assert.equal(origAmt, orderPendingReplace.size);
                                assert.equal(userData.orgname, orderPendingReplace.org);
                                assert.equal(origPrice,orderPendingReplace.price);
                                assert.equal("0",orderPendingReplace.averagePrice);
                                assert.equal("0",orderPendingReplace.cumQty);
                                assert.exists(orderPendingReplace.execId);
                                //assert.equal("0",orderPendingReplace.lastPrice);
                                assert.exists(orderPendingReplace.orderId);
                                assert.equal("PENDING_REPLACE",orderPendingReplace.executionType);
                                assert.equal(orderData.size,orderPendingReplace.leavesQty);
                                assert.equal("amend",orderPendingReplace.action);
                                assert.equal("PENDING_REPLACE",orderPendingReplace.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get replaced")
                            }

                            if (orderNew) {
                                console.log("Amend Order - Amend Amount -  orderPendingReplace")
                               assert.exists(orderNew.coId);
                                //assert.equal("ParentOrderTest2_"+reqId, orderReplaced.originalCoId);
                                assert.equal("Limit", orderNew.type);
                                assert.equal(orderData.GTC, orderNew.timeInForce);
                                assert.equal(orderData.BUY, orderNew.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderNew.currency);
                                assert.equal(orderData.symbol_EURUSD, orderNew.symbol);
                                assert.equal(origAmt, orderNew.size);
                                assert.equal(userData.orgname, orderNew.org);
                                assert.equal(origPrice,orderNew.price);
                                assert.equal("0",orderNew.averagePrice);
                                assert.equal("0",orderNew.cumQty);
                                assert.exists(orderNew.execId);
                                assert.equal("0",orderNew.lastPrice);
                                assert.exists(orderNew.orderId);
                                assert.equal("NEW",orderNew.executionType);
                                assert.equal(orderData.size,orderNew.leavesQty);
                                assert.equal("place",orderNew.action);
                                assert.equal("NEW",orderNew.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get replaced")
                            }

                            if (orderFilled) {
                            console.log("Amend Order - amend price to Fill - entered filled")
                                //assert.equal('ParentOrder6', orderFilled.coId);
                                assert.equal("Limit", orderFilled.type);
                                assert.equal(orderData.GTC, orderFilled.timeInForce);
                                assert.equal(orderData.BUY, orderFilled.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                                assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                                assert.equal(newAmt, orderFilled.size);
                                assert.equal(userData.orgname, orderFilled.org);
                                //assert.equal(execInstString, orderFilled.execFlags);
                                assert.exists(orderFilled.price);
                                assert.exists(orderFilled.averagePrice);
                                assert.equal(newAmt, orderFilled.cumQty);
                                assert.exists(orderFilled.execId);
                                assert.exists(orderFilled.lastPrice);
                                assert.exists(orderFilled.orderId);
                                assert.exists(orderFilled.valueDate);
                                assert.exists(orderFilled.tradeDate);
                                assert.exists(orderFilled.settlCurrAmt);
                                assert.equal(orderData.executionType, orderFilled.executionType);
                                assert.equal("0", orderFilled.leavesQty);
                                assert.exists(orderFilled.counterParty);
                                assert.exists(orderFilled.counterPartyAccount);
                                assert.exists(orderFilled.userFullName);
                                assert.exists(orderFilled.transactTime);
                                assert.equal(newAmt, orderFilled.lastQty);
                                assert.equal(orderData.ACTION, orderFilled.action);
                                assert.equal(orderData.FILLED, orderFilled.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get filled")
                            }


                });

            });  //Amend order describe
		}); //describe parent order

		describe("Amend Order - Cancel Parent Order", function () {
		    reqId = Math.floor(Math.random() * 1000)
		    //Correct newPrice once the issue is fixed
            let origPrice = "0.5"
            let origAmt = 1000
            let newPrice = "0.7"
            let newAmt = 1000

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order;
            let AmendOrder;
            let orderReplaced;

			before(function (done) {
				console.log('*************************** Amend Order - Cancel Parent Order - ParentOrder ************************** ' + new Date());
                order = '{"orders":[{"coId":"ParentOrderTest4_' + reqId + '", "symbol":"EUR/USD", "side":"Buy", "type":"Limit","price":"' + origPrice + '", "size":"' + origAmt + '", "currency":"EUR", "execFlags" : ["OkToCross"], "timeInForce":"GTC", "maxShow" : "100"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                    console.log("order  - res : " + JSON.stringify(res.orderResponses));
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
						    orderReceived = orderResponse;
                            console.log("Amend Order - Cancel Parent Order - order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
                            console.log("Amend Order - Cancel Parent Order - order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("Amend Order - Cancel Parent Order - order New : " + JSON.stringify(orderNew));
							done()
						}
				    }
				} // connection.on message
			}); //before parent order

            describe("AmendOrder Order Test ", function () {
            // TC fails - PLT-4927 & PLT-4928/29
                before(function (done) {
                 // amend price such that order doesnt get filled
                    Amendorder = '{"orders":[{"action":"amend","originalCoId":"ParentOrderTest4_' + reqId + '","coId":"amendreqTest4_' + reqId + '","size":"'+ newAmt + '","price":"' + newPrice +'"}]}';
                    console.log("Amendorder request : " + Amendorder);
                    connection.send(Amendorder);
                    connection.onmessage = (e) => {
                        let res = JSON.parse(e.data)
                       console.log("==============Amend Order - Cancel Parent Order - res : " + JSON.stringify(res));
                        if (res.orderResponses) {
                            let orderResponse = res.orderResponses[0];
                            if (orderResponse.status === 'RECEIVED') {
                                orderReceived = orderResponse;
                                console.log("Amend Order - Cancel Parent Order - order Received : " + JSON.stringify(orderReceived));
                            } else if (orderResponse.status === 'PENDING_NEW') {
                                orderPendingNew = orderResponse;
                                console.log("Amend Order - Cancel Parent Order - Pending New : " + JSON.stringify(orderPendingNew));
                            } else if (orderResponse.status === 'PENDING_REPLACE') {
                                orderPendingReplace = orderResponse;
                                console.log("Amend Order - Cancel Parent Order - Pending Replace : " + JSON.stringify(orderPendingReplace));
                            } else if  (orderResponse.status == 'REPLACED'){
                                orderReplaced = orderResponse;
                                Cancelorder = '{"orders":[{"action":"cancel", "coId":"ParentOrderTest4_'+ reqId +'", "requestId":"req'+ reqId + '", "side":"Buy", "symbol":"EUR/USD", "size":"' + newAmt + '"}]}';
                                console.log("CancelOrder request :" + Cancelorder)
                                connection.send(Cancelorder);
                                console.log("Amend Order - Cancel Parent Order - replaced: " + JSON.stringify(orderReplaced));
                           } else if  (orderResponse.reason == 'TOO_LATE_TO_CANCEL'){
                                orderCancelResponse = orderResponse;
                                console.log("Amend Order - Cancel Parent Order - cancelResponse: " + JSON.stringify(orderCancelResponse));
                                done()
                            } else if (orderResponse.executionType == 'CANCEL_REJECTED') {
                                orderCancelResponse = orderResponse;
                                console.log("+++++++++++++++++++++++++++Amend Order - Cancel Parent Order - cancelResponse: " + JSON.stringify(orderCancelResponse));
                                done()
                            } else if (res.errors) {
                                InvalidOrderRequest = res.errors;
                                console.log("Amend Order - Cancel Parent Order - InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest));
                                done();
                            } //else if ends
                        } //on message amend order
                    }
				 });//on message before

                it("Amend Order - Cancel Parent Order", function () {
                // Parent order - "size":"1000", "price":"0.5"
                // Amended order - "size":"1000","price":"0.7"

                            if (orderReceived) {
                            console.log("Amend Order - Cancel Parent Order - orderReceivedAmend Order - Cancel Parent Order ")
                                assert.equal("ParentOrderTest4_"+reqId,orderReceived.coId);
                                assert.equal(orderData.BUY, orderReceived.side);
                                assert.equal(orderData.symbol_EURUSD, orderReceived.symbol);
                                assert.equal(newAmt, orderReceived.size);
                                //assert.equal(userData.orgname, orderReceived.org);
                                assert.exists(orderReceived.clientOrderTime);
                                assert.exists(orderReceived.tradeChannel);
                                assert.exists(orderReceived.userFullName);
                                assert.equal("cancel",orderReceived.action);
                                assert.equal("RECEIVED",orderReceived.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get filled")
                            }
                            if (orderCancelResponse) {
//res : {"orderResponses":[{"originalCoId":"ParentOrderTest4_15","userFullName":"user1@spoorthy","requestId":"req15",
//"action":"place","reason":"TOO_LATE_TO_CANCEL"}]}
                                assert.equal("ParentOrderTest4_"+reqId,orderCancelResponse.coId);
                                assert.equal("cancel",orderCancelResponse.action);
                                assert.equal("UNKNOWN_ORDER",orderCancelResponse.reason);
                                assert.exists(orderCancelResponse.requestId);
                                assert.exists(orderCancelResponse.userFullName);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order cancel request failed")
                            }

                });

            });  //Amend order describe

		}); //describe parent order

		describe("Amend Order - Cancel Amended Order", function () {
		    reqId = Math.floor(Math.random() * 100)
		    //Correct newPrice once the issue is fixed
            let origPrice = "0.5"
            let origAmt = 1000
            let newPrice = "0.7"
            let newAmt = 1000

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order;
            let AmendOrder;
            let orderReplaced;

			before(function (done) {
				console.log('*************************** Amend Order - Cancel Amended Order - ParentOrder ************************** ' + new Date());
                order = '{"orders":[{"coId":"ParentOrderTest5_' + reqId + '", "symbol":"EUR/USD", "side":"Buy", "type":"Limit","price":"' + origPrice + '", "size":"' + origAmt + '", "currency":"EUR", "execFlags" : ["OkToCross"], "timeInForce":"GTC", "maxShow" : "100"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
                    console.log("order  - res : " + JSON.stringify(res.orderResponses));
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("Amend Order - Cancel Amended Order - order New : " + JSON.stringify(orderNew));
							done()
						}
				    }
				} // connection.on message
			}); //before parent order

            describe("AmendOrder Order Test ", function () {
            // TC fails - PLT-4927 & PLT-4928/29
                before(function (done) {
                 // amend price such that order doesnt get filled
                    Amendorder = '{"orders":[{"action":"amend","originalCoId":"ParentOrderTest5_' + reqId + '","coId":"amendreqTest5_' + reqId + '","size":"'+ newAmt + '","price":"' + newPrice +'"}]}';
                    console.log("Amendorder request : " + Amendorder);
                    connection.send(Amendorder);
                    connection.onmessage = (e) => {
                        let res = JSON.parse(e.data)
                       console.log("==============Amend Order - Cancel Amended Order - res : " + JSON.stringify(res));
                        if (res.orderResponses) {
                            let orderResponse = res.orderResponses[0];
                            if (orderResponse.status === 'RECEIVED') {
                                orderReceived = orderResponse;
                            } else if (orderResponse.status === 'PENDING_NEW') {
                                orderPendingNew = orderResponse;
                            } else if (orderResponse.status === 'PENDING_REPLACE') {
                                orderPendingReplace = orderResponse;
                            } else if (orderResponse.status === 'NEW') {
                                orderNew = orderResponse;
                            } else if  (orderResponse.status == 'REPLACED'){
                                orderReplaced = orderResponse;
                                Cancelorder = '{"orders":[{"action":"cancel", "coId":"' + orderReplaced.coId + '", "requestId":"req'+ reqId + '", "side":"Buy", "symbol":"EUR/USD", "size":"' + newAmt + '"}]}';
                                console.log("=========== cancel order request===" + Cancelorder)
                                connection.send(Cancelorder);
                           } else if  (orderResponse.status == 'CANCELED'){
                                orderCancelResponse = orderResponse;
                                done()
                            } else if (res.errors) {
                                InvalidOrderRequest = res.errors;
                                console.log("Amend Order - Cancel Amended Order - InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest));
                                done();
                            } //else if ends
                        } //on message amend order
                    }
				 });//on message before

                it("Amend Order - Cancel Amended Order", function () {
                // Parent order - "size":"1000", "price":"0.5"
                // Amended order - "size":"1000","price":"0.7"

                            if (orderReceived) {
                            console.log("Amend Order - Cancel Amended Order - orderReceivedAmend Order - Cancel Amended Order ")
                            console.log(orderCancelResponse)
                                assert.exists(orderReceived.coId);
                                assert.equal(orderData.BUY, orderReceived.side);
                                assert.equal(orderData.symbol_EURUSD, orderReceived.symbol);
                                assert.equal(newAmt, orderReceived.size);
                                assert.equal(userData.orgname, orderReceived.org);
                                assert.exists(orderReceived.clientOrderTime);
                                assert.exists(orderReceived.tradeChannel);
                                assert.exists(orderReceived.userFullName);
                                assert.equal("cancel",orderReceived.action);
                                assert.equal("RECEIVED",orderReceived.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order didnt get filled")
                            }
                            if (orderCancelResponse) {
								//assert.equal("amendreqTest3_"+reqId,orderCancelResponse.coId);
                                //assert.equal("ParentOrderTest3_"+reqId, orderCancelResponse.originalCoId);
                                assert.equal("Limit", orderCancelResponse.type);
                                assert.equal(orderData.GTC, orderCancelResponse.timeInForce);
                                assert.equal(orderData.BUY, orderCancelResponse.side);
                                assert.equal(orderData.baseCurrency_EURUSD, orderCancelResponse.currency);
                                assert.equal(orderData.symbol_EURUSD, orderCancelResponse.symbol);
                                assert.equal(newAmt, orderCancelResponse.size);
                                assert.equal(userData.orgname, orderCancelResponse.org);
                                assert.exists(orderCancelResponse.averagePrice);
								assert.exists(orderCancelResponse.averagePrice);
								assert.exists(orderCancelResponse.cumQty);
								assert.exists(orderCancelResponse.execId);
								assert.exists(orderCancelResponse.lastPrice);
								assert.exists(orderCancelResponse.orderId);
								assert.equal("CANCELED", orderCancelResponse.executionType);
								assert.equal(newAmt, orderCancelResponse.leavesQty);
								assert.exists(orderCancelResponse.userFullName);
                                assert.equal("cancel",orderCancelResponse.action);
                                assert.equal("CANCELED",orderCancelResponse.status);
                            } else {
                                assert.equal('True', 'False')
                                console.log ("order status - Order cancel request failed")
                            }

                });

            });  //Amend order describe

		}); //describe parent order

// stop order amend has to be done

	});
};


AmendOrderTC();