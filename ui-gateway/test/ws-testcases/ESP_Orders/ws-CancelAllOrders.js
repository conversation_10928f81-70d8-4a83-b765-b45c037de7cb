const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const orderData = require('../../config/properties').orderData
const userData = require('../../config/properties').user


let orderReceived;
let orderPendingNew;
let orderNew;
let orderFilled;
let orderrejected;
let order;
let Cancelres;
let orderPendingCancel, orderCancel, orderCancelReceived;
let counter
let orderCount

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'

    connection = new WebSocket(websocket_url, [], {
        'headers': {
            'Host': env.apiHost,
            'apikey': env.apikey
        }
    })

    connection.onopen = () => {
        console.log('WS connected successfully: ' + new Date());
        setTimeout(function () { done(); }, 5000);
    }

    connection.onerror = (error) => {
        console.log(`WebSocket error: ${error}`)
    }
}


let CancelAllTC = function () {
    describe("CancelAll Orders ", function () {
        before(function (done) {
            wsconnect(done);
        });

        after(function () {
            connection.close()
        });

        describe("CancelAll Orders test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order,Cancelorder;
            let orderid =  new Date(). getTime();


            before(function (done){
                console.log('*************************** Creating orders for CancelAll  ************************** ' + new Date());
                id =  new Date(). getTime();

                orderid1 = id + 1;
                order = '{"orders":[{"coId":"'+orderid1+ '","currency":"EUR", "execFlags" : [], "size":"1000000", "type":"Limit","price":"1.5", "side":"Sell", "symbol":"EUR/USD", "timeInForce":"GTC"}]}';
                console.log("order request : " + order);
                connection.send(order);

                orderid2 = id + 2;
                order = '{"orders":[{"coId":"'+orderid2+ '","currency":"EUR", "execFlags" : [], "size":"1000000", "type":"Limit","price":"1.5", "side":"Sell", "symbol":"EUR/USD", "timeInForce":"GTC"}]}';
                console.log("order request : " + order);
                connection.send(order);

                orderid3  = id + 3;
                order = '{"orders":[{"coId":"'+orderid3+ '","currency":"EUR", "execFlags" : [], "size":"1000000", "type":"Limit","price":"0.9", "side":"Buy", "symbol":"EUR/USD", "timeInForce":"GTC"}]}';
                console.log("order request : " + order);
                connection.send(order);

                orderCount = 0

                // These orders are placed for cancelAll api, these orders will get placed and be ready to cancelAll
                connection.onmessage = (e) => {
                    let res = JSON.parse(e.data)
//                    console.log("order  : " + JSON.stringify(res.orderResponses));
                    if (res.orderResponses) {
                        let orderResponse = res.orderResponses[0];
                        if (orderResponse.status === 'RECEIVED') {
                            orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderNew));
                        } else if (orderResponse.status === 'PENDING_NEW') {
                            orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
                        }else if (orderResponse.status === 'NEW') {
                            orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
                            orderCount++;
                            if(orderCount == 3) {
                                counter = orderCount
                                done()
                            }

                        }
                    } else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
                }
            });

            after(function (done){
                done();
            });


            it("Create Orders for CancelAll API", function () {
                if (orderCount == 3) {
                    console.log("orders have been created" + orderCount)
                } else {
                    assert.equal('True', 'False')
                    console.log ("total order placed : " + orderCount)
                }
            });


        describe("CancelAll test", function() {
            doneFlag = true

            before(function (done) {
                //Placing an order cancel request
                CancelAllOrders = '{"orders":[{"action":"cancelAll", "requestId":"A12"}]}';
                console.log("cancelAll request :" + CancelAllOrders)
                connection.send(CancelAllOrders);
                connection.onmessage = (e) => {
                    Cancelres = JSON.parse(e.data)
//                    console.log("Cancel order response: " +JSON.stringify(Cancelres.orderResponses));
                    if (Cancelres.orderResponses) {
                        let orderResponse = Cancelres.orderResponses[0];
                        if (orderResponse.status === 'RECEIVED') {
                            orderCancelReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderCancelReceived));
                        } else if (orderResponse.status === 'PENDING_CANCEL') {
                            orderPendingCancel = orderResponse;
                            console.log("order Pending Cancel : " + JSON.stringify(orderPendingCancel));
                        }else if (orderResponse.status === 'CANCELED') {
                            orderCancel = orderResponse;
                            console.log("order Cancel : " + JSON.stringify(orderCancel));
                            counter--
                            console.log("counter = " + counter)
                            console.log("=doneFlag=" + doneFlag + "=counter=" + counter)
                            if(doneFlag && (counter == 0)) {
                                doneFlag = false
                                done()
                            }
                        }
                    } else if (Cancelres.errors) {
                        InvalidOrderRequest = Cancelres.errors;
                        console.log("Invalid CancelAll request =" + JSON.stringify(InvalidOrderRequest));
                        done()
                    }
                }
            });

            it("Cancel Order Test", function () {
                if (counter==0) {
                    console.log("Total orders cancelled : " + orderCount)
                } else {
                    console.log ("orders are not cancelled")
                    assert.equal('True', 'False')
                }
            });

        });

        });

    });
}

CancelAllTC();

