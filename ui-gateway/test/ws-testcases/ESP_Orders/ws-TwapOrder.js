const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const orderData = require('../../config/properties').orderData
const userData = require('../../config/properties').use

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }

let orderReceived;
let orderPendingNew;
let orderNew;
let orderFilled;
let orderrejected;
let order ;

let TwapOrderTC = function () {
	describe("Twap Order - ", function () {

		before(function (done) {
			wsconnect(done)
		});

		after(function () {
			connection.close()
		});


        let execInstString = "OkToCross"
        let execInst = '["OkToCross"]';


		describe("Placing Market Sell IOC Order ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderrejected;
            let order,Cancelorder;
            let orderid =  new Date(). getTime();

			before(function (done)
			{
				console.log('*************************** Market Sell IOC OrderID ************************** ' + new Date());
            	order = '{"orders":[{"coId":"'+orderid+'", "symbol":"EUR/USD", "side":"Sell", "type":"Algo","price":"1.1848", "size":"2000", "currency":"EUR", "execFlags" : ["OkToCross"], "timeInForce":"GTC", "maxShow" : "100", "sliceSize" : "1000", "randomizeSliceSizePercentage" : "9", "randomizeSliceIntervalPercentage" : "70", "targetStrategy" : "LitSwitch", "sliceInterval" : 10000, "actionOnExpiry" : "Twap"}]}';
            	//works,..order = '{"orders":[{"coId":"'+orderid+ '","currency":"EUR", "execFlags" : ["OkToCross","Algo","TWAP"], "size":"1000000", "type":"Market","price":"1.1993", "side":"Sell", "symbol":"EUR/USD", "timeInForce":"GTC"}]}';
                console.log("order request : " + order);
				connection.send(order);

                            	connection.onmessage = (e) => {
                					let res = JSON.parse(e.data)
                                   console.log("order  : " + JSON.stringify(res.orderResponses));
                					if (res.orderResponses) {
                						let orderResponse = res.orderResponses[0];
                						if (orderResponse.status === 'RECEIVED') {
                							orderReceived = orderResponse;
                                            console.log("order Received : " + JSON.stringify(orderReceived));
                						} else if (orderResponse.status === 'PENDING_NEW') {
                							orderPendingNew = orderResponse;
                                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
                						} else if (orderResponse.status === 'NEW') {
                							orderNew = orderResponse;
                                            console.log("order New : " + JSON.stringify(orderNew));
                						} else if (orderResponse.status === 'FILLED') {
                							orderFilled = orderResponse;
                							console.log("orderFilled =" + JSON.stringify(orderFilled))
                							done();
                						}
                					} else if (res.errors) {
                                            InvalidOrderRequest = res.errors;
                                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                                           	//done();
                                    }
                                  }
                                });


			it("Market Sell IOC Order Test", function () {
				if (orderFilled) {
                    console.log("Order filled response is :  "+JSON.stringify(orderFilled));
                    //assert.equal(orderid, orderFilled.coId);
                    /*assert.equal(orderData.IOC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.currency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.settlCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);*/
                } else {
                    assert.equal('True', 'False')
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

    });

};

TwapOrderTC();
