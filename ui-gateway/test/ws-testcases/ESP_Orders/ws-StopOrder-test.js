 const assert = require('chai').assert

const WebSocket = require('ws')
const env = require('../../config/properties').env
const orderData = require('../../config/properties').orderData
const userData = require('../../config/properties').user

// sample Stop msgs
// Stop order request: {"orders":[{"coId":"Stop11","currency":"EUR","size":"1000000", "type":"Stop","price":"1.18353", "side":"Sell",
//"symbol":"EUR/USD", "timeInForce":"GTC",  "stopPrice" : "1.18353", "execFlags" : ["OkToCross", "BidTrigger", "AtRate"]}]}
// expiryTime should be specified incase of GTT orders, otherwise EMS treats it as GTC. UIG cannot reject the request when expiry time is not specified for GTT orders.
// orderFilled ={"coId":"StopIOCSell","type":"Stop","timeInForce":"GTC","side":"Sell","currency":"EUR","symbol":"EUR/USD","size":1000,"org":"XCN1139","price":1.18353,"stopPrice":1.18353,"execFlags":["OkToCross","BidTrigger","AtRate"],"averagePrice":1.1863,"cumQty":1000,"execId":"FXI9082616162","lastPrice":1.1863,"lastQty":1000,"orderId":"4744204150","valueDate":"20201117","tradeDate":"20201114","settlCurrAmt":1186.3,"settlCurrency":"USD","executionType":"TRADE","leavesQty":0,"counterParty":"UBS","action":"place","status":"FILLED"}

// Supports only GTC, DAY & GTT TIFs
// Sometimes last qty will be different for  order size (incase of partial fills), hence checking just the tag not value
// Few tags like avgPrice, lastPrice, cumQty, LastQty etc are validated for tag presence but not the value.

let wsconnect = function (done, cookies) {
        const websocket_url = 'wss://' + env.hostname +':'+ env.port + '/v2/fxstream'
        connection = new WebSocket(websocket_url, [], {
  		'headers': {
   			'Host': env.apiHost,
   			'apikey': env.apikey
   		}
        })

        connection.onopen = () => {
            done()
            console.log("Logged in to -- "+env.apiHost + " and " + env.apikey)
        }

        connection.onerror = (error) => {
            console.log(`WebSocket error: ${error}`)
        }
    }


reqId = Math.floor(Math.random() * 1000)

let StopOrderTC = function () {
	describe("Stop Order - ", function () {
		before(function (done) {
			wsconnect(done);
		});

		after(function () {
			connection.close()
		});

		describe("Stop Sell GTC BidTriggerAtRate Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
            let execInstString = "BidTrigger,AtRate,OkToCross"
            let execInst = '["BidTrigger", "AtRate"]';

			before(function (done) {
				console.log('*************************** Stop Sell GTC BidTriggerAtRate ************************** ' + new Date());
                coId = "StopGTCSellBidTrig"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"'+coId+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '","type":"' + orderData.STOP + '", "price":"' + orderData.stopLmtSellPrice + '", "stopPrice" : "' + orderData.stopSellAtPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
							done()
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
				}
			});

			it("Stop Sell GTC BidTriggerAtRate Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.STOP, orderFilled.type);
                    assert.equal(orderData.GTC, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.averagePrice);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.stopPrice);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Stop Sell DAY BidTriggerAtRate Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            let execInstString = "BidTrigger,AtRate,OkToCross";
            let execInst = '["BidTrigger","AtRate"]';

        	before(function (done) {
				console.log('*************************** Stop Sell DAY BidTriggerAtRate ************************** ' + new Date());
                coId = "StopDaySellBidTrigAtRate"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"'+coId+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.STOP + '", "price":"' + orderData.stopLmtSellPrice + '", "stopPrice" : "' + orderData.stopSellAtPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
							done()
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
				}
			});

			it("Stop Sell DAY BidTriggerAtRate Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.STOP, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.stopPrice);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});


		});

		describe("Stop Sell GTT BidTriggerAtRate Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            let execInstString = "BidTrigger,AtRate,OkToCross";
            let execInst = '["BidTrigger","AtRate"]';

        	before(function (done) {
				console.log('*************************** Stop Sell GTT BidTriggerAtRate ************************** ' + new Date());
                coId = "StopGTTSellBidTrigAtRate"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"'+coId+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.STOP + '", "price":"' + orderData.stopLmtSellPrice + '", "stopPrice" : "' + orderData.stopSellAtPrice + '", "side":"' + orderData.SELL + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTT + '","expiryTime":"' + orderData.ExpiryTime + '"}]}'
            	console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
							done()
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
				}
			});

			it("Stop Sell GTT BidTriggerAtRate Order Test", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.STOP, orderFilled.type);
                    assert.equal(orderData.GTT, orderFilled.timeInForce);
                    assert.equal(orderData.SELL, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.stopPrice);
                    assert.exists(orderFilled.expiryTime);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Stop Buy GTC OfferTriggerAtRate Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            let execInstString = "OfferTrigger,AtRate,ProviderPriority,OkToCross";
            let execInst = '["OfferTrigger","AtRate","ProviderPriority"]';

        	before(function (done) {
				console.log('*************************** Stop Buy GTC Buy OfferTriggerAtRate ************************** ' + new Date());
                coId = "StopGTCBuyOfferTrigAtRate"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"'+coId+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.STOP + '", "price":"' + orderData.stopLmtBuyPrice + '", "stopPrice" : "' + orderData.stopBuyAtPrice + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTC + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
							done()
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
				}
			});

			it("Stop Buy GTC OfferTriggerAtRate Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.STOP, orderFilled.type);
                    assert.equal(orderData.GTC, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.stopPrice);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.exists(orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Stop GTT Buy OfferTriggerAtRate Order Test ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            let execInstString = "OfferTrigger,AtRate,SizePriority,OkToCross";
            let execInst = '["OfferTrigger","AtRate","SizePriority"]';

        	before(function (done) {
				console.log('*************************** Stop GTT Buy OfferTriggerAtRate ************************** ' + new Date());
                coId = "StopGTTBuyOfferTrigAtRate"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"'+coId+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.STOP + '", "price":"' + orderData.stopLmtBuyPrice + '", "stopPrice" : "' + orderData.stopBuyAtPrice + '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.GTT  + '","expiryTime":"' + orderData.ExpiryTime + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
							done()
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
				}
			});

			it("Stop Buy GTT OfferTriggerAtRate Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.STOP, orderFilled.type);
                    assert.equal(orderData.GTT, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.expiryTime);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.stopPrice);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.exists(orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.exists(orderFilled.lastQty);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});

		describe("Stop DAY Buy Term BidTriggerAtRate Order Test ", function () {
		// since order is in term, limit and stop trigger price is marked in sell for the order to get filled
		// order gets filled in EMs, orders in term ccy will not goto ME
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

             let execInst = '["BidTrigger","AtRate"]';
            let execInstString = "BidTrigger,AtRate,OkToCross"

        	before(function (done) {
				console.log('*************************** Stop DAY Buy Term OfferTriggerAtRate ************************** ' + new Date());
                coId = "StopDAYBuyTermOfferTriggerAtRate"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"'+coId+'","currency":"' + orderData.termCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.STOP + '", "price":"' + orderData.stopLmtSellPrice + '", "stopPrice" : "' + orderData.stopSellAtPrice +  '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
							done()
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
    				}	else if (res.errors) {
                            InvalidOrderRequest = res.errors;
                           	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
                    }
                }
			});

			it("Stop Buy DAY Term OfferTriggerAtRate Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.STOP, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.stopPrice);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else {
                    console.log ("order status - Order didnt get filled")
                    assert.equal("true","false")
                }
			});
		});
/*
//For future implementation of NoCross
        describe("Stop DAY Buy Term V4 Order Test ", function () {
        // This is failing, need to handle this
        // Ideally if OKToCross is not mentioned, it should goto ME, but the current UIG implementation doesnt check  OKToCross as mandatory flag.
        // But since order is in term ccy, this will not be sent to ME. Finally order gets cancelled when user logs out.
        //Below logs has the details
  // OA Log
  //2020-11-14 10:27:27,855  INFO inMessagesProcessor UserMessageWorker1 OrderBookC #[user2@MAIN.XCN1139] OrderBookC.StopOrderProcessor - Trigger reached. (Offer Order)(MP < SP) . ID 4744204192 SP -> 1.18 MP -> 1.1863 TriggerType -> 0
  //2020-11-14 10:27:27,855  INFO inMessagesProcessor UserMessageWorker1 OrderBookC #[user2@MAIN.XCN1139] 4744204192 LP_Crossing_Disabled. Match_Not_Triggered. BOOK_NAME=XCN1139+EUR/USD
//2020-11-14 10:27:27,859  INFO SecondaryExecutorTH-8 RateSubscriptionManagerC #[subscriptionUserZZZ@MAIN.XCN1139] RateSubsMgrC.subs.info : Rates subscription failed. RiskNet subs and matching turned off. Request=FXI9082616248,Key=subscriptionUserZZZ@MAIN.XCN1139EUR/USDREXtest

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

            let execInst = '["BestPrice"]';
            let execInstString = "BestPrice,OkToCross"
        	before(function (done) {
				console.log('*************************** Stop DAY Sell Term V4 ExecFlag - NoCross ************************** ' + new Date());
                coId = "StopDAYBuyTermExecFlagNoCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"'+coId+'","currency":"' + orderData.termCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.STOP + '", "price":"' + orderData.stopLmtSellPrice + '", "stopPrice" : "' + orderData.stopSellAtPrice +  '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					let res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                        console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
					}
				}
			});

			it("Stop DAY Sell Term V4 ExecFlag NoCross Order Test", function () {
				console.log('Order response: ' + JSON.stringify(orderFilled));
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.STOP, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.stopPrice);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else if (orderRejected) {
    				console.log('orderRejected response: ' + JSON.stringify(orderRejected));
                    assert.equal(coId, orderRejected.coId);
                    assert.equal(orderData.STOP, orderRejected.type);
                    assert.equal(orderData.DAY, orderRejected.timeInForce);
                    assert.equal(orderData.BUY, orderRejected.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderRejected.currency);
                    assert.equal(orderData.symbol_EURUSD, orderRejected.symbol);
                    assert.equal(orderData.size, orderRejected.size);
                    assert.equal(userData.orgname, orderRejected.org);
                    assert.equal(execInstString, orderRejected.execFlags);
                    assert.equal('0', orderRejected.averagePrice);
                    assert.equal('0', orderRejected.cumQty);
                    assert.equal('0', orderRejected.execId);
                    assert.equal('0', orderRejected.lastPrice);
                    assert.equal('0', orderRejected.lastQty);
                    assert.equal('0', orderRejected.orderId);
                    assert.equal(orderData.REJECTED, orderRejected.executionType);
                    assert.equal("0", orderRejected.leavesQty);
                    assert.equal(orderData.ACTION, orderRejected.action);
                    assert.equal(orderData.REJECTED, orderRejected.status);
                    assert.equal("Request.Validation.Term.Currency.Directed.Order.Not.Allowed", orderRejected.reason);
                } else {
                    assert.equal('True','False');
                    console.log ("order status - Order didnt get filled")
                }
            });
		});
*/
	});
};

// Negative scenarios
let NegativeStopOrderTC = function () {
 	describe("Negative Stop Order TCs - ", function () {
 		before(function (done) {
 			wsconnect(done);
 		});

 		after(function () {
 			connection.close()
 		});


		describe("StopOrder Negative Scenario - Null Currency ", function () {
// This is not working as expected. Below info is seen in OA logs, but not reaching UIG
//        2020-12-22 10:08:43,028  INFO inMessagesProcessor UserMessageWorker3 FXFIXHandler43 #[user2@MAIN.XCN1139] FH.onMessage order NullCurrency could not be processed as following error was generated RequestValidationError.InvalidDealtCcy
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Null Currency ************************** ' + new Date());
                coId = "NullCurrency"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice" : "1.2", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
                            done()
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
                            done()
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" StopOrder Negative Scenario - Null Currency ", function () {
				console.log('Order response : ' + JSON.stringify(orderData.RECEIVED));
//				assert.equal('RequestValidationError.InvalidDealtCcy', orderFailed.reason);
                assert.equal(orderData.RECEIVED, orderReceived.status);
			});
		});


		describe(" StopOrder Negative Scenario - Blank coID ", function () {
// This is not working as expected. Below info is seen in OA logs, but not reaching UIG
//2020-12-22 10:47:19,390  INFO inMessagesProcessor UserMessageWorker4 FXFIXHandler43 #[user2@MAIN.XCN1139] FH.onMessage order  could not be processed as following error was generated RequestValidationError.InvalidClOrdID
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Blank coID ************************** ' + new Date());
                coId = "BlankcoId"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" StopOrder Negative Scenario - Blank coID ", function () {
				console.log('Order response : ' + JSON.stringify(orderFailed.reason));
                assert.equal(orderData.FAILED, orderFailed.status);
			});
		});

		describe(" StopOrder Negative Scenario - Invalid Currency ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let orderFailed;
            let orderRejected;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Invalid Currency ************************** ' + new Date());
                coId = "InvalidCurrency"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"XXX", "size":"100000", "type":"Stop", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Invalid Currency ", function () {
//                console.log('Order response : ' + JSON.stringify(orderFailed.reason));
//                assert.equal(orderData.FAILED, orderFailed.status);
   				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidDealtCcy', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

	    describe(" StopOrder Negative Scenario - Invalid CP ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** Negative Scenario - Invalid CP ************************** ' + new Date());
                coId = "StopGTCInvalidCP"+parseInt(reqId)
                console.log("coId = " + coId)

				connection.send('{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"Stop", "side":"Sell", "symbol":"XXX/USD", "execFlags" : [], "timeInForce":"GTC"}]}')
				console.log('{"order request ":[{"coId":"StopGTCInvalidCP1","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"XXX/USD", "execFlags" : [], "timeInForce":"GTC"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it(" StopOrder Negative Scenario - Invalid CP ", function () {
				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidCurrencyPair', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

		describe(" StopOrder Negative Scenario - Incorrect Case Symbol ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Incorrect Case Symbol ************************** ' + new Date());
                coId = "IncorrectCaseSymbol"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"eur/usd", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Incorrect Case Symbol ", function () {
				console.log('Order response : ' + JSON.stringify(orderRejected.reason));
				assert.equal('RequestValidationError.InvalidCurrencyPair', orderRejected.reason);
                assert.equal(orderData.REJECTED, orderRejected.status);
			});
		});

		describe(" StopOrder Negative Scenario - Invalid Amount ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Invalid Amount ************************** ' + new Date());
                coId = "InvalidAmount"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"x00000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Invalid Amount ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" StopOrder Negative Scenario - Null Amount ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('***************************StopOrder Negative Scenario - Null Amount ************************** ' + new Date());
                coId = "NullAmount"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":" ", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Null Amount ", function () {
				console.log('Order response : ' + JSON.stringify(orderFailed.reason));
                assert.equal(orderData.FAILED, orderFailed.status);
            });
		});

		describe(" StopOrder Negative Scenario - Invalid TIF ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Invalid TIF ************************** ' + new Date());
                coId = "StopGTCInvalidTIF"+parseInt(reqId)
                console.log("coId = " + coId)

				connection.send('{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"TIF"}]}')
				console.log('{"orders":[{"coId":"StopGTCInvalidTIF","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"TIF"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
 						if (orderResponse.status === 'RECEIVED') {
 							orderReceived = orderResponse;
                             console.log("order Received : " + JSON.stringify(orderReceived));
 						} else if (orderResponse.status === 'PENDING_NEW') {
 							orderPendingNew = orderResponse;
                             console.log("order Pending New : " + JSON.stringify(orderPendingNew));
 						} else if (orderResponse.status === 'NEW') {
 							orderNew = orderResponse;
                             console.log("order New : " + JSON.stringify(orderNew));
 						} else if (orderResponse.status === 'FILLED') {
 							orderFilled = orderResponse;
 							console.log("orderFilled =" + JSON.stringify(orderFilled))
 							//done()
 						} else if (orderResponse.status === 'REJECTED') {
 							orderRejected = orderResponse;
 							console.log("orderRejected =" + JSON.stringify(orderRejected))
 							//done()
 						}
 					} else if (res.errors) {
 						    InvalidOrderRequest = res.errors;
 						    console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
 					}
				}
			});

			it("StopOrder Negative Scenario - Invalid TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" StopOrder Negative Scenario - Incorrect Case TIF ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Incorrect Case TIF ************************** ' + new Date());
                coId = "IncorrectCaseTIF"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"SELL", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"gtc"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Incorrect Case TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" StopOrder Negative Scenario - Invalid side ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Invalid side  ************************** ' + new Date());
                coId = "StopGTCInvalidSide"+parseInt(reqId)
                console.log("coId = " + coId)

				connection.send('{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sebi", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}')
				console.log('{"orders":[{"coId":"StopGTCInvalidSide","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"Sebi", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}')
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
 						if (orderResponse.status === 'RECEIVED') {
 							orderReceived = orderResponse;
                             console.log("order Received : " + JSON.stringify(orderReceived));
 						} else if (orderResponse.status === 'PENDING_NEW') {
 							orderPendingNew = orderResponse;
                             console.log("order Pending New : " + JSON.stringify(orderPendingNew));
 						} else if (orderResponse.status === 'NEW') {
 							orderNew = orderResponse;
                             console.log("order New : " + JSON.stringify(orderNew));
 						} else if (orderResponse.status === 'FILLED') {
 							orderFilled = orderResponse;
 							console.log("orderFilled =" + JSON.stringify(orderFilled))
 							//done()
 						} else if (orderResponse.status === 'REJECTED') {
 							orderRejected = orderResponse;
 							console.log("orderRejected =" + JSON.stringify(orderRejected))
 							//done()
 						}
 					} else if (res.errors) {
 						    InvalidOrderRequest = res.errors;
 						    console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                            done()
 					}
				}
			});

			it("StopOrder Negative Scenario - Invalid TIF ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});

		describe(" StopOrder Negative Scenario - Incorrect Case Side ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Incorrect Case Side ************************** ' + new Date());
                coId = "IncorrectCaseSide"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"Stop", "price":"1.1", "stopPrice":"1.2", "side":"SELL", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Incorrect Case Side ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});
		});


// NoCross tc is failing since no response comes and order stays NEW
		describe(" StopOrder Negative Scenario - NoCross ", function () {
        // Not able to simulate the scenario where order doesnt get matched in EMS and gets forwarded to ME.
        //OkToCross is considered by defaul, there is a change in the implementation
        //In future when we dont want an order to be crossed with LP, we might have to specify NoCross

            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;
            let NoFill;
            let orderRejected;

			let res;
			let execInst = '["BestPrice"]';
			let execInstString = "BestPrice,OkToCross";

			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - NoCross ************************** ' + new Date());
                coId = "StopDAYBuyExecFlagNoCross"+parseInt(reqId)
                console.log("coId = " + coId)

            	order = '{"orders":[{"coId":"'+coId+'","currency":"' + orderData.baseCurrency_EURUSD + '", "size":"'+ orderData.size + '", "type":"' + orderData.STOP + '", "price":"' + orderData.stopLmtBuyPrice + '", "stopPrice" : "' + orderData.stopSellAtPrice +  '", "side":"' + orderData.BUY + '", "symbol":"' + orderData.symbol_EURUSD + '", "execFlags" : ' + execInst + ', "timeInForce":"' + orderData.DAY + '"}]}'
                console.log("order request : " + order);
				connection.send(order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
 					if (res.orderResponses) {
 						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
                            done()
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						} else if (orderResponse.status === 'FAILED') {
							orderFailed = orderResponse;
							console.log("orderFailed =" + JSON.stringify(orderFailed))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        //done()
                    } else {
                        NoFill = "order didnt get filled"
                        console.log("order didnt get filled")
                    }
				}
			});

			it("StopOrder Negative Scenario - NoCross ", function () {
				if (orderFilled) {
                    assert.equal(coId, orderFilled.coId);
                    assert.equal(orderData.STOP, orderFilled.type);
                    assert.equal(orderData.DAY, orderFilled.timeInForce);
                    assert.equal(orderData.BUY, orderFilled.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.currency);
                    assert.equal(orderData.symbol_EURUSD, orderFilled.symbol);
                    assert.equal(orderData.size, orderFilled.size);
                    assert.equal(userData.orgname, orderFilled.org);
                    assert.exists(orderFilled.price);
                    assert.exists(orderFilled.stopPrice);
                    assert.equal(execInstString, orderFilled.execFlags);
                    assert.exists(orderFilled.averagePrice);
                    assert.equal(orderData.size, orderFilled.cumQty);
                    assert.exists(orderFilled.execId);
                    assert.exists(orderFilled.lastPrice);
                    assert.exists(orderFilled.orderId);
                    assert.exists(orderFilled.valueDate);
                    assert.exists(orderFilled.tradeDate);
                    assert.exists(orderFilled.settlCurrAmt);
                    assert.exists(orderFilled.counterParty);
                    assert.equal(orderData.size, orderFilled.lastQty);
                    assert.equal(orderData.baseCurrency_EURUSD, orderFilled.settlCurrency);
                    assert.equal(orderData.executionType, orderFilled.executionType);
                    assert.equal("0", orderFilled.leavesQty);
                    assert.equal(orderData.ACTION, orderFilled.action);
                    assert.equal(orderData.FILLED, orderFilled.status);
                } else if (orderRejected) {
                    console.log('orderRejected response: ' + JSON.stringify(orderRejected));
                    assert.equal(coId, orderRejected.coId);
                    assert.equal(orderData.STOP, orderRejected.type);
                    assert.equal(orderData.DAY, orderRejected.timeInForce);
                    assert.equal(orderData.BUY, orderRejected.side);
                    assert.equal(orderData.baseCurrency_EURUSD, orderRejected.currency);
                    assert.equal(orderData.symbol_EURUSD, orderRejected.symbol);
                    assert.equal(orderData.size, orderRejected.size);
                    assert.equal('0', orderRejected.averagePrice);
                    assert.equal(userData.orgname, orderRejected.org);
                    assert.equal(execInstString, orderRejected.execFlags);
                    assert.equal('0', orderRejected.cumQty);
                    assert.equal('0', orderRejected.execId);
                    assert.equal('0', orderRejected.lastPrice);
                    assert.equal('0', orderRejected.lastQty);
                    assert.equal('0', orderRejected.orderId);
                    assert.equal(orderData.REJECTED, orderRejected.executionType);
                    assert.equal("0", orderRejected.leavesQty);
                    assert.equal(orderData.ACTION, orderRejected.action);
                    assert.equal(orderData.REJECTED, orderRejected.status);
                    assert.equal("Request.Validation.Term.Currency.Directed.Order.Not.Allowed", orderRejected.reason);
                } else {
                    assert.equal('True','False');
                    console.log ("order status - Order didnt get filled")
                }
			});
		});

		describe(" StopOrder Negative Scenario - Invalid Trade Type ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Invalid Trade Type ************************** ' + new Date());
                coId = "InvalidTradeType"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"MarX", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'

				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Invalid Trade Type ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', res.errors[0].errorMessage);
                assert.equal('1', res.errors[0].errorCode);
			});
		});

		describe(" StopOrder Negative Scenario - Incorrect Case Trade Type ", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Incorrect Case Trade Type ************************** ' + new Date());
                coId = "IncorrectCaseTradeType"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"STOP", "price":"1.1", "stopPrice":"1.2", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Incorrect Case Trade Type ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});

		});

		describe(" StopOrder Negative Scenario - Missing StopPrice", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Missing StopPrice ************************** ' + new Date());
                coId = "MissingStopPrice"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"STOP", "price":"1.1", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Missing StopPrice ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});

		});

		describe(" StopOrder Negative Scenario - Null StopPrice", function () {
            let orderReceived;
            let orderPendingNew;
            let orderNew;
            let orderFilled;
            let order ;

			let res;
			let orderResponses;
			before(function (done) {
				console.log('*************************** StopOrder Negative Scenario - Null StopPrice ************************** ' + new Date());
                coId = "NullStopPrice"+parseInt(reqId)
                console.log("coId = " + coId)

				let order = '{"orders":[{"coId":"'+coId+'","currency":"EUR", "size":"100000", "type":"STOP", "price":"1.1", "StopPrice":"", "side":"Sell", "symbol":"EUR/USD", "execFlags" : [], "timeInForce":"GTC"}]}'
				connection.send(order)
				console.log("order request : " + order);
				connection.onmessage = (e) => {
					res = JSON.parse(e.data)
					console.log("res =" + JSON.stringify(res))
					if (res.orderResponses) {
						let orderResponse = res.orderResponses[0];
						if (orderResponse.status === 'RECEIVED') {
							orderReceived = orderResponse;
                            console.log("order Received : " + JSON.stringify(orderReceived));
						} else if (orderResponse.status === 'PENDING_NEW') {
							orderPendingNew = orderResponse;
                            console.log("order Pending New : " + JSON.stringify(orderPendingNew));
						} else if (orderResponse.status === 'NEW') {
							orderNew = orderResponse;
                            console.log("order New : " + JSON.stringify(orderNew));
						} else if (orderResponse.status === 'FILLED') {
							orderFilled = orderResponse;
							console.log("orderFilled =" + JSON.stringify(orderFilled))
							//done()
						} else if (orderResponse.status === 'REJECTED') {
							orderRejected = orderResponse;
							console.log("orderRejected =" + JSON.stringify(orderRejected))
							//done()
						}
					} else if (res.errors) {
                        InvalidOrderRequest = res.errors;
                      	console.log("InvalidOrderRequest =" + JSON.stringify(InvalidOrderRequest))
                        done()
                    }
 				}
			});

			it("StopOrder Negative Scenario - Null StopPrice ", function () {
				console.log('Order response : ' + JSON.stringify(InvalidOrderRequest[0].errorMessage));
				assert.equal('Not a valid request.', InvalidOrderRequest[0].errorMessage);
                assert.equal('1', InvalidOrderRequest[0].errorCode);
			});

		});

	});
};

StopOrderTC();
NegativeStopOrderTC();