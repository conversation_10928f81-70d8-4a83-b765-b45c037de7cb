const http = require('http')
const user = require('../config/properties').user
const env = require('../config/properties').env

let loginResponse

let login = function (done, callback){
	const data = JSON.stringify({
	  user: user.username,
	  org: user.orgname,
	  pass: user.password
	})
	
	const options = {
	  hostname: env.kongHost,
	  port: env.kongPort,
//	  path: '/fxi/fxiapi/sso/login',
	  path: '/sso/login',
	  method: 'POST',
	  headers: {
		'Content-Type': 'application/json',
		'Content-Length': data.length
	  }
	}
	
	const req = http.request(options, res => {
	  
	  res.on('data', d => {
		  loginResponse = JSON.parse(d)
		if(callback){
			callback(done, res.headers['set-cookie'])
		} else {
			done()
		}
		
        });
	  
	  res.on('end', function () {
          //console.log(str);
    });
	  
	
	})
	
	req.on('error', error => {
	  console.error(error)
	})

	req.write(data)
	req.end()
};

let getLoginResponse = function (){
	return loginResponse
}

module.exports = {
	login: login,
	getLoginResponse: getLoginResponse
}