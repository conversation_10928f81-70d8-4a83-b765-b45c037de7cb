

function orderRequest(org){
    let request = {
        coId: "test-" + new Date().getTime(),
        type: "Limit",
        side: "Buy",
        symbol: "EUR/USD",
        currency: "EUR",
        size: 1000000,
        price: 1.0,
        timeInForce: "GTT",
        expiryTime: 30,
    }
    let wrapper = {
        orders: [request],
    }
    return wrapper;
}

function suspendRequest(orderId, clOrderId){
    let request = {
        orderId: orderId,
        clOrderId: clOrderId,
    }
    let wrapper = {
        orderSuspendRequests: [request],
    }
    return wrapper;
}

function resumeRequest(orderId, clOrderId){
    let request = {
        orderId: orderId,
        clOrderId: clOrderId,
    }
    let wrapper = {
        orderResumeRequests: [request],
    }
    return wrapper;
}

function cancelRequest(clOrderId){
    let request = {
        action: "cancel",
        requestId: "cancel-" + new Date().getTime(),
        side: "Buy",
        symbol: "EUR/USD",
        size: "100000",
        coId: clOrderId,
    }
    let wrapper = {
        orders: [request],
    }
    return wrapper;
}

function suspendRequestWithoutOrderIdTag(orderId, clOrderId){
    let request = {
        //orderId: orderId,
        clOrderId: clOrderId,
    }
    let wrapper = {
        orderSuspendRequests: [request],
    }
    return wrapper;
}

function suspendRequestWithoutClOrdIdTag(orderId, clOrderId){
    let request = {
        orderId: orderId,
        //clOrderId: clOrderId,
    }
    let wrapper = {
        orderSuspendRequests: [request],
    }
    return wrapper;
}

module.exports = {
    orderRequest,
    suspendRequest,
    resumeRequest,
    cancelRequest,
    suspendRequestWithoutOrderIdTag,
    suspendRequestWithoutClOrdIdTag,
}