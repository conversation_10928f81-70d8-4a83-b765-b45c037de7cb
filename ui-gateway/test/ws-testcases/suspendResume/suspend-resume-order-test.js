const wsUtil = require('../utils/web-socket-util');
const assert = require('chai').assert;
const _ = require('lodash');
const commonUtil = require('../utils/common-util');
const orderUtil = require('./orderUtil');

let wsConnection;
const org = "restOAFI";


describe("order suspend and resume", function (){
    before(function (done) {
        wsConnection = wsUtil.wsConnect(org, done);
        console.log("Got connection");
    });

    after(function () {
        console.log("Going to close websocket connection");
        wsConnection.close();
    });


    it('Suspend NEW order', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        console.log("coId - " + coId);
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if (response.status === "NEW" && response.coId === coId) {
                    let suspendRequest = orderUtil.suspendRequest(response.orderId, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "SUCCESS" && response.clOrderId === coId) {
                    console.log("Suspended NEW order " + response.clOrderId);
                    completed = true;
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("did not receive suspend response");
                assert.fail("did not receive suspend response");
                done();
            }
        }, 2000);
    });

    it('Resume NEW order', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        console.log("Order request - " + JSON.stringify(request))
        wsConnection.send(JSON.stringify(request));
        wsConnection.onmessage = (e) => {
            console.log("order response - " + e.data);
            let msg = JSON.parse(e.data);
            if(msg.orderResponses){
                let response = msg.orderResponses[0];
                if(response.status === "NEW" && response.coId === coId) {
                    let suspendRequest = orderUtil.suspendRequest(response.orderId, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            }
            if(msg.orderSuspendResponses){
                let response = msg.orderSuspendResponses[0];
                if(response.status === "SUCCESS" && response.clOrderId === coId) {
                    console.log("Suspended NEW order : " + response.clOrderId);
                    let resumeRequest = orderUtil.resumeRequest(response.orderId, coId);
                    console.log("resume request - " + JSON.stringify(resumeRequest));
                    wsConnection.send(JSON.stringify(resumeRequest));
                }
            }
            if(msg.orderResumeResponses){
                let response = msg.orderResumeResponses[0];
                if(response.status === "SUCCESS" && response.clOrderId === coId) {
                    console.log("Resumed suspended NEW order : " + response.clOrderId);
                    completed = true;
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("did not receive resume response for suspended NEW order");
                assert.fail("did not receive resume response");
                done();
            }
        }, 2000);
    });

    it('Suspend EXPIRED order - Not possible ', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let order = request.orders[0];
        order.expiryTime = 1;
        let coId = request.orders[0].coId;
        console.log("Order request - " + JSON.stringify(request))
        wsConnection.send(JSON.stringify(request));
        wsConnection.onmessage = (e) => {
            console.log("Suspend/Resume an expired order - order response - " + e.data);
            let msg = JSON.parse(e.data);
            if(msg.orderResponses){
                let response = msg.orderResponses[0];
                if(response.status === "EXPIRED" && response.coId === coId) {
                    let suspendRequest = orderUtil.suspendRequest(response.orderId, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            }
            if(msg.orderSuspendResponses){
                let response = msg.orderSuspendResponses[0];
                if(response.status === "FAILED" && response.clOrderId === coId) {
                    assert.equal("Invalid order status", response.message, "suspend response message is not matching");
                    console.log("Order suspend failed : " + coId);
                    completed = true;
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("did not receive resume response");
                assert.fail("did not receive resume response");
                done();
            }
        }, 2000);
    });

    it('Resume EXPIRED order - Not possible ', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let order = request.orders[0];
        order.expiryTime = 1;
        let coId = request.orders[0].coId;
        console.log("Order request - " + JSON.stringify(request))
        wsConnection.send(JSON.stringify(request));
        wsConnection.onmessage = (e) => {
            console.log("order response - " + e.data);
            let msg = JSON.parse(e.data);
            if(msg.orderResponses){
                let response = msg.orderResponses[0];
                if(response.status === "NEW" && response.coId === coId) {
                    let suspendRequest = orderUtil.suspendRequest(response.orderId, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            }
            if(msg.orderSuspendResponses){
                let response = msg.orderSuspendResponses[0];
                if(response.status === "SUCCESS" && response.clOrderId === coId) {
                    console.log("Suspend order : " + coId);
                }
            }
            if(msg.orderResponses){
                let response = msg.orderResponses[0];
                if(response.status === "EXPIRED" && response.coId === coId) {
                    console.log("order expired " + coId);
                    let resumeRequest = orderUtil.resumeRequest(response.orderId, coId);
                    console.log("resume request - " + JSON.stringify(resumeRequest));
                    wsConnection.send(JSON.stringify(resumeRequest));
                }
            }
            if(msg.orderResumeResponses){
                let response = msg.orderResumeResponses[0];
                if(response.status === "FAILED" && response.clOrderId === coId) {
                    console.log("order resume failed " + coId);
                    completed = true;
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("did not receive resume response");
                assert.fail("did not receive resume response");
                done();
            }
        }, 2000);
    });

    it('Suspend CANCELLED order - Not possible', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let order = request.orders[0];
        order.timeInForce = "IOC";
        delete order.expiryTime;
        let coId = request.orders[0].coId;
        console.log("Order request - " + JSON.stringify(request))
        wsConnection.send(JSON.stringify(request));
        wsConnection.onmessage = (e) => {
            console.log("order response - " + e.data);
            let msg = JSON.parse(e.data);
            if(msg.orderResponses){
                let response = msg.orderResponses[0];
                if(response.status === "CANCELED" && response.requestId === coId) {
                    let suspendRequest = orderUtil.suspendRequest(response.orderId, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            }
            if(msg.orderSuspendResponses){
                let response = msg.orderSuspendResponses[0];
                if(response.status === "FAILED" && response.clOrderId === coId) {
                    console.log("order suspend failed " + coId);
                    completed = true;
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("did not receive suspend response");
                assert.fail("did not receive suspend response");
                done();
            }
        }, 2000);
    });

    it('Resume CANCELLED order - Not possible ', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let order = request.orders[0];
        order.expiryTime = 5;
        let coId = request.orders[0].coId;
        let orderId;
        console.log("Order request - " + JSON.stringify(request))
        wsConnection.send(JSON.stringify(request));
        wsConnection.onmessage = (e) => {
            console.log("order response - " + e.data);
            let msg = JSON.parse(e.data);
            if(msg.orderResponses){
                let response = msg.orderResponses[0];
                if(response.status === "NEW" && response.coId === coId) {
                    orderId = response.orderId;
                    let suspendRequest = orderUtil.suspendRequest(response.orderId, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            }
            if(msg.orderSuspendResponses){
                let response = msg.orderSuspendResponses[0];
                if(response.status === "SUCCESS" && response.clOrderId === coId) {
                    console.log("order suspended : " + coId);
                    cancelRequest = orderUtil.cancelRequest(coId);
                    console.log("cancel request - " + JSON.stringify(cancelRequest));
                    wsConnection.send(JSON.stringify(cancelRequest));
                }
            }
            if(msg.orderResponses){
                let response = msg.orderResponses[0];
                if(response.status === "CANCELED" && response.orderId === orderId) {
                    console.log("order cancelled " + coId);
                    let resumeRequest = orderUtil.resumeRequest(response.orderId, coId);
                    console.log("resume request - " + JSON.stringify(resumeRequest));
                    wsConnection.send(JSON.stringify(resumeRequest));
                }
            }
            if(msg.orderResumeResponses){
                let response = msg.orderResumeResponses[0];
                if(response.status === "FAILED" && response.clOrderId === coId) {
                    console.log("order resume failed - " + orderId);
                    completed = true;
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("did not receive resume response");
                assert.fail("did not receive resume response");
                done();
            }
        }, 2000);
    });

    it('Suspend Partially Filled order', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        console.log("coId - " + coId);
        let order = request.orders[0];
        order.size = **********;
        order.type = "Market";
        order.expiryTime = 120;
        order.preferredProviders = ["NTFX","VISA"];
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));

        let SUSPENDED = true;

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if(SUSPENDED) {
                    if (response.status === "PARTIALLY_FILLED" && response.coId === coId) {
                    SUSPENDED = false;
                        let suspendRequest = orderUtil.suspendRequest(response.orderId, coId);
                        console.log("suspend request - " + JSON.stringify(suspendRequest));
                        wsConnection.send(JSON.stringify(suspendRequest));

                    }
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "SUCCESS" && response.clOrderId === coId) {
                    console.log("Suspended PARTIALLY_FILLED order " + response.clOrderId);
                    completed = true;
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("did not receive suspend or resume response of paritally filled order");
                assert.fail("did not receive suspend or resume response of paritally filled order");
                done();
            }
        }, 2000);
    });

    it('Resume Partially Filled suspended order', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        console.log("coId - " + coId);
        let order = request.orders[0];
        order.size = **********;
        order.type = "Market";
        order.expiryTime = 120;
        order.preferredProviders = ["NTFX","VISA"];
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));

        let SUSPENDED = true;

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if(SUSPENDED) {
                    if (response.status === "PARTIALLY_FILLED" && response.coId === coId) {
                    SUSPENDED = false;
                        let suspendRequest = orderUtil.suspendRequest(response.orderId, coId);
                        console.log("suspend request - " + JSON.stringify(suspendRequest));
                        wsConnection.send(JSON.stringify(suspendRequest));

                    }
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "SUCCESS" && response.clOrderId === coId) {
                    console.log("Suspended PARTIALLY_FILLED order " + response.clOrderId);
                    let resumeRequest = orderUtil.resumeRequest(response.orderId, coId);
                    console.log("resume request - " + JSON.stringify(resumeRequest));
                    wsConnection.send(JSON.stringify(resumeRequest));
                    //completed = true;
                    //done();
                }
            } else if(msg.orderResumeResponses){
                let response = msg.orderResumeResponses[0];
                if(response.status === "SUCCESS" && response.clOrderId === coId) {
                    console.log("Resumed suspended PARTIALLY_FILLED order : " + response.clOrderId);
                    completed = true;
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("did not receive suspend or resume response of paritally filled order");
                assert.fail("did not receive suspend or resume response of paritally filled order");
                done();
            }
        }, 2000);
    });

})


describe("order suspend and resume - Negative scenarios", function (){
    before(function (done) {
        wsConnection = wsUtil.wsConnect(org, done);
        console.log("Got connection");
    });

    after(function () {
        console.log("Going to close websocket connection");
        wsConnection.close();
    });

    it('Invalid OrderId', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        console.log("coId - " + coId);
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if (response.status === "NEW" && response.coId === coId) {
                    let suspendRequest = orderUtil.suspendRequest(12345566, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "FAILED" && response.clOrderId === coId) {
                    console.log("Suspended NEW order failed " + response.clOrderId);
                    completed = true;
                    assert.equal(response.message, "Invalid orderId", "error message is not correct");
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("Suspend with invalid orderId - didnt fail");
                assert.fail("Suspend with invalid orderId - didnt fail");
                done();
            }
        }, 2000);
    });

    it('Null OrderId', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        console.log("coId - " + coId);
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if (response.status === "NEW" && response.coId === coId) {
                    let suspendRequest = orderUtil.suspendRequest("", coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "FAILED" && response.clOrderId === coId) {
                    console.log("Suspended NEW order failed " + response.clOrderId);
                    completed = true;
                    assert.equal(response.message, "Invalid orderId", "error message is not correct");
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("Suspend with Null orderId - didnt fail");
                assert.fail("Suspend with Null orderId - didnt fail");
                done();
            }
        }, 2000);
    });

    it('Invalid coId', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        console.log("coId - " + coId);
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));
        let orderId = "";

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if (response.status === "NEW" && response.coId === coId) {
                    orderId = response.orderId
                    let suspendRequest = orderUtil.suspendRequest(response.orderId, "invalidOrd1");
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "FAILED" && response.orderId === orderId) {
                    console.log("Suspend NEW order failed " + response.clOrderId);
                    completed = true;
                    assert.equal(response.message, "Invalid clOrderId", "error message is not correct");
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("Suspend with invalid coId - didnt fail");
                assert.fail("Suspend with invalid clOrderId");
                done();
            }
        }, 2000);
    });

    it('Null coId', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        let orderId = "";
        console.log("coId - " + coId);
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if (response.status === "NEW" && response.coId === coId) {
                    orderId = response.orderId;
                    let suspendRequest = orderUtil.suspendRequest(response.orderId, "");
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "FAILED" && response.orderId === orderId) {
                    console.log("Suspended NEW order failed " + response.clOrderId);
                    completed = true;
                    assert.equal(response.message, "Invalid clOrderId", "error message is not correct");
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("Suspend with Null coId - didnt fail");
                assert.fail("Suspend with Null coId - didnt fail");
                done();
            }
        }, 2000);
    });

    it('Without OrderId tag', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        console.log("coId - " + coId);
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if (response.status === "NEW" && response.coId === coId) {
                    let suspendRequest = orderUtil.suspendRequestWithoutOrderIdTag(response.orderId, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "FAILED" && response.clOrderId === coId) {
                    console.log("Suspended NEW order failed " + response.clOrderId);
                    completed = true;
                    assert.equal(response.message, "orderId and clOrderId are required");
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("Suspend with out orderId - didnt fail");
                assert.fail("Suspend with out orderId - didnt fail");
                done();
            }
        }, 2000);
    });

    it('Without clOrderId tag', function (done) {
        let completed = false;
        let request = orderUtil.orderRequest(org);
        let coId = request.orders[0].coId;
        let orderId = "";

        console.log("coId - " + coId);
        console.log("Order request - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));

        wsConnection.onmessage = (e) => {
            let msg = JSON.parse(e.data);
            console.log("order response - " + JSON.stringify(msg));
            if (msg.orderResponses) {
                let response = msg.orderResponses[0];
                if (response.status === "NEW" && response.coId === coId) {
                    orderId = response.orderId;
                    let suspendRequest = orderUtil.suspendRequestWithoutClOrdIdTag(response.orderId, coId);
                    console.log("suspend request - " + JSON.stringify(suspendRequest));
                    wsConnection.send(JSON.stringify(suspendRequest));
                }
            } else if (msg.orderSuspendResponses) {
                let response = msg.orderSuspendResponses[0];
                if (response.status === "FAILED" && response.orderId === orderId) {
                    console.log("Suspended NEW order failed " + response.clOrderId);
                    completed = true;
                    assert.equal(response.message, "orderId and clOrderId are required");
                    done();
                }
            }
        }
        setTimeout(function (){
            if(!completed) {
                console.log("Suspend with out clOrdId - didnt fail");
                assert.fail("Suspend with out clOrdId - didnt fail");
                done();
            }
        }, 2000);
    });

})
