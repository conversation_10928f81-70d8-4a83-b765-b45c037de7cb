const assert = require('chai').assert;
const expect = require('chai').expect;
function subscriptionRequest(org) {
    return {
        portfolioPriceRequests: [{
            portfolioLegs: [{
                id: "trade1",
                amount: 1000000,
                currencyPair: "EUR/USD",
                dealtCurrency: "EUR",
                legalEntity: org,
                side: 1,
                tenor: "SPOT",
            }, {
                id: "trade2",
                amount: 2000000,
                currencyPair: "EUR/USD",
                dealtCurrency: "EUR",
                legalEntity: org,
                side: 0,
                tenor: "1W",
            }],
            allocation: false,
            expiryInSecs: 120,
            tradeChannel: "REACT/RFS/PTA",
            sef: false,
            blockTrade: false,
            broadcast: false,
            providers: ["VISA"],
            customerOrg: org,
            clOrderId: "PRequest-" + new Date().getTime(),
        }]
    }
}

function withdrawalRequest(clOrderId, portfolioId) {
    return {
        portfolioWithdrawRequests: [{
            clOrderId: clOrderId,
            portfolioId: portfolioId,
        }]
    }
}
function tradeRequest(clOrderId, portfolioId, quoteId) {
    return {
        portfolioTradeRequests: [{
            portfolioId: portfolioId,
            clOrderId: clOrderId,
            quoteId: quoteId,
            tradeChannel: "REACT/RFS/PTA",
            allocation: false,
        }]
    }
}

function validatePriceLeg(portfolioLeg, priceLeg) {
    assert.equal(portfolioLeg.id, priceLeg.id);
    assert.equal(portfolioLeg.amount, priceLeg.amount);
    assert.equal(portfolioLeg.currencyPair, priceLeg.currencyPair);
    assert.equal(portfolioLeg.dealtCurrency, priceLeg.dealtCurrency);
    assert.equal(portfolioLeg.side, priceLeg.side);
    assert.equal(portfolioLeg.tenor, priceLeg.tenor);
    assert.isAbove(priceLeg.rate, 0);
}

function validatePortfolioPrice(portfolioPriceRequest, portfolioPrice) {
    assert.equal(portfolioPriceRequest.clOrderId, portfolioPrice.clOrderId);
    //validate portfolio request providers include price org
    expect(portfolioPriceRequest.providers).to.include(portfolioPrice.org);
    assert.equal(portfolioPriceRequest.expiryInSecs, portfolioPrice.expiration);
}

module.exports = {
    subscriptionRequest,
    validatePriceLeg,
    validatePortfolioPrice,
    withdrawalRequest,
    tradeRequest,
}