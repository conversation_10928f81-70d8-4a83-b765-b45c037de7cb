const wsUtil = require('../utils/web-socket-util');
const batchTradeUtil = require('./batch-trade-util');
const assert = require('chai').assert;
const _ = require('lodash');
const commonUtil = require('../utils/common-util');

let wsConnection;
const org = "pfOrg";

describe("batch trade subscription and prices", function (){
    before(function (done) {
        wsConnection = wsUtil.wsConnect(org, done);
        console.log("Got connection");
    });

    after(function () {
        console.log("Going to close websocket connection");
        wsConnection.close();
    });

    it('should be able to subscribe', function (done) {
        let request = batchTradeUtil.subscriptionRequest(org);
        let req = request.portfolioPriceRequests[0];
        console.log("subscriptionRequest - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));
        wsConnection.onmessage = (e) => {
            console.log("subscriptionResponse - " + e.data);
            let msg = JSON.parse(e.data);
            if(msg.portfolioPriceResponses){
                let response = msg.portfolioPriceResponses[0];
                if(response.clOrderId !== req.clOrderId) return;
                assert.equal(response.status, "OK");
                assert.equal(response.portfolioMessage.eventName, "Portfolio Submitted");
                //cancel subscription
                let withdrawRequest = batchTradeUtil.withdrawalRequest(response.clOrderId, response.portfolioId);
                wsConnection.send(JSON.stringify(withdrawRequest));
                done();
            }
        }
    });

    it('should get prices', function (done) {
        let request = batchTradeUtil.subscriptionRequest(org);
        let req = request.portfolioPriceRequests[0];
        console.log("subscriptionRequest - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));
        let priceProcessed = false;
        wsConnection.onmessage = (e) => {
            console.log("pricesResponse - " + e.data);
            let msg = JSON.parse(e.data);
            if(msg.portfolioPrices && msg.portfolioPrices[0].clOrderId === req.clOrderId){
                let prices = msg.portfolioPrices[0];
                if(priceProcessed) return;
                else priceProcessed = true;
                batchTradeUtil.validatePortfolioPrice(req, prices);
                let portfolioLegs = req.portfolioLegs;
                let priceLegs = prices.priceLegs;
                /*
                 * Iterate through each element of portfolioLegs and compare with priceLegs
                 */
                for(let i = 0; i < portfolioLegs.length; i++){
                    let portfolioLeg = portfolioLegs[i];
                    let priceLeg = _.find(priceLegs, ['id', portfolioLeg.id]);
                    assert.isNotNull(priceLeg);
                    batchTradeUtil.validatePriceLeg(portfolioLeg, priceLeg);
                }
                //cancel subscription
                let withdrawRequest = batchTradeUtil.withdrawalRequest(prices.clOrderId, prices.portfolioId);
                wsConnection.send(JSON.stringify(withdrawRequest));
                done();
            }
        }

    });
    /*
     * send subscription request, wait for prices, then send withdraw request
     */
    it('should be able to withdraw', function (done) {
        let request = batchTradeUtil.subscriptionRequest(org);
        let req = request.portfolioPriceRequests[0];
        console.log("subscriptionRequest - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));
        let priceProcessed = false;
        wsConnection.onmessage = (e) => {
            console.log("pricesResponse - " + e.data);
            let msg = JSON.parse(e.data);
            let priceCount = 0;
            if (msg.portfolioPrices && msg.portfolioPrices[0].clOrderId === req.clOrderId) {
                priceCount++;
                if(priceProcessed) return;
                else priceProcessed = true;
                let prices = msg.portfolioPrices[0];
                let withdrawRequest = batchTradeUtil.withdrawalRequest(prices.clOrderId, prices.portfolioId);
                wsConnection.send(JSON.stringify(withdrawRequest));
            }
            if(msg.portfolioResponses && msg.portfolioResponses[0].clOrderId === req.clOrderId){
                //assert eventName is 'Portfolio Withdrawn'
                let response = msg.portfolioResponses[0];
                assert.equal(response.portfolioMessage.eventName, "Portfolio Withdrawn");
                priceCount = 0;
                //verify that not more than one price received after withdraw
                setTimeout(() => {
                    //assert that priceCount is not more than 1;
                    assert.isAtMost(priceCount, 1);
                    done();
                }, 1000);
            }
        }
    });

    /*
     * send subscription request, wait for prices, then send portfolioTradeRequest, wait for portfolioTradeResponse
     */
    it('should be able to trade', function (done) {
        let request = batchTradeUtil.subscriptionRequest(org);
        let req = request.portfolioPriceRequests[0];
        console.log("subscriptionRequest - " + JSON.stringify(request));
        wsConnection.send(JSON.stringify(request));
        let priceProcessed = false;
        wsConnection.onmessage = (e) => {
            console.log("portfolioTradeResponse - " + e.data);
            let msg = JSON.parse(e.data);
            if (msg.portfolioPrices && msg.portfolioPrices[0].clOrderId === req.clOrderId) {
                if(priceProcessed) return;
                else priceProcessed = true;
                //get quoteId from the portfolioPrice and send portfolioTradeRequest
                let prices = msg.portfolioPrices[0];
                let quoteId = prices.quoteId;
                let portfolioTradeRequest = batchTradeUtil.tradeRequest(prices.clOrderId, prices.portfolioId, quoteId);
                wsConnection.send(JSON.stringify(portfolioTradeRequest));
            }
            if(msg.portfolioTradeResponses && msg.portfolioTradeResponses[0].clOrderId === req.clOrderId){
                //assert portfolioMessage.eventName is 'Portfolio Trade Verified'
                let response = msg.portfolioTradeResponses[0];
                assert.equal(response.portfolioMessage.eventName, "Portfolio Trade Verified");
                let apiTrades = response.apiTrades;
                //assert apiTrades.length is 2
                assert.equal(apiTrades.length, 2);
                //validate apiTrades
                let trade1W = _.find(apiTrades, ['tenor', "1W"]);
                assert.isNotNull(trade1W);
                let tradeSpot = _.find(apiTrades, ['tenor', "SPOT"]);
                assert.isNotNull(tradeSpot);
                done();
            }
        }
    });
})