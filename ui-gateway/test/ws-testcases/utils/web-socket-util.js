const WebSocket = require("ws");
const env = require('../../config/properties').env

let wsConnect = function (org, done) {
    const websocket_url = 'wss://' + env.hostname + '/v2/fxstream'
    let key = org + '_apikey';
    let connection = new WebSocket(websocket_url, [], {
        'headers': {
            'Host': env.apiHost,
            'apikey': env.apikey
        }
    })

    connection.onopen = () => {
        console.log("Web Socket session started for " + org);
        done()
    }
    connection.onerror = (error) => {
        console.log("Error on Web Socket session for " + org, error);
    }
    connection.onclose = () => {
        console.log("Web Socket session closed for " + org);
    }
    return connection;
}

module.exports = {
    wsConnect,
}