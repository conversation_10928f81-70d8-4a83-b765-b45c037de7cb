============= Trade ticker ===============
Trade Ticker
// Trade for the logged in org with the subscribed ccyPair should be done after subscription to get the tradeticker details, otherwise tcs (below 2) will fail
// Incase of RestAPI, it gets the last trade details whereas WS needs trade after subscription.
{"tradeTickerSubscriptions":[{"symbol":"USD/CAD"}]}
Unsubscription
{"tradetickerUnsubscriptions":[{"symbol":"USD/CAD"}]}

2020-07-22T12:53:57.588Z: {"tradeticker":{"symbol":"USD/CAD","rate":"1.18955","time":1595422437481,"isBid":true}}


============= Benchmark =============
Subscription
{"fxbenchmarkSubscriptions":[{"symbol":"USD/CAD","priceSource":"FXB"}]}
Unsubscription
{"fxbenchmarkUnsubscriptions":[{"symbol":"USD/CAD","priceSource":"FXB"}]}

2020.07.20, 07:13:18.0186 UTC -> {"fxbenchmark":{"currencyPair":"AUD/USD","rate":0.78844,"guid":"G-4796976cd-1736b11d717-FXB-177ae70","timestamp":1595229198103}}


============= GMR =============
Subscription
{"fxbenchmarkSubscriptions":[{"symbol":"USD/CAD","priceSource":"GMR"}]}
Unsubscription
{"fxbenchmarkUnsubscriptions":[{"symbol":"USD/CAD","priceSource":"GMR"}]}

2020.07.20, 07:15:02.0606 UTC -> {"gridmidrate":{"currencyPair":"AUD/USD","rate":0.78844,"guid":"G-4796976cd-1736b136ee8-FXB-17a1b52","timestamp":1595229302504}}


============= Generic rate =============
subscription - FULL
{"rateSubscriptions":[{"symbol":"EUR/USD","type":"FULL","requestId":60}]}
Unsubscription
{"rateUnsubscriptions":[{"requestId":60}]}

subscription - RAW
{"rateSubscriptions":[{"symbol":"EUR/USD","type":"RAW","requestId":62}]}
Unsubscription
{"rateUnsubscriptions":[{"requestId":62}]}

subscription - TOB
{"rateSubscriptions":[{"symbol":"EUR/USD","type":"TOB","requestId":63}]}
Unsubscription
{"rateUnsubscriptions":[{"requestId":63}]}

2020.07.20, 10:09:27.0999 UTC -> {"rate":{"symbol":"EUR/USD","bid":[1.1863,1.18627,1.18626],"offer":[1.18638,1.1864,1.18641],"bidLimit":[3000000.0,4000000.0,6000000.0],"offerLimit":[3000000.0,4000000.0,6000000.0],"time":1595239767911,"requestId":60}}


============= Custom rate =============
Subscription
{"rateSubscriptions":[{"symbol":"EUR/USD","type":"VWAP","requestId":65,"org":"XCN1139","tiers":[7000000]}]}
Unsubscription
{"rateUnsubscriptions":[{"requestId":65}]}

2020.07.20, 10:10:44.0283 UTC -> {"rate":{"symbol":"EUR/USD","bid":[1.18627],"offer":[1.1864],"bidLimit":[7000000.0],"offerLimit":[7000000.0],"time":1595239844176,"requestId":65}}



============= Positions =============
{"positionSubscriptions" : [{"clientReferenceId": "html", "customerId": "user1", "customerOrg":"BUBBLESUCD", "fromValueDate": "05/04/2020", "toValueDate": "7/17/2030","pnlCurrency": "USD", "positionLevel":"ORG" , "inclCustomerPositions":"false"}]}
{"positionUnsubscriptions":[{"positionRequestId":478271281}]}

2020.07.20, 08:04:56.0301 UTC -> {"position":{"side":"Short","symbol":"USD/CHF","level":"ORG","levelValue":"CSDK1","netOpenBase":"15,000,000.00","netOpenterm":"12,906,962.70","rate":"0.8604642","realizedPnL":"0.00","unrealizedPnL":"(1,716,667.89)","pnlCurrency":"USD","children":[{"side":"Short","symbol":"USD/CHF","valueDate":"09/14/2021","level":"ORG","levelValue":"CSDK1","netOpenBase":"3,000,000.00","netOpenterm":"2,581,344.50","rate":"0.8604482","realizedPnL":"0.00","unrealizedPnL":"(343,383.02)","pnlCurrency":"USD"},{"side":"Short","symbol":"USD/CHF","valueDate":"10/14/2021","level":"ORG","levelValue":"CSDK1","netOpenBase":"3,000,000.00","netOpenterm":"2,581,337.10","rate":"0.8604457","realizedPnL":"0.00","unrealizedPnL":"(343,390.64)","pnlCurrency":"USD"},{"side":"Short","symbol":"USD/CHF","valueDate":"03/15/2021","level":"ORG","levelValue":"CSDK1","netOpenBase":"3,000,000.00","netOpenterm":"2,581,389.60","rate":"0.8604632","realizedPnL":"0.00","unrealizedPnL":"(343,336.60)","pnlCurrency":"USD"},{"side":"Short","symbol":"USD/CHF","valueDate":"01/14/2021","level":"ORG","levelValue":"CSDK1","netOpenBase":"3,000,000.00","netOpenterm":"2,581,524.40","rate":"0.8605081","realizedPnL":"0.00","unrealizedPnL":"(343,197.86)","pnlCurrency":"USD"},{"side":"Short","symbol":"USD/CHF","valueDate":"06/14/2021","level":"ORG","levelValue":"CSDK1","netOpenBase":"3,000,000.00","netOpenterm":"2,581,367.10","rate":"0.8604557","realizedPnL":"0.00","unrealizedPnL":"(343,359.76)","pnlCurrency":"USD"}],"requestId":"-1079782850"}}


============= ChiefDealer =============
Subscription
{"chiefDealerNotification":"subscribe"}

{"chiefDealerNotification":"unsubscribe"}

2020.07.20, 08:09:20.0427 UTC -> {"chiefDealerNotification":{"data":"[{\u0027bitSetMap\u0027:{\u0027twapOrderBitSet\u0027:[126,120,0],\u0027headerBitSet\u0027:[63],\u0027cptyBBitSet\u0027:[5],\u0027base\u0027:[127,43,48,100,43,67,100,80,0],\u0027OutrightBitSet\u0027:[21]},\u0027dataMap\u0027:{\u0027headerData\u0027:\u00271113919274\\u0001ORDER_SUBMITTED\\u00012020-07-20 08:09:20:295 GMT\\u0001BUBBLESUCD\\u0001HTML\\u0001BUBBLESUCD\u0027,\u0027cptyBData\u0027:\u0027FXI\\u0001FXI-le1\u0027,\u0027OutrightData\u0027:\u00270.0000\\u00011.21803\\u0001SPOT\u0027,\u0027twapOrderData\u0027:\u00270.0\\u00010.0\\u0001N\\u00010.0\\u00010\\u0001N\\u00010.0\\u00010\\u00010\\u0001N\u0027,\u0027base\u0027:\u0027false\\u00014731330338\\u0001MKT\\u0001OFFER\\u0001GBP\\u0001USD\\u00011000000.0000\\u0001FXI9066840417\\u00012020-07-20\\u00011.21803\\u0001GTD\\u00015.0\\u0001singleLeg\\u0001DNET/PP\\u0001GBP/USD\\u00011218030.0000\\u00011.21803\\u00011000000.0\\u000120200720-08:09:20\\u00010.5\\u00012020-07-20 08:09:20:295 GMT\\u00011000000.0000\\u00010.0\\u000119700101-00:00:00\u0027}}]","key":"1595232560341-24"}}
{"chiefDealerNotification":"unsubscribe"}

