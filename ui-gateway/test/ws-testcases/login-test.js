const assert = require('chai').assert
const login = require('../login').login
const getLoginResponse = require('../login').getLoginResponse

let testCase1 = function(){
		describe("login test ", function(){
			
			beforeEach(function(done) {
			login(done)
		  });
			
			it("status ok test", function(){
				console.log('login response: ' + JSON.stringify(getLoginResponse()))
				assert.equal(getLoginResponse().status, 'OK')
			});
			
		});
}

testCase1();