{"version": 3, "file": "workerpool.min.js", "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,aAAc,GAAIH,GACC,iBAAZC,QACdA,QAAoB,WAAID,IAExBD,EAAiB,WAAIC,IARvB,CASoB,oBAATK,KAAuBA,KAAOC,MAAO,WAChD,+DCVA,IAAIC,EAAUC,EAAQ,KAClBC,EAAgBD,EAAQ,KACxBE,EAAcF,EAAQ,KAEtBG,EAAuB,IADFH,EAAQ,MAQjC,SAASI,EAAKC,EAAQC,GACE,iBAAXD,EACTP,KAAKO,OAASA,GAAU,MAGxBP,KAAKO,OAAS,KACdC,EAAUD,GAGZP,KAAKS,QAAU,GACfT,KAAKU,MAAQ,GAEbF,EAAUA,GAAW,GAErBR,KAAKW,SAAWC,OAAOC,OAAOL,EAAQG,UAAY,IAClDX,KAAKc,SAAWF,OAAOC,OAAOL,EAAQM,UAAY,IAClDd,KAAKe,eAAkBP,EAAQO,gBAAkB,MACjDf,KAAKgB,WAAaR,EAAQQ,WAC1BhB,KAAKiB,WAAaT,EAAQS,YAAcT,EAAQQ,YAAc,OAC9DhB,KAAKkB,aAAeV,EAAQU,cAAgBC,EAAAA,EAE5CnB,KAAKoB,eAAiBZ,EAAQY,gBAAmB,kBAAM,MACvDpB,KAAKqB,kBAAoBb,EAAQa,mBAAsB,kBAAM,MAGzDb,GAAW,eAAgBA,GAiXjC,SAA4Bc,GAC1B,IAAKC,EAASD,KAAgBE,EAAUF,IAAeA,EAAa,EAClE,MAAM,IAAIG,UAAU,oDAlXpBC,CAAmBlB,EAAQc,YAC3BtB,KAAKsB,WAAad,EAAQc,YAG1BtB,KAAKsB,WAAaK,KAAKC,KAAKxB,EAAYyB,MAAQ,GAAK,EAAG,GAGtDrB,GAAW,eAAgBA,IACH,QAAvBA,EAAQsB,WACT9B,KAAK8B,WAAa9B,KAAKsB,YAkX7B,SAA4BQ,GAC1B,IAAKP,EAASO,KAAgBN,EAAUM,IAAeA,EAAa,EAClE,MAAM,IAAIL,UAAU,oDAlXlBM,CAAmBvB,EAAQsB,YAC3B9B,KAAK8B,WAAatB,EAAQsB,WAC1B9B,KAAKsB,WAAaK,KAAKC,IAAI5B,KAAK8B,WAAY9B,KAAKsB,aAEnDtB,KAAKgC,qBAGPhC,KAAKiC,WAAajC,KAAKkC,MAAMC,KAAKnC,MAGV,WAApBA,KAAKiB,YACPd,EAAciC,sBAgXlB,SAASb,EAASc,GAChB,MAAwB,iBAAVA,EAQhB,SAASb,EAAUa,GACjB,OAAOV,KAAKW,MAAMD,IAAUA,EArV9B/B,EAAKiC,UAAUC,KAAO,SAAUC,EAAQC,EAAQlC,GAE9C,GAAIkC,IAAWC,MAAMC,QAAQF,GAC3B,MAAM,IAAIjB,UAAU,uCAGtB,GAAsB,iBAAXgB,EAAqB,CAC9B,IAAII,EAAW5C,EAAQ6C,QAEvB,GAAI9C,KAAKU,MAAMqC,QAAU/C,KAAKkB,aAC5B,MAAM,IAAI8B,MAAM,qBAAuBhD,KAAKkB,aAAe,YAI7D,IAAIR,EAAQV,KAAKU,MACbuC,EAAO,CACTR,OAASA,EACTC,OAASA,EACTG,SAAUA,EACVK,QAAS,KACT1C,QAASA,GAEXE,EAAMyC,KAAKF,GAIX,IAAIG,EAAkBP,EAASQ,QAAQH,QAgBvC,OAfAL,EAASQ,QAAQH,QAAU,SAAkBI,GAC3C,OAA6B,IAAzB5C,EAAM6C,QAAQN,IAEhBA,EAAKC,QAAUI,EACRT,EAASQ,SAITD,EAAgBI,KAAKX,EAASQ,QAASC,IAKlDtD,KAAKkC,QAEEW,EAASQ,QAEb,GAAsB,mBAAXZ,EAEd,OAAOzC,KAAKwC,KAAK,MAAO,CAACiB,OAAOhB,GAASC,IAGzC,MAAM,IAAIjB,UAAU,qDAUxBnB,EAAKiC,UAAUmB,MAAQ,WACrB,GAAIC,UAAUZ,OAAS,EACrB,MAAM,IAAIC,MAAM,yBAGlB,IAAIY,EAAO5D,KACX,OAAOA,KAAKwC,KAAK,WACZqB,MAAK,SAAUC,GACd,IAAIJ,EAAQ,GAQZ,OANAI,EAAQC,SAAQ,SAAUtB,GACxBiB,EAAMjB,GAAU,WACd,OAAOmB,EAAKpB,KAAKC,EAAQE,MAAMJ,UAAUyB,MAAMR,KAAKG,gBAIjDD,MAwBfpD,EAAKiC,UAAUL,MAAQ,WACrB,GAAIlC,KAAKU,MAAMqC,OAAS,EAAG,CAIzB,IAAIkB,EAASjE,KAAKkE,aAClB,GAAID,EAAQ,CAEV,IAAIE,EAAKnE,KACLiD,EAAOjD,KAAKU,MAAM0D,QAGtB,GAAInB,EAAKJ,SAASQ,QAAQgB,QAAS,CAEjC,IAAIhB,EAAUY,EAAOzB,KAAKS,EAAKR,OAAQQ,EAAKP,OAAQO,EAAKJ,SAAUI,EAAKzC,SACrEqD,KAAKM,EAAGlC,YADG,OAEL,WAEL,GAAIgC,EAAOK,WACT,OAAOH,EAAGI,cAAcN,MAEzBJ,MAAK,WACNM,EAAGjC,WAIqB,iBAAjBe,EAAKC,SACdG,EAAQH,QAAQD,EAAKC,cAIvBiB,EAAGjC,WAeX5B,EAAKiC,UAAU2B,WAAa,WAG1B,IADA,IAAIzD,EAAUT,KAAKS,QACV+D,EAAI,EAAGA,EAAI/D,EAAQsC,OAAQyB,IAAK,CACvC,IAAIP,EAASxD,EAAQ+D,GACrB,IAAsB,IAAlBP,EAAOQ,OACT,OAAOR,EAIX,OAAIxD,EAAQsC,OAAS/C,KAAKsB,YAExB2C,EAASjE,KAAK0E,uBACdjE,EAAQ0C,KAAKc,GACNA,GAGF,MAWT3D,EAAKiC,UAAUgC,cAAgB,SAASN,GACtC,IAAIE,EAAKnE,KAQT,OANAK,EAAqBsE,YAAYV,EAAOW,WAExC5E,KAAK6E,sBAAsBZ,GAE3BjE,KAAKgC,oBAEE,IAAI/B,GAAQ,SAAS6E,EAASC,GACnCd,EAAOe,WAAU,GAAO,SAASC,GAC/Bd,EAAG9C,kBAAkB,CACnBV,SAAUsD,EAAOtD,SACjBG,SAAUmD,EAAOnD,SACjBP,OAAQ0D,EAAO1D,SAEb0E,EACFF,EAAOE,GAEPH,EAAQb,UAWhB3D,EAAKiC,UAAUsC,sBAAwB,SAASZ,GAE9C,IAAIiB,EAAQlF,KAAKS,QAAQ8C,QAAQU,IAClB,IAAXiB,GACFlF,KAAKS,QAAQ0E,OAAOD,EAAO,IAc/B5E,EAAKiC,UAAUyC,UAAY,SAAUI,EAAOlC,GAC1C,IAAIiB,EAAKnE,KAGTA,KAAKU,MAAMqD,SAAQ,SAAUd,GAC3BA,EAAKJ,SAASkC,OAAO,IAAI/B,MAAM,uBAEjChD,KAAKU,MAAMqC,OAAS,EAEpB,IAGIsC,EAHI,SAAUpB,GAChBjE,KAAK6E,sBAAsBZ,IAER9B,KAAKnC,MAEtBsF,EAAW,GAcf,OAbctF,KAAKS,QAAQuD,QACnBD,SAAQ,SAAUE,GACxB,IAAIsB,EAActB,EAAOuB,mBAAmBJ,EAAOlC,GAChDW,KAAKwB,GACLI,QAAO,WACNtB,EAAG9C,kBAAkB,CACnBV,SAAUsD,EAAOtD,SACjBG,SAAUmD,EAAOnD,SACjBP,OAAQ0D,EAAO1D,YAGrB+E,EAASnC,KAAKoC,MAETtF,EAAQyF,IAAIJ,IAOrBhF,EAAKiC,UAAUoD,MAAQ,WACrB,IAAIC,EAAe5F,KAAKS,QAAQsC,OAC5B8C,EAAc7F,KAAKS,QAAQqF,QAAO,SAAU7B,GAC9C,OAAOA,EAAOQ,UACb1B,OAEH,MAAO,CACL6C,aAAeA,EACfC,YAAeA,EACfE,YAAeH,EAAeC,EAE9BG,aAAehG,KAAKU,MAAMqC,OAC1BkD,YAAeJ,IAQnBvF,EAAKiC,UAAUP,kBAAoB,WACjC,GAAIhC,KAAK8B,WACP,IAAI,IAAI0C,EAAIxE,KAAKS,QAAQsC,OAAQyB,EAAIxE,KAAK8B,WAAY0C,IACpDxE,KAAKS,QAAQ0C,KAAKnD,KAAK0E,yBAU7BpE,EAAKiC,UAAUmC,qBAAuB,WACpC,IAAMwB,EAAkBlG,KAAKoB,eAAe,CAC1CT,SAAUX,KAAKW,SACfG,SAAUd,KAAKc,SACfP,OAAQP,KAAKO,UACT,GAEN,OAAO,IAAIJ,EAAc+F,EAAgB3F,QAAUP,KAAKO,OAAQ,CAC9DI,SAAUuF,EAAgBvF,UAAYX,KAAKW,SAC3CG,SAAUoF,EAAgBpF,UAAYd,KAAKc,SAC3C8D,UAAWvE,EAAqB8F,wBAAwBnG,KAAKe,gBAC7DE,WAAYjB,KAAKiB,cA4CrBrB,EAAOD,QAAUW,gCC9ajB,SAASL,EAAQmG,EAASC,GACxB,IAAIlC,EAAKnE,KAET,KAAMA,gBAAgBC,GACpB,MAAM,IAAIqG,YAAY,oDAGxB,GAAuB,mBAAZF,EACT,MAAM,IAAIE,YAAY,uDAGxB,IAAIC,EAAa,GACbC,EAAU,GAGdxG,KAAKyG,UAAW,EAChBzG,KAAK0G,UAAW,EAChB1G,KAAKqE,SAAU,EASf,IAAIsC,EAAW,SAAUC,EAAWC,GAClCN,EAAWpD,KAAKyD,GAChBJ,EAAQrD,KAAK0D,IASf7G,KAAK6D,KAAO,SAAU+C,EAAWC,GAC/B,OAAO,IAAI5G,GAAQ,SAAU6E,EAASC,GACpC,IAAI+B,EAAIF,EAAYG,EAAMH,EAAW9B,EAASC,GAAUD,EACpDkC,EAAIH,EAAYE,EAAMF,EAAW/B,EAASC,GAAUA,EAExD4B,EAASG,EAAGE,KACX7C,IAQL,IAAI8C,EAAW,SAAUC,GAgBvB,OAdA/C,EAAGsC,UAAW,EACdtC,EAAGuC,UAAW,EACdvC,EAAGE,SAAU,EAEbkC,EAAWxC,SAAQ,SAAUoD,GAC3BA,EAAGD,MAGLP,EAAW,SAAUC,EAAWC,GAC9BD,EAAUM,IAGZD,EAAWG,EAAU,aAEdjD,GAQLiD,EAAU,SAAUC,GAgBtB,OAdAlD,EAAGsC,UAAW,EACdtC,EAAGuC,UAAW,EACdvC,EAAGE,SAAU,EAEbmC,EAAQzC,SAAQ,SAAUoD,GACxBA,EAAGE,MAGLV,EAAW,SAAUC,EAAWC,GAC9BA,EAAOQ,IAGTJ,EAAWG,EAAU,aAEdjD,GAOTnE,KAAKsH,OAAS,WAQZ,OAPIjB,EACFA,EAAOiB,SAGPF,EAAQ,IAAIG,GAGPpD,GAUTnE,KAAKkD,QAAU,SAAUI,GACvB,GAAI+C,EACFA,EAAOnD,QAAQI,OAEZ,CACH,IAAIkE,EAAQC,YAAW,WACrBL,EAAQ,IAAIM,EAAa,2BAA6BpE,EAAQ,UAC7DA,GAEHa,EAAGsB,QAAO,WACRkC,aAAaH,MAIjB,OAAOrD,GAITiC,GAAQ,SAAUc,GAChBD,EAASC,MACR,SAAUG,GACXD,EAAQC,MAYZ,SAASN,EAAMa,EAAU9C,EAASC,GAChC,OAAO,SAAUmC,GACf,IACE,IAAIW,EAAMD,EAASV,GACfW,GAA2B,mBAAbA,EAAIhE,MAA+C,mBAAjBgE,EAAG,MAErDA,EAAIhE,KAAKiB,EAASC,GAGlBD,EAAQ+C,GAGZ,MAAOR,GACLtC,EAAOsC,KA6Eb,SAASE,EAAkBO,GACzB9H,KAAK8H,QAAUA,GAAW,oBAC1B9H,KAAK+H,OAAS,IAAI/E,OAAS+E,MAe7B,SAASL,EAAaI,GACpB9H,KAAK8H,QAAUA,GAAW,mBAC1B9H,KAAK+H,OAAS,IAAI/E,OAAS+E,MAtF7B9H,EAAQsC,UAAR,MAA6B,SAAUsE,GACrC,OAAO7G,KAAK6D,KAAK,KAAMgD,IAWzB5G,EAAQsC,UAAUkD,OAAS,SAAU0B,GACnC,OAAOnH,KAAK6D,KAAKsD,EAAIA,IASvBlH,EAAQyF,IAAM,SAAUJ,GACtB,OAAO,IAAIrF,GAAQ,SAAU6E,EAASC,GACpC,IAAIiD,EAAY1C,EAASvC,OACrBkF,EAAU,GAEVD,EACF1C,EAASvB,SAAQ,SAAUmE,EAAG1D,GAC5B0D,EAAErE,MAAK,SAAUqD,GACfe,EAAQzD,GAAK0C,EAEI,KADjBc,GAEElD,EAAQmD,MAET,SAAUZ,GACXW,EAAY,EACZjD,EAAOsC,SAKXvC,EAAQmD,OASdhI,EAAQ6C,MAAQ,WACd,IAAID,EAAW,GAOf,OALAA,EAASQ,QAAU,IAAIpD,GAAQ,SAAU6E,EAASC,GAChDlC,EAASiC,QAAUA,EACnBjC,EAASkC,OAASA,KAGblC,GAaT0E,EAAkBhF,UAAY,IAAIS,MAClCuE,EAAkBhF,UAAU4F,YAAcnF,MAC1CuE,EAAkBhF,UAAU6F,KAAO,oBAEnCnI,EAAQsH,kBAAoBA,EAa5BG,EAAanF,UAAY,IAAIS,MAC7B0E,EAAanF,UAAU4F,YAAcnF,MACrC0E,EAAanF,UAAU6F,KAAO,eAE9BnI,EAAQyH,aAAeA,EAGvB9H,EAAOD,QAAUM,oXCpRjB,IAAIA,EAAUC,EAAQ,KAClBE,EAAcF,EAAQ,KACtBmI,EAAqBnI,EAAQ,KAM7BoI,EAAsB,2BAQ1B,SAASlG,IACP,IAAImG,EAAgBC,IACpB,IAAKD,EACH,MAAM,IAAIvF,MAAM,+EAGlB,OAAOuF,EAIT,SAASE,IAEP,GAAsB,mBAAXC,SAA4C,YAAlB,oBAAOA,OAAP,cAAOA,UAA+D,mBAAjCA,OAAOnG,UAAU4F,aACzF,MAAM,IAAInF,MAAM,yCAIpB,SAASwF,IACP,IACE,OAAOH,EAAmB,kBAC1B,MAAMhB,GACN,GAAqB,WAAjB,EAAOA,IAAgC,OAAVA,GAAiC,qBAAfA,EAAMsB,KAEvD,OAAO,KAEP,MAAMtB,GAmDZ,SAASuB,EAAmBrI,EAAQmI,GAElC,IAAIzE,EAAS,IAAIyE,EAAOnI,GAYxB,OAVA0D,EAAO4E,iBAAkB,EAEzB5E,EAAO6E,GAAK,SAAUC,EAAOnB,GAC3B5H,KAAKgJ,iBAAiBD,GAAO,SAAUjB,GACrCF,EAASE,EAAQmB,UAGrBhF,EAAOiF,KAAO,SAAUpB,GACtB9H,KAAKmJ,YAAYrB,IAEZ7D,EAGT,SAASmF,EAAwB7I,EAAQgI,GACvC,IAAItE,EAAS,IAAIsE,EAAcG,OAAOnI,EAAQ,CAC5C8I,QAAQ,EACRC,QAAQ,IAiBV,OAfArF,EAAOsF,gBAAiB,EAExBtF,EAAOiF,KAAO,SAASpB,GACrB9H,KAAKmJ,YAAYrB,IAGnB7D,EAAOuF,KAAO,WAEZ,OADAxJ,KAAKgF,aACE,GAGTf,EAAOwF,WAAa,WAClBzJ,KAAKgF,aAGAf,EAGT,SAASyF,EAAmBnJ,EAAQC,EAASmJ,GAE3C,IAAI1F,EAAS0F,EAAcC,KACzBrJ,EACAC,EAAQG,SACRH,EAAQM,UAIV,OADAmD,EAAO4F,gBAAiB,EACjB5F,EAIT,SAAS6F,EAAmBC,GAC1BA,EAAOA,GAAQ,GAEf,IAAIC,EAAkBC,QAAQC,SAASC,KAAK,KACxCC,GAA4D,IAA1CJ,EAAgBzG,QAAQ,aAC1C8G,GAAuD,IAA5CL,EAAgBzG,QAAQ,eAEnC2G,EAAW,GAef,OAdIE,IACFF,EAAS/G,KAAK,aAAe4G,EAAKnF,WAE9ByF,GACFH,EAAS/G,KAAK,gBAIlB8G,QAAQC,SAASnG,SAAQ,SAASuG,GAC5BA,EAAI/G,QAAQ,yBAA2B,GACzC2G,EAAS/G,KAAKmH,MAIX1J,OAAO2J,OAAO,GAAIR,EAAM,CAC7BpJ,SAAUoJ,EAAKpJ,SACfG,SAAUF,OAAO2J,OAAO,GAAIR,EAAKjJ,SAAU,CACzCoJ,UAAWH,EAAKjJ,UAAYiJ,EAAKjJ,SAASoJ,UAAY,IACrDM,OAAON,OA6Bd,SAAS/J,EAAcI,EAAQkK,GAC7B,IAAItG,EAAKnE,KACLQ,EAAUiK,GAAY,GAsD1B,SAASC,EAAQrD,GAGf,IAAK,IAAIsD,KAFTxG,EAAGG,YAAa,EAEDH,EAAGyG,gBACUC,IAAtB1G,EAAGyG,WAAWD,IAChBxG,EAAGyG,WAAWD,GAAI9H,SAASkC,OAAOsC,GAGtClD,EAAGyG,WAAahK,OAAOkK,OAAO,MA5DhC9K,KAAKO,OAASA,GA7JhB,WACE,GAA6B,YAAzBH,EAAY2K,SAAwB,CAEtC,GAAoB,oBAATC,KACT,MAAM,IAAIhI,MAAM,qCAElB,IAAKiI,OAAOC,KAA6C,mBAA/BD,OAAOC,IAAIC,gBACnC,MAAM,IAAInI,MAAM,oDAIlB,IAAIoI,EAAO,IAAIJ,KAAK,CAAC9K,EAAQ,MAAgC,CAACmL,KAAM,oBACpE,OAAOJ,OAAOC,IAAIC,gBAAgBC,GAIlC,OAAOE,UAAY,aA6IGC,GACxBvL,KAAKiE,OA1IP,SAAqB1D,EAAQC,GAC3B,GAA2B,QAAvBA,EAAQS,WAEV,OADAwH,IACOG,EAAmBrI,EAAQmI,QAC7B,GAA2B,WAAvBlI,EAAQS,WAEjB,OAAOmI,EAAwB7I,EAD/BgI,EAAgBnG,KAEX,GAA2B,YAAvB5B,EAAQS,YAA6BT,EAAQS,WAEjD,CACL,GAA6B,YAAzBb,EAAY2K,SAEd,OADAtC,IACOG,EAAmBrI,EAAQmI,QAGlC,IAAIH,EAAgBC,IACpB,OAAID,EACKa,EAAwB7I,EAAQgI,GAEhCmB,EAAmBnJ,EAAQuJ,EAAmBtJ,GAAU6H,EAAmB,kBAXtF,OAAOqB,EAAmBnJ,EAAQuJ,EAAmBtJ,GAAU6H,EAAmB,kBAkItEmD,CAAYxL,KAAKO,OAAQC,GACvCR,KAAK4E,UAAYpE,EAAQoE,UACzB5E,KAAKc,SAAWN,EAAQM,SACxBd,KAAKW,SAAWH,EAAQG,SAGnBJ,IACHP,KAAKiE,OAAOwH,OAAQ,GAItBzL,KAAK0L,aAAe,GACpB1L,KAAKiE,OAAO6E,GAAG,WAAW,SAAU6C,GAClC,IAAIxH,EAAGG,WAGP,GAAwB,iBAAbqH,GAAsC,UAAbA,EAClCxH,EAAGF,OAAOwH,OAAQ,EA8CtB,WACA,o6BACuBtH,EAAGuH,aAAavG,OAAO,IAD9C,IACE,2BAAgD,KAAtCyG,EAAsC,QAC9CzH,EAAGF,OAAOiF,KAAK0C,IAFnB,+BA9CIC,OACK,CAEL,IAAIlB,EAAKgB,EAAShB,GACd1H,EAAOkB,EAAGyG,WAAWD,QACZE,IAAT5H,IACE0I,EAASG,QACP7I,EAAKzC,SAAsC,mBAApByC,EAAKzC,QAAQsI,IACtC7F,EAAKzC,QAAQsI,GAAG6C,EAASI,iBAIpB5H,EAAGyG,WAAWD,IAGE,IAAnBxG,EAAG6H,aAEL7H,EAAGa,YAID2G,EAAStE,MACXpE,EAAKJ,SAASkC,OAhE1B,SAAwBkH,GAItB,IAHA,IAAIC,EAAO,IAAIlJ,MAAM,IACjBmJ,EAAQvL,OAAOwL,KAAKH,GAEfzH,EAAI,EAAGA,EAAI2H,EAAMpJ,OAAQyB,IAChC0H,EAAKC,EAAM3H,IAAMyH,EAAIE,EAAM3H,IAG7B,OAAO0H,EAwDwBG,CAAcV,EAAStE,QAG5CpE,EAAKJ,SAASiC,QAAQ6G,EAASzE,cA2BzC,IAAIjD,EAASjE,KAAKiE,OAElBjE,KAAKiE,OAAO6E,GAAG,QAAS4B,GACxB1K,KAAKiE,OAAO6E,GAAG,QAAQ,SAAUwD,EAAUC,GACzC,IAAIzE,EAAU,8CAEdA,GAAW,kBAAoBwE,EAAW,MAC1CxE,GAAW,oBAAsByE,EAAa,MAE9CzE,GAAW,2BAA8B3D,EAAG5D,OAAS,MACrDuH,GAAW,mBAAsB7D,EAAOuI,UAAY,MACpD1E,GAAW,mBAAqB7D,EAAOwI,UAAY,MAEnD3E,GAAW,gBAAkB7D,EAAOoF,OAAS,MAC7CvB,GAAW,gBAAkB7D,EAAOqF,OAAS,MAE7CoB,EAAQ,IAAI1H,MAAM8E,OAGpB9H,KAAK4K,WAAahK,OAAOkK,OAAO,MAEhC9K,KAAKgM,aAAc,EACnBhM,KAAKsE,YAAa,EAClBtE,KAAK0M,mBAAqB,KAC1B1M,KAAK2M,OAAS,EAOhBxM,EAAcoC,UAAUuB,QAAU,WAChC,OAAO9D,KAAKwC,KAAK,YAWnBrC,EAAcoC,UAAUC,KAAO,SAASC,EAAQC,EAAQG,EAAUrC,GAC3DqC,IACHA,EAAW5C,EAAQ6C,SAIrB,IAAI6H,IAAO3K,KAAK2M,OAGhB3M,KAAK4K,WAAWD,GAAM,CACpBA,GAAIA,EACJ9H,SAAUA,EACVrC,QAASA,GAIX,IAAIoL,EAAU,CACZjB,GAAIA,EACJlI,OAAQA,EACRC,OAAQA,GAGN1C,KAAKsE,WACPzB,EAASkC,OAAO,IAAI/B,MAAM,yBACjBhD,KAAKiE,OAAOwH,MAErBzL,KAAKiE,OAAOiF,KAAK0C,GAEjB5L,KAAK0L,aAAavI,KAAKyI,GAIzB,IAAIzH,EAAKnE,KACT,OAAO6C,EAASQ,QAAT,OAAuB,SAAUgE,GACtC,GAAIA,aAAiBpH,EAAQsH,mBAAqBF,aAAiBpH,EAAQyH,aAMzE,cAHOvD,EAAGyG,WAAWD,GAGdxG,EAAGqB,oBAAmB,GAC1B3B,MAAK,WACJ,MAAMwD,KACL,SAASpC,GACV,MAAMA,KAGV,MAAMoC,MASZlH,EAAcoC,UAAUkC,KAAO,WAC7B,OAAO7D,OAAOwL,KAAKpM,KAAK4K,YAAY7H,OAAS,GAW/C5C,EAAcoC,UAAUyC,UAAY,SAAUI,EAAOwC,GACnD,IAAIzD,EAAKnE,KACT,GAAIoF,EAAO,CAET,IAAK,IAAIuF,KAAM3K,KAAK4K,gBACUC,IAAxB7K,KAAK4K,WAAWD,IAClB3K,KAAK4K,WAAWD,GAAI9H,SAASkC,OAAO,IAAI/B,MAAM,sBAGlDhD,KAAK4K,WAAahK,OAAOkK,OAAO,MAMlC,GAHwB,mBAAblD,IACT5H,KAAK0M,mBAAqB9E,GAEvB5H,KAAKyE,OAgERzE,KAAKgM,aAAc,MAhEH,CAEhB,IAAIY,EAAU,SAAS3H,GAQrB,GAPAd,EAAGG,YAAa,EACC,MAAbH,EAAGF,QAAkBE,EAAGF,OAAO4I,oBAEjC1I,EAAGF,OAAO4I,mBAAmB,WAE/B1I,EAAGF,OAAS,KACZE,EAAG6H,aAAc,EACb7H,EAAGuI,mBACLvI,EAAGuI,mBAAmBzH,EAAKd,QACtB,GAAIc,EACT,MAAMA,GAIV,GAAIjF,KAAKiE,OAAQ,CACf,GAAgC,mBAArBjE,KAAKiE,OAAOuF,KAAqB,CAC1C,GAAIxJ,KAAKiE,OAAO6I,OAEd,YADAF,EAAQ,IAAI5J,MAAM,2BAIpB,GAAIhD,KAAKiE,OAAO4F,eAAgB,CAC9B,IAAIkD,EAAmBtF,YAAW,WAC5BtD,EAAGF,QACLE,EAAGF,OAAOuF,SA9ZS,KAkavBxJ,KAAKiE,OAAO+I,KAAK,QAAQ,WACvBrF,aAAaoF,GACT5I,EAAGF,SACLE,EAAGF,OAAO6I,QAAS,GAErBF,OAGE5M,KAAKiE,OAAOwH,MACdzL,KAAKiE,OAAOiF,KAAKZ,GAEjBtI,KAAK0L,aAAavI,KAAKmF,QAIzBtI,KAAKiE,OAAOuF,OACZxJ,KAAKiE,OAAO6I,QAAS,EACrBF,IAEF,OAEG,GAAqC,mBAA1B5M,KAAKiE,OAAOe,UAK1B,MAAM,IAAIhC,MAAM,8BAJhBhD,KAAKiE,OAAOe,YACZhF,KAAKiE,OAAO6I,QAAS,EAMzBF,MAkBJzM,EAAcoC,UAAUiD,mBAAqB,SAAUJ,EAAOlC,GAC5D,IAAIL,EAAW5C,EAAQ6C,QAWvB,OAVII,IACFL,EAASQ,QAAQH,QAAUA,GAE7BlD,KAAKgF,UAAUI,GAAO,SAASH,EAAKhB,GAC9BgB,EACFpC,EAASkC,OAAOE,GAEhBpC,EAASiC,QAAQb,MAGdpB,EAASQ,SAGlBzD,EAAOD,QAAUQ,EACjBP,EAAOD,QAAQsN,yBAA2BzE,EAC1C5I,EAAOD,QAAQuN,oBAAsBxD,EACrC9J,EAAOD,QAAQwN,oBAAsBvE,EACrChJ,EAAOD,QAAQyN,yBAA2BhE,EAC1CxJ,EAAOD,QAAQyC,oBAAsBA,gCCjfrC,SAASiL,IACPrN,KAAKsN,MAAQ1M,OAAOkK,OAAO,MAC3B9K,KAAK+C,OAAS,EAHhBnD,EAAOD,QAAU0N,EAMjBA,EAAmB9K,UAAU4D,wBAA0B,SAASoH,GAC9D,MAAgC,IAAzBvN,KAAKsN,MAAMC,IAChBA,IAGF,GAAIA,GAZU,MAaZ,MAAM,IAAIvK,MAAM,wCAA0CuK,EAA1C,YAKlB,OAFAvN,KAAKsN,MAAMC,IAAY,EACvBvN,KAAK+C,SACEwK,GAGTF,EAAmB9K,UAAUoC,YAAc,SAAS6I,UAC3CxN,KAAKsN,MAAME,GAClBxN,KAAK+C,+BCzBP,IAAIsF,EAAqBnI,EAAQ,KAG7BuN,EAAS,SAAUC,GACrB,YACyB,IAAhBA,GACiB,MAAxBA,EAAYC,UACiB,MAA7BD,EAAYC,SAASC,MAGzBhO,EAAOD,QAAQ8N,OAASA,EAGxB7N,EAAOD,QAAQoL,SAA8B,oBAAZd,SAA2BwD,EAAOxD,SAC/D,OACA,UAIJ,IAAI4D,EAUJ,SAAgCjO,GAC9B,IACE,OAAOyI,EAZgC,kBAavC,MAAMpD,GACN,OAAO,MAdU6I,GACrBlO,EAAOD,QAAQoO,aAA2C,SAA5BnO,EAAOD,QAAQoL,WACtC8C,GAAkBA,EAAeE,gBAAkB9D,QAAQ+D,UAC5C,oBAAXC,OAGXrO,EAAOD,QAAQkC,KAAmC,YAA5BjC,EAAOD,QAAQoL,SACjChL,KAAKmO,UAAUC,oBACf9F,EAAmB,MAAMxG,OAAOkB,wBCtBpCnD,EAAOD,QAAU,+pFCJjB,IAAI0I,mBAAqB+F,KACrB,0HAKJxO,OAAOD,QAAU0I,0TCDjB,IAAIA,mBAAqB+F,KACrB,0HASA9F,oBAAsB,2BAMtBrE,OAAS,CACXoK,KAAM,cAER,GAAoB,oBAATtO,MAA+C,mBAAhBoJ,aAA0D,mBAArBH,iBAE7E/E,OAAO6E,GAAK,SAAUC,EAAOnB,GAC3BoB,iBAAiBD,GAAO,SAAUjB,GAChCF,EAASE,EAAQmB,UAGrBhF,OAAOiF,KAAO,SAAUpB,GACtBqB,YAAYrB,QAGX,IAAuB,oBAAZmC,QA+Bd,MAAM,IAAIjH,MAAM,uCA5BhB,IAAIuF,cACJ,IACEA,cAAgBF,mBAAmB,kBACnC,MAAMhB,GACN,GAAqB,WAAjB,QAAOA,IAAgC,OAAVA,GAAiC,qBAAfA,EAAMsB,KAGvD,MAAMtB,EAIV,GAAIkB,eAE2B,OAA7BA,cAAc+F,WAAqB,CACnC,IAAIA,WAAc/F,cAAc+F,WAChCrK,OAAOiF,KAAOoF,WAAWnF,YAAYhH,KAAKmM,YAC1CrK,OAAO6E,GAAKwF,WAAWxF,GAAG3G,KAAKmM,iBAE/BrK,OAAO6E,GAAKmB,QAAQnB,GAAG3G,KAAK8H,SAC5BhG,OAAOiF,KAAOe,QAAQf,KAAK/G,KAAK8H,SAEhChG,OAAO6E,GAAG,cAAc,WACtBmB,QAAQoE,KAAK,MAEfpK,OAAOoK,KAAOpE,QAAQoE,KAAKlM,KAAK8H,SAOpC,SAASsE,aAAalH,GACpB,OAAOzG,OAAO4N,oBAAoBnH,GAAOoH,QAAO,SAASC,EAAStG,GAChE,OAAOxH,OAAO+N,eAAeD,EAAStG,EAAM,CAC/C/F,MAAOgF,EAAMe,GACbwG,YAAY,MAER,IASL,SAASC,UAAUxM,GACjB,OAAOA,GAAgC,mBAAfA,EAAMwB,MAAgD,mBAAhBxB,EAAK,MAIrE4B,OAAOH,QAAU,GAQjBG,OAAOH,QAAQgL,IAAM,SAAa3H,EAAI4H,GACpC,IAAI/H,EAAI,IAAIgI,SAAS,WAAa7H,EAAK,6BACvC,OAAOH,EAAEiI,MAAMjI,EAAG+H,IAOpB9K,OAAOH,QAAQA,QAAU,WACvB,OAAOlD,OAAOwL,KAAKnI,OAAOH,UAG5B,IAAIoL,iBAAmB,KAEvBjL,OAAO6E,GAAG,WAAW,SAAU8C,GAC7B,GAAIA,IAAYtD,oBACd,OAAOrE,OAAOoK,KAAK,GAErB,IACE,IAAI5L,EAASwB,OAAOH,QAAQ8H,EAAQnJ,QAEpC,IAAIA,EAsCF,MAAM,IAAIO,MAAM,mBAAqB4I,EAAQnJ,OAAS,KArCtDyM,iBAAmBtD,EAAQjB,GAG3B,IAAIzD,EAASzE,EAAOwM,MAAMxM,EAAQmJ,EAAQlJ,QAEtCmM,UAAU3H,GAEZA,EACKrD,MAAK,SAAUqD,GACdjD,OAAOiF,KAAK,CACVyB,GAAIiB,EAAQjB,GACZzD,OAAQA,EACRG,MAAO,OAET6H,iBAAmB,QAPzB,OASW,SAAUjK,GACfhB,OAAOiF,KAAK,CACVyB,GAAIiB,EAAQjB,GACZzD,OAAQ,KACRG,MAAOkH,aAAatJ,KAEtBiK,iBAAmB,SAKzBjL,OAAOiF,KAAK,CACVyB,GAAIiB,EAAQjB,GACZzD,OAAQA,EACRG,MAAO,OAGT6H,iBAAmB,MAOzB,MAAOjK,GACLhB,OAAOiF,KAAK,CACVyB,GAAIiB,EAAQjB,GACZzD,OAAQ,KACRG,MAAOkH,aAAatJ,SAS1BhB,OAAOkL,SAAW,SAAUrL,GAE1B,GAAIA,EACF,IAAK,IAAIsE,KAAQtE,EACXA,EAAQsL,eAAehH,KACzBnE,OAAOH,QAAQsE,GAAQtE,EAAQsE,IAKrCnE,OAAOiF,KAAK,UAIdjF,OAAOoL,KAAO,SAAUtD,GAClBmD,kBACFjL,OAAOiF,KAAK,CACVyB,GAAIuE,iBACJpD,SAAS,EACTC,QAAAA,KAMJpM,QAAQ2P,IAAMrL,OAAOkL,SACrBxP,QAAQ0P,KAAOpL,OAAOoL,OCvMpBE,yBAA2B,GAG/B,SAASC,oBAAoBC,GAE5B,IAAIC,EAAeH,yBAAyBE,GAC5C,QAAqB5E,IAAjB6E,EACH,OAAOA,EAAa/P,QAGrB,IAAIC,EAAS2P,yBAAyBE,GAAY,CAGjD9P,QAAS,IAOV,OAHAgQ,oBAAoBF,GAAU7P,EAAQA,EAAOD,QAAS6P,qBAG/C5P,EAAOD,+ECrBXS,EAAcF,oBAAQ,KAQ1BP,EAAQiE,KAAO,SAAcrD,EAAQC,GAGnC,OAAO,IAFIN,oBAAQ,KAEZ,CAASK,EAAQC,IAO1Bb,EAAQsE,OAAS,SAAgBH,GAClB5D,oBAAQ,KACdoP,IAAIxL,IAObnE,EAAQiQ,WAAa,SAAoB7D,GAC1B7L,oBAAQ,KACdmP,KAAKtD,IAOdpM,EAAQM,QAAU,oBAAlBN,KAEAA,EAAQoL,SAAW3K,EAAY2K,SAC/BpL,EAAQoO,aAAe3N,EAAY2N,aACnCpO,EAAQkC,KAAOzB,EAAYyB,4BV9B3B", "sources": ["webpack://workerpool/webpack/universalModuleDefinition", "webpack://workerpool/./src/Pool.js", "webpack://workerpool/./src/Promise.js", "webpack://workerpool/./src/WorkerHandler.js", "webpack://workerpool/./src/debug-port-allocator.js", "webpack://workerpool/./src/environment.js", "webpack://workerpool/./src/generated/embeddedWorker.js", "webpack://workerpool/./src/requireFoolWebpack.js", "webpack://workerpool/./src/worker.js", "webpack://workerpool/webpack/bootstrap", "webpack://workerpool/./src/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"workerpool\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"workerpool\"] = factory();\n\telse\n\t\troot[\"workerpool\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", "var Promise = require('./Promise');\nvar WorkerHandler = require('./WorkerHandler');\nvar environment = require('./environment');\nvar DebugPortAllocator = require('./debug-port-allocator');\nvar DEBUG_PORT_ALLOCATOR = new DebugPortAllocator();\n/**\n * A pool to manage workers\n * @param {String} [script]   Optional worker script\n * @param {WorkerPoolOptions} [options]  See docs\n * @constructor\n */\nfunction Pool(script, options) {\n  if (typeof script === 'string') {\n    this.script = script || null;\n  }\n  else {\n    this.script = null;\n    options = script;\n  }\n\n  this.workers = [];  // queue with all workers\n  this.tasks = [];    // queue with tasks awaiting execution\n\n  options = options || {};\n\n  this.forkArgs = Object.freeze(options.forkArgs || []);\n  this.forkOpts = Object.freeze(options.forkOpts || {});\n  this.debugPortStart = (options.debugPortStart || 43210);\n  this.nodeWorker = options.nodeWorker;\n  this.workerType = options.workerType || options.nodeWorker || 'auto'\n  this.maxQueueSize = options.maxQueueSize || Infinity;\n\n  this.onCreateWorker = options.onCreateWorker || (() => null);\n  this.onTerminateWorker = options.onTerminateWorker || (() => null);\n\n  // configuration\n  if (options && 'maxWorkers' in options) {\n    validateMaxWorkers(options.maxWorkers);\n    this.maxWorkers = options.maxWorkers;\n  }\n  else {\n    this.maxWorkers = Math.max((environment.cpus || 4) - 1, 1);\n  }\n\n  if (options && 'minWorkers' in options) {\n    if(options.minWorkers === 'max') {\n      this.minWorkers = this.maxWorkers;\n    } else {\n      validateMinWorkers(options.minWorkers);\n      this.minWorkers = options.minWorkers;\n      this.maxWorkers = Math.max(this.minWorkers, this.maxWorkers);     // in case minWorkers is higher than maxWorkers\n    }\n    this._ensureMinWorkers();\n  }\n\n  this._boundNext = this._next.bind(this);\n\n\n  if (this.workerType === 'thread') {\n    WorkerHandler.ensureWorkerThreads();\n  }\n}\n\n\n/**\n * Execute a function on a worker.\n *\n * Example usage:\n *\n *   var pool = new Pool()\n *\n *   // call a function available on the worker\n *   pool.exec('fibonacci', [6])\n *\n *   // offload a function\n *   function add(a, b) {\n *     return a + b\n *   };\n *   pool.exec(add, [2, 4])\n *       .then(function (result) {\n *         console.log(result); // outputs 6\n *       })\n *       .catch(function(error) {\n *         console.log(error);\n *       });\n *\n * @param {String | Function} method  Function name or function.\n *                                    If `method` is a string, the corresponding\n *                                    method on the worker will be executed\n *                                    If `method` is a Function, the function\n *                                    will be stringified and executed via the\n *                                    workers built-in function `run(fn, args)`.\n * @param {Array} [params]  Function arguments applied when calling the function\n * @param {ExecOptions} [options]  Options object\n * @return {Promise.<*, Error>} result\n */\nPool.prototype.exec = function (method, params, options) {\n  // validate type of arguments\n  if (params && !Array.isArray(params)) {\n    throw new TypeError('Array expected as argument \"params\"');\n  }\n\n  if (typeof method === 'string') {\n    var resolver = Promise.defer();\n\n    if (this.tasks.length >= this.maxQueueSize) {\n      throw new Error('Max queue size of ' + this.maxQueueSize + ' reached');\n    }\n\n    // add a new task to the queue\n    var tasks = this.tasks;\n    var task = {\n      method:  method,\n      params:  params,\n      resolver: resolver,\n      timeout: null,\n      options: options\n    };\n    tasks.push(task);\n\n    // replace the timeout method of the Promise with our own,\n    // which starts the timer as soon as the task is actually started\n    var originalTimeout = resolver.promise.timeout;\n    resolver.promise.timeout = function timeout (delay) {\n      if (tasks.indexOf(task) !== -1) {\n        // task is still queued -> start the timer later on\n        task.timeout = delay;\n        return resolver.promise;\n      }\n      else {\n        // task is already being executed -> start timer immediately\n        return originalTimeout.call(resolver.promise, delay);\n      }\n    };\n\n    // trigger task execution\n    this._next();\n\n    return resolver.promise;\n  }\n  else if (typeof method === 'function') {\n    // send stringified function and function arguments to worker\n    return this.exec('run', [String(method), params]);\n  }\n  else {\n    throw new TypeError('Function or string expected as argument \"method\"');\n  }\n};\n\n/**\n * Create a proxy for current worker. Returns an object containing all\n * methods available on the worker. The methods always return a promise.\n *\n * @return {Promise.<Object, Error>} proxy\n */\nPool.prototype.proxy = function () {\n  if (arguments.length > 0) {\n    throw new Error('No arguments expected');\n  }\n\n  var pool = this;\n  return this.exec('methods')\n      .then(function (methods) {\n        var proxy = {};\n\n        methods.forEach(function (method) {\n          proxy[method] = function () {\n            return pool.exec(method, Array.prototype.slice.call(arguments));\n          }\n        });\n\n        return proxy;\n      });\n};\n\n/**\n * Creates new array with the results of calling a provided callback function\n * on every element in this array.\n * @param {Array} array\n * @param {function} callback  Function taking two arguments:\n *                             `callback(currentValue, index)`\n * @return {Promise.<Array>} Returns a promise which resolves  with an Array\n *                           containing the results of the callback function\n *                           executed for each of the array elements.\n */\n/* TODO: implement map\nPool.prototype.map = function (array, callback) {\n};\n*/\n\n/**\n * Grab the first task from the queue, find a free worker, and assign the\n * worker to the task.\n * @protected\n */\nPool.prototype._next = function () {\n  if (this.tasks.length > 0) {\n    // there are tasks in the queue\n\n    // find an available worker\n    var worker = this._getWorker();\n    if (worker) {\n      // get the first task from the queue\n      var me = this;\n      var task = this.tasks.shift();\n\n      // check if the task is still pending (and not cancelled -> promise rejected)\n      if (task.resolver.promise.pending) {\n        // send the request to the worker\n        var promise = worker.exec(task.method, task.params, task.resolver, task.options)\n          .then(me._boundNext)\n          .catch(function () {\n            // if the worker crashed and terminated, remove it from the pool\n            if (worker.terminated) {\n              return me._removeWorker(worker);\n            }\n          }).then(function() {\n            me._next(); // trigger next task in the queue\n          });\n\n        // start queued timer now\n        if (typeof task.timeout === 'number') {\n          promise.timeout(task.timeout);\n        }\n      } else {\n        // The task taken was already complete (either rejected or resolved), so just trigger next task in the queue\n        me._next();\n      }\n    }\n  }\n};\n\n/**\n * Get an available worker. If no worker is available and the maximum number\n * of workers isn't yet reached, a new worker will be created and returned.\n * If no worker is available and the maximum number of workers is reached,\n * null will be returned.\n *\n * @return {WorkerHandler | null} worker\n * @private\n */\nPool.prototype._getWorker = function() {\n  // find a non-busy worker\n  var workers = this.workers;\n  for (var i = 0; i < workers.length; i++) {\n    var worker = workers[i];\n    if (worker.busy() === false) {\n      return worker;\n    }\n  }\n\n  if (workers.length < this.maxWorkers) {\n    // create a new worker\n    worker = this._createWorkerHandler();\n    workers.push(worker);\n    return worker;\n  }\n\n  return null;\n};\n\n/**\n * Remove a worker from the pool.\n * Attempts to terminate worker if not already terminated, and ensures the minimum\n * pool size is met.\n * @param {WorkerHandler} worker\n * @return {Promise<WorkerHandler>}\n * @protected\n */\nPool.prototype._removeWorker = function(worker) {\n  var me = this;\n\n  DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n  // _removeWorker will call this, but we need it to be removed synchronously\n  this._removeWorkerFromList(worker);\n  // If minWorkers set, spin up new workers to replace the crashed ones\n  this._ensureMinWorkers();\n  // terminate the worker (if not already terminated)\n  return new Promise(function(resolve, reject) {\n    worker.terminate(false, function(err) {\n      me.onTerminateWorker({\n        forkArgs: worker.forkArgs,\n        forkOpts: worker.forkOpts,\n        script: worker.script\n      });\n      if (err) {\n        reject(err);\n      } else {\n        resolve(worker);\n      }\n    });\n  });\n};\n\n/**\n * Remove a worker from the pool list.\n * @param {WorkerHandler} worker\n * @protected\n */\nPool.prototype._removeWorkerFromList = function(worker) {\n  // remove from the list with workers\n  var index = this.workers.indexOf(worker);\n  if (index !== -1) {\n    this.workers.splice(index, 1);\n  }\n};\n\n/**\n * Close all active workers. Tasks currently being executed will be finished first.\n * @param {boolean} [force=false]   If false (default), the workers are terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the workers will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<void, Error>}\n */\nPool.prototype.terminate = function (force, timeout) {\n  var me = this;\n\n  // cancel any pending tasks\n  this.tasks.forEach(function (task) {\n    task.resolver.reject(new Error('Pool terminated'));\n  });\n  this.tasks.length = 0;\n\n  var f = function (worker) {\n    this._removeWorkerFromList(worker);\n  };\n  var removeWorker = f.bind(this);\n\n  var promises = [];\n  var workers = this.workers.slice();\n  workers.forEach(function (worker) {\n    var termPromise = worker.terminateAndNotify(force, timeout)\n      .then(removeWorker)\n      .always(function() {\n        me.onTerminateWorker({\n          forkArgs: worker.forkArgs,\n          forkOpts: worker.forkOpts,\n          script: worker.script\n        });\n      });\n    promises.push(termPromise);\n  });\n  return Promise.all(promises);\n};\n\n/**\n * Retrieve statistics on tasks and workers.\n * @return {{totalWorkers: number, busyWorkers: number, idleWorkers: number, pendingTasks: number, activeTasks: number}} Returns an object with statistics\n */\nPool.prototype.stats = function () {\n  var totalWorkers = this.workers.length;\n  var busyWorkers = this.workers.filter(function (worker) {\n    return worker.busy();\n  }).length;\n\n  return {\n    totalWorkers:  totalWorkers,\n    busyWorkers:   busyWorkers,\n    idleWorkers:   totalWorkers - busyWorkers,\n\n    pendingTasks:  this.tasks.length,\n    activeTasks:   busyWorkers\n  };\n};\n\n/**\n * Ensures that a minimum of minWorkers is up and running\n * @protected\n */\nPool.prototype._ensureMinWorkers = function() {\n  if (this.minWorkers) {\n    for(var i = this.workers.length; i < this.minWorkers; i++) {\n      this.workers.push(this._createWorkerHandler());\n    }\n  }\n};\n\n/**\n * Helper function to create a new WorkerHandler and pass all options.\n * @return {WorkerHandler}\n * @private\n */\nPool.prototype._createWorkerHandler = function () {\n  const overridenParams = this.onCreateWorker({\n    forkArgs: this.forkArgs,\n    forkOpts: this.forkOpts,\n    script: this.script\n  }) || {};\n\n  return new WorkerHandler(overridenParams.script || this.script, {\n    forkArgs: overridenParams.forkArgs || this.forkArgs,\n    forkOpts: overridenParams.forkOpts || this.forkOpts,\n    debugPort: DEBUG_PORT_ALLOCATOR.nextAvailableStartingAt(this.debugPortStart),\n    workerType: this.workerType\n  });\n}\n\n/**\n * Ensure that the maxWorkers option is an integer >= 1\n * @param {*} maxWorkers\n * @returns {boolean} returns true maxWorkers has a valid value\n */\nfunction validateMaxWorkers(maxWorkers) {\n  if (!isNumber(maxWorkers) || !isInteger(maxWorkers) || maxWorkers < 1) {\n    throw new TypeError('Option maxWorkers must be an integer number >= 1');\n  }\n}\n\n/**\n * Ensure that the minWorkers option is an integer >= 0\n * @param {*} minWorkers\n * @returns {boolean} returns true when minWorkers has a valid value\n */\nfunction validateMinWorkers(minWorkers) {\n  if (!isNumber(minWorkers) || !isInteger(minWorkers) || minWorkers < 0) {\n    throw new TypeError('Option minWorkers must be an integer number >= 0');\n  }\n}\n\n/**\n * Test whether a variable is a number\n * @param {*} value\n * @returns {boolean} returns true when value is a number\n */\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\n\n/**\n * Test whether a number is an integer\n * @param {number} value\n * @returns {boolean} Returns true if value is an integer\n */\nfunction isInteger(value) {\n  return Math.round(value) == value;\n}\n\nmodule.exports = Pool;\n", "'use strict';\n\n/**\n * Promise\n *\n * Inspired by https://gist.github.com/RubaXa/8501359 from RubaXa <<EMAIL>>\n *\n * @param {Function} handler   Called as handler(resolve: Function, reject: Function)\n * @param {Promise} [parent]   Parent promise for propagation of cancel and timeout\n */\nfunction Promise(handler, parent) {\n  var me = this;\n\n  if (!(this instanceof Promise)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  if (typeof handler !== 'function') {\n    throw new SyntaxError('Function parameter handler(resolve, reject) missing');\n  }\n\n  var _onSuccess = [];\n  var _onFail = [];\n\n  // status\n  this.resolved = false;\n  this.rejected = false;\n  this.pending = true;\n\n  /**\n   * Process onSuccess and onFail callbacks: add them to the queue.\n   * Once the promise is resolve, the function _promise is replace.\n   * @param {Function} onSuccess\n   * @param {Function} onFail\n   * @private\n   */\n  var _process = function (onSuccess, onFail) {\n    _onSuccess.push(onSuccess);\n    _onFail.push(onFail);\n  };\n\n  /**\n   * Add an onSuccess callback and optionally an onFail callback to the Promise\n   * @param {Function} onSuccess\n   * @param {Function} [onFail]\n   * @returns {Promise} promise\n   */\n  this.then = function (onSuccess, onFail) {\n    return new Promise(function (resolve, reject) {\n      var s = onSuccess ? _then(onSuccess, resolve, reject) : resolve;\n      var f = onFail    ? _then(onFail,    resolve, reject) : reject;\n\n      _process(s, f);\n    }, me);\n  };\n\n  /**\n   * Resolve the promise\n   * @param {*} result\n   * @type {Function}\n   */\n  var _resolve = function (result) {\n    // update status\n    me.resolved = true;\n    me.rejected = false;\n    me.pending = false;\n\n    _onSuccess.forEach(function (fn) {\n      fn(result);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onSuccess(result);\n    };\n\n    _resolve = _reject = function () { };\n\n    return me;\n  };\n\n  /**\n   * Reject the promise\n   * @param {Error} error\n   * @type {Function}\n   */\n  var _reject = function (error) {\n    // update status\n    me.resolved = false;\n    me.rejected = true;\n    me.pending = false;\n\n    _onFail.forEach(function (fn) {\n      fn(error);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onFail(error);\n    };\n\n    _resolve = _reject = function () { }\n\n    return me;\n  };\n\n  /**\n   * Cancel te promise. This will reject the promise with a CancellationError\n   * @returns {Promise} self\n   */\n  this.cancel = function () {\n    if (parent) {\n      parent.cancel();\n    }\n    else {\n      _reject(new CancellationError());\n    }\n\n    return me;\n  };\n\n  /**\n   * Set a timeout for the promise. If the promise is not resolved within\n   * the time, the promise will be cancelled and a TimeoutError is thrown.\n   * If the promise is resolved in time, the timeout is removed.\n   * @param {number} delay     Delay in milliseconds\n   * @returns {Promise} self\n   */\n  this.timeout = function (delay) {\n    if (parent) {\n      parent.timeout(delay);\n    }\n    else {\n      var timer = setTimeout(function () {\n        _reject(new TimeoutError('Promise timed out after ' + delay + ' ms'));\n      }, delay);\n\n      me.always(function () {\n        clearTimeout(timer);\n      });\n    }\n\n    return me;\n  };\n\n  // attach handler passing the resolve and reject functions\n  handler(function (result) {\n    _resolve(result);\n  }, function (error) {\n    _reject(error);\n  });\n}\n\n/**\n * Execute given callback, then call resolve/reject based on the returned result\n * @param {Function} callback\n * @param {Function} resolve\n * @param {Function} reject\n * @returns {Function}\n * @private\n */\nfunction _then(callback, resolve, reject) {\n  return function (result) {\n    try {\n      var res = callback(result);\n      if (res && typeof res.then === 'function' && typeof res['catch'] === 'function') {\n        // method returned a promise\n        res.then(resolve, reject);\n      }\n      else {\n        resolve(res);\n      }\n    }\n    catch (error) {\n      reject(error);\n    }\n  }\n}\n\n/**\n * Add an onFail callback to the Promise\n * @param {Function} onFail\n * @returns {Promise} promise\n */\nPromise.prototype['catch'] = function (onFail) {\n  return this.then(null, onFail);\n};\n\n// TODO: add support for Promise.catch(Error, callback)\n// TODO: add support for Promise.catch(Error, Error, callback)\n\n/**\n * Execute given callback when the promise either resolves or rejects.\n * @param {Function} fn\n * @returns {Promise} promise\n */\nPromise.prototype.always = function (fn) {\n  return this.then(fn, fn);\n};\n\n/**\n * Create a promise which resolves when all provided promises are resolved,\n * and fails when any of the promises resolves.\n * @param {Promise[]} promises\n * @returns {Promise} promise\n */\nPromise.all = function (promises){\n  return new Promise(function (resolve, reject) {\n    var remaining = promises.length,\n        results = [];\n\n    if (remaining) {\n      promises.forEach(function (p, i) {\n        p.then(function (result) {\n          results[i] = result;\n          remaining--;\n          if (remaining == 0) {\n            resolve(results);\n          }\n        }, function (error) {\n          remaining = 0;\n          reject(error);\n        });\n      });\n    }\n    else {\n      resolve(results);\n    }\n  });\n};\n\n/**\n * Create a promise resolver\n * @returns {{promise: Promise, resolve: Function, reject: Function}} resolver\n */\nPromise.defer = function () {\n  var resolver = {};\n\n  resolver.promise = new Promise(function (resolve, reject) {\n    resolver.resolve = resolve;\n    resolver.reject = reject;\n  });\n\n  return resolver;\n};\n\n/**\n * Create a cancellation error\n * @param {String} [message]\n * @extends Error\n */\nfunction CancellationError(message) {\n  this.message = message || 'promise cancelled';\n  this.stack = (new Error()).stack;\n}\n\nCancellationError.prototype = new Error();\nCancellationError.prototype.constructor = Error;\nCancellationError.prototype.name = 'CancellationError';\n\nPromise.CancellationError = CancellationError;\n\n\n/**\n * Create a timeout error\n * @param {String} [message]\n * @extends Error\n */\nfunction TimeoutError(message) {\n  this.message = message || 'timeout exceeded';\n  this.stack = (new Error()).stack;\n}\n\nTimeoutError.prototype = new Error();\nTimeoutError.prototype.constructor = Error;\nTimeoutError.prototype.name = 'TimeoutError';\n\nPromise.TimeoutError = TimeoutError;\n\n\nmodule.exports = Promise;\n", "'use strict';\n\nvar Promise = require('./Promise');\nvar environment = require('./environment');\nvar requireFoolWebpack = require('./requireFoolWebpack');\n\n/**\n * Special message sent by parent which causes a child process worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n/**\n * If sending `TERMINATE_METHOD_ID` does not cause the child process to exit in this many milliseconds,\n * force-kill the child process.\n */\nvar CHILD_PROCESS_EXIT_TIMEOUT = 1000;\n\nfunction ensureWorkerThreads() {\n  var WorkerThreads = tryRequireWorkerThreads()\n  if (!WorkerThreads) {\n    throw new Error('WorkerPool: workerType = \\'thread\\' is not supported, Node >= 11.7.0 required')\n  }\n\n  return WorkerThreads;\n}\n\n// check whether Worker is supported by the browser\nfunction ensureWebWorker() {\n  // Workaround for a bug in PhantomJS (Or QtWebkit): https://github.com/ariya/phantomjs/issues/14534\n  if (typeof Worker !== 'function' && (typeof Worker !== 'object' || typeof Worker.prototype.constructor !== 'function')) {\n    throw new Error('WorkerPool: Web Workers not supported');\n  }\n}\n\nfunction tryRequireWorkerThreads() {\n  try {\n    return requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads available (old version of node.js)\n      return null;\n    } else {\n      throw error;\n    }\n  }\n}\n\n// get the default worker script\nfunction getDefaultWorker() {\n  if (environment.platform === 'browser') {\n    // test whether the browser supports all features that we need\n    if (typeof Blob === 'undefined') {\n      throw new Error('Blob not supported by the browser');\n    }\n    if (!window.URL || typeof window.URL.createObjectURL !== 'function') {\n      throw new Error('URL.createObjectURL not supported by the browser');\n    }\n\n    // use embedded worker.js\n    var blob = new Blob([require('./generated/embeddedWorker')], {type: 'text/javascript'});\n    return window.URL.createObjectURL(blob);\n  }\n  else {\n    // use external worker.js in current directory\n    return __dirname + '/worker.js';\n  }\n}\n\nfunction setupWorker(script, options) {\n  if (options.workerType === 'web') { // browser only\n    ensureWebWorker();\n    return setupBrowserWorker(script, Worker);\n  } else if (options.workerType === 'thread') { // node.js only\n    WorkerThreads = ensureWorkerThreads();\n    return setupWorkerThreadWorker(script, WorkerThreads);\n  } else if (options.workerType === 'process' || !options.workerType) { // node.js only\n    return setupProcessWorker(script, resolveForkOptions(options), requireFoolWebpack('child_process'));\n  } else { // options.workerType === 'auto' or undefined\n    if (environment.platform === 'browser') {\n      ensureWebWorker();\n      return setupBrowserWorker(script, Worker);\n    }\n    else { // environment.platform === 'node'\n      var WorkerThreads = tryRequireWorkerThreads();\n      if (WorkerThreads) {\n        return setupWorkerThreadWorker(script, WorkerThreads);\n      } else {\n        return setupProcessWorker(script, resolveForkOptions(options), requireFoolWebpack('child_process'));\n      }\n    }\n  }\n}\n\nfunction setupBrowserWorker(script, Worker) {\n  // create the web worker\n  var worker = new Worker(script);\n\n  worker.isBrowserWorker = true;\n  // add node.js API to the web worker\n  worker.on = function (event, callback) {\n    this.addEventListener(event, function (message) {\n      callback(message.data);\n    });\n  };\n  worker.send = function (message) {\n    this.postMessage(message);\n  };\n  return worker;\n}\n\nfunction setupWorkerThreadWorker(script, WorkerThreads) {\n  var worker = new WorkerThreads.Worker(script, {\n    stdout: false, // automatically pipe worker.STDOUT to process.STDOUT\n    stderr: false  // automatically pipe worker.STDERR to process.STDERR\n  });\n  worker.isWorkerThread = true;\n  // make the worker mimic a child_process\n  worker.send = function(message) {\n    this.postMessage(message);\n  };\n\n  worker.kill = function() {\n    this.terminate();\n    return true;\n  };\n\n  worker.disconnect = function() {\n    this.terminate();\n  };\n\n  return worker;\n}\n\nfunction setupProcessWorker(script, options, child_process) {\n  // no WorkerThreads, fallback to sub-process based workers\n  var worker = child_process.fork(\n    script,\n    options.forkArgs,\n    options.forkOpts\n  );\n\n  worker.isChildProcess = true;\n  return worker;\n}\n\n// add debug flags to child processes if the node inspector is active\nfunction resolveForkOptions(opts) {\n  opts = opts || {};\n\n  var processExecArgv = process.execArgv.join(' ');\n  var inspectorActive = processExecArgv.indexOf('--inspect') !== -1;\n  var debugBrk = processExecArgv.indexOf('--debug-brk') !== -1;\n\n  var execArgv = [];\n  if (inspectorActive) {\n    execArgv.push('--inspect=' + opts.debugPort);\n\n    if (debugBrk) {\n      execArgv.push('--debug-brk');\n    }\n  }\n\n  process.execArgv.forEach(function(arg) {\n    if (arg.indexOf('--max-old-space-size') > -1) {\n      execArgv.push(arg)\n    }\n  })\n\n  return Object.assign({}, opts, {\n    forkArgs: opts.forkArgs,\n    forkOpts: Object.assign({}, opts.forkOpts, {\n      execArgv: (opts.forkOpts && opts.forkOpts.execArgv || [])\n      .concat(execArgv)\n    })\n  });\n}\n\n/**\n * Converts a serialized error to Error\n * @param {Object} obj Error that has been serialized and parsed to object\n * @return {Error} The equivalent Error.\n */\nfunction objectToError (obj) {\n  var temp = new Error('')\n  var props = Object.keys(obj)\n\n  for (var i = 0; i < props.length; i++) {\n    temp[props[i]] = obj[props[i]]\n  }\n\n  return temp\n}\n\n/**\n * A WorkerHandler controls a single worker. This worker can be a child process\n * on node.js or a WebWorker in a browser environment.\n * @param {String} [script] If no script is provided, a default worker with a\n *                          function run will be created.\n * @param {WorkerPoolOptions} _options See docs\n * @constructor\n */\nfunction WorkerHandler(script, _options) {\n  var me = this;\n  var options = _options || {};\n\n  this.script = script || getDefaultWorker();\n  this.worker = setupWorker(this.script, options);\n  this.debugPort = options.debugPort;\n  this.forkOpts = options.forkOpts;\n  this.forkArgs = options.forkArgs;\n\n  // The ready message is only sent if the worker.add method is called (And the default script is not used)\n  if (!script) {\n    this.worker.ready = true;\n  }\n\n  // queue for requests that are received before the worker is ready\n  this.requestQueue = [];\n  this.worker.on('message', function (response) {\n    if (me.terminated) {\n      return;\n    }\n    if (typeof response === 'string' && response === 'ready') {\n      me.worker.ready = true;\n      dispatchQueuedRequests();\n    } else {\n      // find the task from the processing queue, and run the tasks callback\n      var id = response.id;\n      var task = me.processing[id];\n      if (task !== undefined) {\n        if (response.isEvent) {\n          if (task.options && typeof task.options.on === 'function') {\n            task.options.on(response.payload);\n          }\n        } else {\n          // remove the task from the queue\n          delete me.processing[id];\n\n          // test if we need to terminate\n          if (me.terminating === true) {\n            // complete worker termination if all tasks are finished\n            me.terminate();\n          }\n\n          // resolve the task's promise\n          if (response.error) {\n            task.resolver.reject(objectToError(response.error));\n          }\n          else {\n            task.resolver.resolve(response.result);\n          }\n        }\n      }\n    }\n  });\n\n  // reject all running tasks on worker error\n  function onError(error) {\n    me.terminated = true;\n\n    for (var id in me.processing) {\n      if (me.processing[id] !== undefined) {\n        me.processing[id].resolver.reject(error);\n      }\n    }\n    me.processing = Object.create(null);\n  }\n\n  // send all queued requests to worker\n  function dispatchQueuedRequests()\n  {\n    for(const request of me.requestQueue.splice(0)) {\n      me.worker.send(request);\n    }\n  }\n\n  var worker = this.worker;\n  // listen for worker messages error and exit\n  this.worker.on('error', onError);\n  this.worker.on('exit', function (exitCode, signalCode) {\n    var message = 'Workerpool Worker terminated Unexpectedly\\n';\n\n    message += '    exitCode: `' + exitCode + '`\\n';\n    message += '    signalCode: `' + signalCode + '`\\n';\n\n    message += '    workerpool.script: `' +  me.script + '`\\n';\n    message += '    spawnArgs: `' +  worker.spawnargs + '`\\n';\n    message += '    spawnfile: `' + worker.spawnfile + '`\\n'\n\n    message += '    stdout: `' + worker.stdout + '`\\n'\n    message += '    stderr: `' + worker.stderr + '`\\n'\n\n    onError(new Error(message));\n  });\n\n  this.processing = Object.create(null); // queue with tasks currently in progress\n\n  this.terminating = false;\n  this.terminated = false;\n  this.terminationHandler = null;\n  this.lastId = 0;\n}\n\n/**\n * Get a list with methods available on the worker.\n * @return {Promise.<String[], Error>} methods\n */\nWorkerHandler.prototype.methods = function () {\n  return this.exec('methods');\n};\n\n/**\n * Execute a method with given parameters on the worker\n * @param {String} method\n * @param {Array} [params]\n * @param {{resolve: Function, reject: Function}} [resolver]\n * @param {ExecOptions}  [options]\n * @return {Promise.<*, Error>} result\n */\nWorkerHandler.prototype.exec = function(method, params, resolver, options) {\n  if (!resolver) {\n    resolver = Promise.defer();\n  }\n\n  // generate a unique id for the task\n  var id = ++this.lastId;\n\n  // register a new task as being in progress\n  this.processing[id] = {\n    id: id,\n    resolver: resolver,\n    options: options\n  };\n\n  // build a JSON-RPC request\n  var request = {\n    id: id,\n    method: method,\n    params: params\n  };\n\n  if (this.terminated) {\n    resolver.reject(new Error('Worker is terminated'));\n  } else if (this.worker.ready) {\n    // send the request to the worker\n    this.worker.send(request);\n  } else {\n    this.requestQueue.push(request);\n  }\n\n  // on cancellation, force the worker to terminate\n  var me = this;\n  return resolver.promise.catch(function (error) {\n    if (error instanceof Promise.CancellationError || error instanceof Promise.TimeoutError) {\n      // remove this task from the queue. It is already rejected (hence this\n      // catch event), and else it will be rejected again when terminating\n      delete me.processing[id];\n\n      // terminate worker\n      return me.terminateAndNotify(true)\n        .then(function() {\n          throw error;\n        }, function(err) {\n          throw err;\n        });\n    } else {\n      throw error;\n    }\n  })\n};\n\n/**\n * Test whether the worker is working or not\n * @return {boolean} Returns true if the worker is busy\n */\nWorkerHandler.prototype.busy = function () {\n  return Object.keys(this.processing).length > 0;\n};\n\n/**\n * Terminate the worker.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {function} [callback=null] If provided, will be called when process terminates.\n */\nWorkerHandler.prototype.terminate = function (force, callback) {\n  var me = this;\n  if (force) {\n    // cancel all tasks in progress\n    for (var id in this.processing) {\n      if (this.processing[id] !== undefined) {\n        this.processing[id].resolver.reject(new Error('Worker terminated'));\n      }\n    }\n    this.processing = Object.create(null);\n  }\n\n  if (typeof callback === 'function') {\n    this.terminationHandler = callback;\n  }\n  if (!this.busy()) {\n    // all tasks are finished. kill the worker\n    var cleanup = function(err) {\n      me.terminated = true;\n      if (me.worker != null && me.worker.removeAllListeners) {\n        // removeAllListeners is only available for child_process\n        me.worker.removeAllListeners('message');\n      }\n      me.worker = null;\n      me.terminating = false;\n      if (me.terminationHandler) {\n        me.terminationHandler(err, me);\n      } else if (err) {\n        throw err;\n      }\n    }\n\n    if (this.worker) {\n      if (typeof this.worker.kill === 'function') {\n        if (this.worker.killed) {\n          cleanup(new Error('worker already killed!'));\n          return;\n        }\n\n        if (this.worker.isChildProcess) {\n          var cleanExitTimeout = setTimeout(function() {\n            if (me.worker) {\n              me.worker.kill();\n            }\n          }, CHILD_PROCESS_EXIT_TIMEOUT);\n\n          this.worker.once('exit', function() {\n            clearTimeout(cleanExitTimeout);\n            if (me.worker) {\n              me.worker.killed = true;\n            }\n            cleanup();\n          });\n\n          if (this.worker.ready) {\n            this.worker.send(TERMINATE_METHOD_ID);\n          } else {\n            this.requestQueue.push(TERMINATE_METHOD_ID)\n          }\n        } else {\n          // worker_thread\n          this.worker.kill();\n          this.worker.killed = true;\n          cleanup();\n        }\n        return;\n      }\n      else if (typeof this.worker.terminate === 'function') {\n        this.worker.terminate(); // web worker\n        this.worker.killed = true;\n      }\n      else {\n        throw new Error('Failed to terminate worker');\n      }\n    }\n    cleanup();\n  }\n  else {\n    // we can't terminate immediately, there are still tasks being executed\n    this.terminating = true;\n  }\n};\n\n/**\n * Terminate the worker, returning a Promise that resolves when the termination has been done.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<WorkerHandler, Error>}\n */\nWorkerHandler.prototype.terminateAndNotify = function (force, timeout) {\n  var resolver = Promise.defer();\n  if (timeout) {\n    resolver.promise.timeout = timeout;\n  }\n  this.terminate(force, function(err, worker) {\n    if (err) {\n      resolver.reject(err);\n    } else {\n      resolver.resolve(worker);\n    }\n  });\n  return resolver.promise;\n};\n\nmodule.exports = WorkerHandler;\nmodule.exports._tryRequireWorkerThreads = tryRequireWorkerThreads;\nmodule.exports._setupProcessWorker = setupProcessWorker;\nmodule.exports._setupBrowserWorker = setupBrowserWorker;\nmodule.exports._setupWorkerThreadWorker = setupWorkerThreadWorker;\nmodule.exports.ensureWorkerThreads = ensureWorkerThreads;\n", "'use strict';\n\nvar MAX_PORTS = 65535;\nmodule.exports = DebugPortAllocator;\nfunction DebugPortAllocator() {\n  this.ports = Object.create(null);\n  this.length = 0;\n}\n\nDebugPortAllocator.prototype.nextAvailableStartingAt = function(starting) {\n  while (this.ports[starting] === true) {\n    starting++;\n  }\n\n  if (starting >= MAX_PORTS) {\n    throw new Error('WorkerPool debug port limit reached: ' + starting + '>= ' + MAX_PORTS );\n  }\n\n  this.ports[starting] = true;\n  this.length++;\n  return starting;\n};\n\nDebugPortAllocator.prototype.releasePort = function(port) {\n  delete this.ports[port];\n  this.length--;\n};\n\n", "var requireFoolWebpack = require('./requireFoolWebpack');\n\n// source: https://github.com/flexdinesh/browser-or-node\nvar isNode = function (nodeProcess) {\n  return (\n    typeof nodeProcess !== 'undefined' &&\n    nodeProcess.versions != null &&\n    nodeProcess.versions.node != null\n  );\n}\nmodule.exports.isNode = isNode\n\n// determines the JavaScript platform: browser or node\nmodule.exports.platform = typeof process !== 'undefined' && isNode(process)\n  ? 'node'\n  : 'browser';\n\n// determines whether the code is running in main thread or not\n// note that in node.js we have to check both worker_thread and child_process\nvar worker_threads = tryRequireFoolWebpack('worker_threads');\nmodule.exports.isMainThread = module.exports.platform === 'node'\n  ? ((!worker_threads || worker_threads.isMainThread) && !process.connected)\n  : typeof Window !== 'undefined';\n\n// determines the number of cpus available\nmodule.exports.cpus = module.exports.platform === 'browser'\n  ? self.navigator.hardwareConcurrency\n  : requireFoolWebpack('os').cpus().length;\n\nfunction tryRequireFoolWebpack (module) {\n  try {\n    return requireFoolWebpack(module);\n  } catch(err) {\n    return null\n  }\n}\n", "/**\n * embeddedWorker.js contains an embedded version of worker.js.\n * This file is automatically generated,\n * changes made in this file will be overwritten.\n */\nmodule.exports = \"!function(){var __webpack_exports__={};!function(){var exports=__webpack_exports__,__webpack_unused_export__;function _typeof(r){return(_typeof=\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&\\\"function\\\"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?\\\"symbol\\\":typeof r})(r)}var requireFoolWebpack=eval(\\\"typeof require !== 'undefined' ? require : function (module) { throw new Error('Module \\\\\\\" + module + \\\\\\\" not found.') }\\\"),TERMINATE_METHOD_ID=\\\"__workerpool-terminate__\\\",worker={exit:function(){}},WorkerThreads,parentPort;if(\\\"undefined\\\"!=typeof self&&\\\"function\\\"==typeof postMessage&&\\\"function\\\"==typeof addEventListener)worker.on=function(r,e){addEventListener(r,function(r){e(r.data)})},worker.send=function(r){postMessage(r)};else{if(\\\"undefined\\\"==typeof process)throw new Error(\\\"Script must be executed as a worker\\\");try{WorkerThreads=requireFoolWebpack(\\\"worker_threads\\\")}catch(error){if(\\\"object\\\"!==_typeof(error)||null===error||\\\"MODULE_NOT_FOUND\\\"!==error.code)throw error}WorkerThreads&&null!==WorkerThreads.parentPort?(parentPort=WorkerThreads.parentPort,worker.send=parentPort.postMessage.bind(parentPort),worker.on=parentPort.on.bind(parentPort)):(worker.on=process.on.bind(process),worker.send=process.send.bind(process),worker.on(\\\"disconnect\\\",function(){process.exit(1)}),worker.exit=process.exit.bind(process))}function convertError(o){return Object.getOwnPropertyNames(o).reduce(function(r,e){return Object.defineProperty(r,e,{value:o[e],enumerable:!0})},{})}function isPromise(r){return r&&\\\"function\\\"==typeof r.then&&\\\"function\\\"==typeof r.catch}worker.methods={},worker.methods.run=function(r,e){r=new Function(\\\"return (\\\"+r+\\\").apply(null, arguments);\\\");return r.apply(r,e)},worker.methods.methods=function(){return Object.keys(worker.methods)};var currentRequestId=null;worker.on(\\\"message\\\",function(e){if(e===TERMINATE_METHOD_ID)return worker.exit(0);try{var r=worker.methods[e.method];if(!r)throw new Error('Unknown method \\\"'+e.method+'\\\"');currentRequestId=e.id;var o=r.apply(r,e.params);isPromise(o)?o.then(function(r){worker.send({id:e.id,result:r,error:null}),currentRequestId=null}).catch(function(r){worker.send({id:e.id,result:null,error:convertError(r)}),currentRequestId=null}):(worker.send({id:e.id,result:o,error:null}),currentRequestId=null)}catch(r){worker.send({id:e.id,result:null,error:convertError(r)})}}),worker.register=function(r){if(r)for(var e in r)r.hasOwnProperty(e)&&(worker.methods[e]=r[e]);worker.send(\\\"ready\\\")},worker.emit=function(r){currentRequestId&&worker.send({id:currentRequestId,isEvent:!0,payload:r})},__webpack_unused_export__=worker.register,worker.emit}()}();\";\n", "// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\' ' +\n    '? require ' +\n    ': function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\nmodule.exports = requireFoolWebpack;\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\n\n// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\'' +\n    ' ? require' +\n    ' : function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message) {\n    postMessage(message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n  } else {\n    worker.on = process.on.bind(process);\n    worker.send = process.send.bind(process);\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(null, arguments);');\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.exit(0);\n  }\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              worker.send({\n                id: request.id,\n                result: result,\n                error: null\n              });\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err)\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        worker.send({\n          id: request.id,\n          result: result,\n          error: null\n        });\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} methods\n */\nworker.register = function (methods) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n      }\n    }\n  }\n\n  worker.send('ready');\n\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "var environment = require('./environment');\n\n/**\n * Create a new worker pool\n * @param {string} [script]\n * @param {WorkerPoolOptions} [options]\n * @returns {Pool} pool\n */\nexports.pool = function pool(script, options) {\n  var Pool = require('./Pool');\n\n  return new Pool(script, options);\n};\n\n/**\n * Create a worker and optionally register a set of methods to the worker.\n * @param {Object} [methods]\n */\nexports.worker = function worker(methods) {\n  var worker = require('./worker');\n  worker.add(methods);\n};\n\n/**\n * Sends an event to the parent worker pool.\n * @param {any} payload \n */\nexports.workerEmit = function workerEmit(payload) {\n  var worker = require('./worker');\n  worker.emit(payload);\n};\n\n/**\n * Create a promise.\n * @type {Promise} promise\n */\nexports.Promise = require('./Promise');\n\nexports.platform = environment.platform;\nexports.isMainThread = environment.isMainThread;\nexports.cpus = environment.cpus;"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "this", "Promise", "require", "Worker<PERSON><PERSON>ler", "environment", "DEBUG_PORT_ALLOCATOR", "Pool", "script", "options", "workers", "tasks", "forkArgs", "Object", "freeze", "forkOpts", "debugPortStart", "nodeWorker", "workerType", "maxQueueSize", "Infinity", "onCreateWorker", "onTerminateWorker", "maxWorkers", "isNumber", "isInteger", "TypeError", "validateMaxWorkers", "Math", "max", "cpus", "minWorkers", "validateMinWorkers", "_ensureMinWorkers", "_boundNext", "_next", "bind", "ensureWorkerThreads", "value", "round", "prototype", "exec", "method", "params", "Array", "isArray", "resolver", "defer", "length", "Error", "task", "timeout", "push", "originalTimeout", "promise", "delay", "indexOf", "call", "String", "proxy", "arguments", "pool", "then", "methods", "for<PERSON>ach", "slice", "worker", "_get<PERSON><PERSON><PERSON>", "me", "shift", "pending", "terminated", "_remove<PERSON><PERSON>ker", "i", "busy", "_createWorkerHandler", "releasePort", "debugPort", "_removeWorkerFromList", "resolve", "reject", "terminate", "err", "index", "splice", "force", "removeW<PERSON>ker", "promises", "termPromise", "terminateAndNotify", "always", "all", "stats", "totalWorkers", "busyWorkers", "filter", "idleWorkers", "pendingTasks", "activeTasks", "overridenParams", "nextAvailableStartingAt", "handler", "parent", "SyntaxError", "_onSuccess", "_onFail", "resolved", "rejected", "_process", "onSuccess", "onFail", "s", "_then", "f", "_resolve", "result", "fn", "_reject", "error", "cancel", "CancellationError", "timer", "setTimeout", "TimeoutError", "clearTimeout", "callback", "res", "message", "stack", "remaining", "results", "p", "constructor", "name", "requireFoolWebpack", "TERMINATE_METHOD_ID", "WorkerThreads", "tryRequireWorkerThreads", "ensureWebWorker", "Worker", "code", "setupBrowserWorker", "isBrowserWorker", "on", "event", "addEventListener", "data", "send", "postMessage", "setupWorkerThreadWorker", "stdout", "stderr", "isWorkerThread", "kill", "disconnect", "setupProcessWorker", "child_process", "fork", "isChildProcess", "resolveForkOptions", "opts", "processExecArgv", "process", "execArgv", "join", "inspectorActive", "debugBrk", "arg", "assign", "concat", "_options", "onError", "id", "processing", "undefined", "create", "platform", "Blob", "window", "URL", "createObjectURL", "blob", "type", "__dirname", "getDefaultWorker", "setupWorker", "ready", "requestQueue", "response", "request", "dispatchQueuedRequests", "isEvent", "payload", "terminating", "obj", "temp", "props", "keys", "objectToError", "exitCode", "signalCode", "spawnargs", "spawnfile", "<PERSON><PERSON><PERSON><PERSON>", "lastId", "cleanup", "removeAllListeners", "killed", "cleanExitTimeout", "once", "_tryRequireWorkerThreads", "_setupProcessWorker", "_setupBrowserWorker", "_setupWorkerThreadWorker", "DebugPortAllocator", "ports", "starting", "port", "isNode", "nodeProcess", "versions", "node", "worker_threads", "tryRequireFoolWebpack", "isMainThread", "connected", "Window", "navigator", "hardwareConcurrency", "eval", "exit", "parentPort", "convertError", "getOwnPropertyNames", "reduce", "product", "defineProperty", "enumerable", "isPromise", "run", "args", "Function", "apply", "currentRequestId", "register", "hasOwnProperty", "emit", "add", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "workerEmit"], "sourceRoot": ""}