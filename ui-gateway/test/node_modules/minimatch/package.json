{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "name": "minimatch", "description": "a glob matcher in javascript", "version": "5.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "main": "minimatch.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "engines": {"node": ">=10"}, "dependencies": {"brace-expansion": "^2.0.1"}, "devDependencies": {"tap": "^15.1.6"}, "license": "ISC", "files": ["minimatch.js", "lib"]}