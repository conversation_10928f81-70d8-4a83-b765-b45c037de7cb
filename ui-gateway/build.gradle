import com.github.jk1.license.render.*
import com.github.jk1.license.importer.*

plugins {
	id 'org.springframework.boot' version '2.2.2.RELEASE'
	id 'io.spring.dependency-management' version '1.0.8.RELEASE'
	id 'java'
    id "org.sonarqube" version "3.3"
    id 'com.github.jk1.dependency-license-report' version '1.17'
}

group = 'com.integral'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'

repositories {
	maven {
		url "http://nexus.sca.dc.integral.net:8081/repository/integral-modules"
	}

	maven {
		url "http://nexus.sca.dc.integral.net:8081/repository/3rdParty"
	}

	maven {
		url "http://nexus.sca.dc.integral.net:8081/repository/local-maven"
	}

	// TODO - this should be removed later.
	maven {
		url 'https://build.shibboleth.net/nexus/content/repositories/releases/'
	}

	maven {
		url "http://nexus.sca.dc.integral.net:8081/repository/local-jcenter"
	}

	mavenCentral()
}

configurations {
  all*.exclude group:"org.eclipse.jetty.orbit"
}

ext.repoSrc = "$rootDir"
def libPath = "$repoSrc/lib"

def SPRING_VERSION = "5.2.3.RELEASE"

ext{
	mapstructVersion = "1.4.1.Final"
	springCloudVersion = "Hoxton.SR10"
}

dependencies {
	configurations.all {
		//exclude group: "org.springframework.boot", module: "spring-boot-starter-logging"
//		exclude group: "org.eclipse.jetty.aggregate", module: "jetty-all-server"
	}

	implementation ('org.springframework.boot:spring-boot-starter-web') {
		//exclude group: "org.springframework.boot", module: "spring-boot-starter-logging"
	}

	implementation ("org.springframework.boot:spring-boot-starter-tomcat")
	implementation 'org.springframework.boot:spring-boot-starter-websocket'

	/*implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.security:spring-security-test'*/

	testImplementation('org.springframework.boot:spring-boot-starter-test') {
		exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
	}

	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'io.micrometer:micrometer-core'
	implementation 'io.micrometer:micrometer-registry-prometheus'

	implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
	implementation 'org.springframework.boot:spring-boot-starter-hateoas'

	implementation 'org.springframework.boot:spring-boot-starter-validation'

	compile "org.springdoc:springdoc-openapi-ui:1.5.2"

	/*compile (group: 'org.springframework.security.extensions', name: 'spring-security-saml2-core', version: '1.0.10.RELEASE'){
		exclude (group: 'org.bouncycastle', module: 'bcprov-ext-jdk15on')
	}*/

	compile fileTree(dir: libPath, include: 'common-services.jar')
//	compile fileTree(dir: libPath, include: 'EMSClient.jar')
	compile (group: "com.integral", name: "oracle-persistence", version: ORACLE_PERSISTENCE_VERSION){
		exclude group: "org.apache.tomcat", module: "catalina"
	}

	compile "com.rabbitmq:amqp-client:3.6.0"
	compile "org.mongodb:mongo-java-driver:1.0.0"
//	compile "com.integral:messaging:1.0.3-SNAPSHOT"
	compile group: "com.integral", name: "monitor-core", version: MONITOR_CORE_VERSION
	compile "org.json:json:20080701"
	compile group: "com.integral", name:"rds",version: RDS_VERSION
	compile group: "com.integral", name:"log",version: '2.3-SNAPSHOT'
	compile group: "com.integral", name:"services",version: MODULES_SERVICES
	compile group: "com.integral", name: 'riskmanagement-common', version :DARWIN_RISKMANAGEMENT_COMMON_VERSION
	compile group: "com.integral", name:"darwin-auth", version:DARWIN_AUTH_VERSION
	compile group: "com.integral", name: "model", version: MODEL_VERSION
	implementation group: "com.integral", name:"portal-service-apps", version:DARWIN_VERSION
//	implementation group: "com.integral", name:"portal-api", version:PORTAL_API_VERSION
	implementation group: "com.integral", name: "price-making-commons", version: PRICE_MAKING_COMMONS_VERSION
	implementation group: "com.integral", name: "darwin-taggable-entities", version: DARWIN_VERSION
	compile "struts:struts:1.0.0"
	implementation group: 'com.google.code.gson', name: 'gson', version: '2.8.6'
//	compile "javax.validation:validation-api:2.0.1.Final"
	compile "commons-io:commons-io:1.1"
	compile "org.restlet.jee:org.restlet:1.0.0"
	compile "org.restlet.jee:org.restlet.ext.json:1.0.0"
	compile group: 'com.fasterxml.jackson.core', name: 'jackson-databind', version: '2.10.2'
	compile group: 'com.fasterxml.jackson.core', name: 'jackson-core', version: '2.10.2'
	compile "org.hibernate:hibernate-validator:5.3.1.Final"
	compile "com.ning:async-http-client:1.6.2"
	compile "aopalliance:aopalliance:1.0"
	compile "aspectj:aspectjweaver:1.6.12"
	compile "commons-fileupload:commons-fileupload:1.1.1"
	compile "joda-time:joda-time:2.2"
	compile "org.owasp.esapi:esapi:2.0.1"
	runtime "net.sf.kxml:kxml2:2.3.0"
	compile group: 'org.eclipse.jetty', name: 'jetty-server', version: '9.4.28.v20200408'
	compile group: 'quickfixj', name: 'quickfixj-all', version: '1.4.2'
	compile "org.apache.mina:mina-core:1.1.7"
	compile group: 'org.apache.logging.log4j', name: 'log4j-api', version: '2.16.0'
	implementation group: 'org.apache.logging.log4j', name: 'log4j-to-slf4j', version: '2.16.0'

	compile group: 'com.hazelcast', name: 'hazelcast', version: '5.1.2'
	compile group: 'org.mapstruct', name: 'mapstruct', version: '1.4.1.Final'

	compile("org.mapstruct:mapstruct-jdk8:${mapstructVersion}")
	compile ("io.jsonwebtoken:jjwt:0.9.1")
	testImplementation("com.squareup.okhttp3:okhttp:4.10.0")
	annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
}

dependencyManagement {
	imports {
		mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
	}
}

compileJava {
	options.annotationProcessorPath = configurations.annotationProcessor

	// if you need to configure mapstruct component model
//	options.compilerArgs << "-Amapstruct.defaultComponentModel=spring"
}

task copyToLib(type: Copy) {
	into "${rootDir}/lib"
	from "${rootDir}/build/libs"
}

test {
	useJUnitPlatform()
}

task configure {

	ext.emptyFolder = { file -> 
		if (file.exists()) {
            if (file.isDirectory()) {
                for (File f : file.listFiles()) {
                    _emptyFolder(f);
                }
            }
        } else {
            System.out.println("path does not exist : " + file);
        }
	}
	
	ext._emptyFolder = { f ->
		if (f.exists()) {
            if (f.isDirectory()) {
                for (File s : f.listFiles()) {
                    _emptyFolder(s);
                }
            }
            f.delete();
        } else {
            System.out.println("path does not exist : " + f);
        }
	}
	
	ext.copyFolder = { srcFile, src, dest ->
		if (srcFile.exists()) {
            try {			
        //        System.out.println("copyFolder src="+src+", srcFile="+srcFile);  	
                String destFilePath = srcFile.getAbsolutePath().replace("\\", "/").replace(src, dest);
				System.out.println(destFilePath);
                File destFile = new File(destFilePath);

                java.nio.file.Files.copy(srcFile.toPath(), destFile.toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING);

                if (srcFile.isDirectory()) {
                    for (File f : srcFile.listFiles()) {
                        copyFolder(f, src, dest);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            System.out.println("file does not exist : " + srcFile);
        }
	}

	ext.configureStartFile = { srcFile, appRoot ->
		String contents = new File( "${srcFile}" ).getText( 'UTF-8' )
		contents = contents.replaceAll( '@uig.root@', appRoot )
		contents = contents.replaceAll( 'default', 'local' )
		//contents = contents.replaceAll( 'lib', 'build/libs' )
		new File( "${srcFile}" ).write( contents, 'UTF-8' )
	}

	ext.configureWebSecurity = { srcFile, appRoot ->
		String contents = new File( "${srcFile}" ).getText( 'UTF-8' )
		contents = contents.replaceAll( '@uig.root@', appRoot )
		contents = contents.replaceAll( 'default', 'local' )
		new File( "${srcFile}" ).write( contents, 'UTF-8' )
	}

	doLast {
		String hostname = InetAddress.getLocalHost().getHostName();

		File configFile = file('runtime.properties');
		Properties props = new Properties();
		props.load(configFile.newDataInputStream());
		println "Read configFile" + props;

		//get current jdk version and jdk home
		def currentJvm = org.gradle.internal.jvm.Jvm.current();
		String jdkVersion = currentJvm.getJavaVersion();
		String jdkHome = currentJvm.getJavaHome().getAbsolutePath().replace("\\","/");

		String projectDirRoot = projectDir.absolutePath.replace("\\", "/");
		String appRoot = projectDirRoot;
		String appLogRoot = appRoot + "/logs" ;
		String installDir = "";
		String absolutePath = appRoot + "/" + installDir;

		String binDir = absolutePath +  "bin";
		String confDir = absolutePath + "conf";
		
		String defaultBinDir = binDir + "/default";
		String defaultConfDir = confDir + "/default";
		
		String localBinDir = binDir +  "/local";
		String localConfDir = confDir + "/local";

		emptyFolder(new File(localBinDir));
		emptyFolder(new File(localConfDir));
		
		copyFolder(new File(defaultBinDir), defaultBinDir, localBinDir);
		copyFolder(new File(defaultConfDir), defaultConfDir, localConfDir);

		println "@uig.root@ --> " + appRoot;
		println "@uig.log.root@ --> " + appLogRoot;

		println "JDK used is " + jdkVersion + " at " + jdkHome;

		String appType = "usrv";
		println "Configuring runtime from " + appType;

		configureStartFile(localBinDir + '/startIntegral.sh', appRoot)
		configureStartFile(localBinDir + '/startIntegral.bat', appRoot)
		configureWebSecurity(localConfDir + '/WebServerSecurity.properties', appRoot)

		def files  = [
				localConfDir + '/hostname.properties',
				localConfDir + '/logback.xml',
				localConfDir + '/XmlConfigLoader.properties',
				localConfDir + '/webapp/fxi.xml',
				localConfDir + '/application.properties',
				localConfDir + '/application.yml',
				localBinDir + '/setEnv.sh',
				localBinDir + '/setEnv.bat'
		]

		files.each { fileName  ->
			println "Processing ${fileName}"
			String contents = new File( "${fileName}" ).getText( 'UTF-8' )
			contents = contents.replaceAll( '@java.home@', jdkHome )
			contents = contents.replaceAll( '@uig.root@', appRoot )
			contents = contents.replaceAll( '@uig.log.root@', appLogRoot )
			contents = contents.replaceAll( '@uig.port@', props['uig.port'] )
			contents = contents.replaceAll( '@jmsproxy.port@', props['jmsproxy.port'] )
			contents = contents.replaceAll( '@websocket.port@', props['websocket.port'] )
			contents = contents.replaceAll( '@jnp.port@', props['jnp.port'] )
			contents = contents.replaceAll( '@http.port@', props['http.port'] )
			contents = contents.replaceAll( '@jmx.port@', props['jmx.port'] )
			contents = contents.replaceAll( '@debug.port@', props['debug.port'] )
			contents = contents.replaceAll( '@shutdown.port@', props['tomcat.shutdown.port'] )
			contents = contents.replaceAll( '@virtual.server.name@', props['virtualserver.name'] )
			contents = contents.replaceAll( '@user.name@', props['user.name'] )
			contents = contents.replaceAll( '@user.password@', props['user.password'] )
			contents = contents.replaceAll( '@ora.db.server@', props['ora.db.server'] )
			contents = contents.replaceAll( '@ora.db.port@', props['ora.db.port'] )
			contents = contents.replaceAll( '@ora.db.instance@', props['ora.db.instance'] )
			contents = contents.replaceAll( '@user.name.prefix@', props['user.name.prefix'] )
			contents = contents.replaceAll( '@sonic.host.name@', props['sonic.host.name'] )
			contents = contents.replaceAll( '@sonic.tcp.port@', props['sonic.tcp.port'] )
			contents = contents.replaceAll( '@sonic.http.port@', props['sonic.http.port'] )
			contents = contents.replaceAll( '@jms.user.name@', props['jms.user.name'] )
			contents = contents.replaceAll( '@jms.password@', props['jms.password'] )
			contents = contents.replaceAll( '@rabbitmq.url@', props['rabbitmq.url'] )
			contents = contents.replaceAll( '@mongo.url@', props['mongo.url'] )
			contents = contents.replaceAll( '@rds.url@', props['rds.url'] )
			contents = contents.replaceAll( '@consul.ip@', props['consul.ip'] )
			contents = contents.replaceAll( '@consul.port@', props['consul.port'] )
			new File( "${fileName}" ).write( contents, 'UTF-8' )
		};
	}
}

task copyDependencies(type: Copy) {
	from configurations.default
	into 'dependencies'
}

licenseReport{
    excludeGroups = ['com.integral','integral5']
    renderers = [new CsvReportRenderer()]
}

build.finalizedBy(copyToLib)

