#
FROM nexus.sca.dc.integral.net:8082/integral/centos/7.8.2003:jdkzu8.72.0.17-382
USER root
COPY uigDocker /integral/app/uig

RUN mkdir -p /integral/logs/uig && \
    mkdir -p /integral/config/preference/clientpref && \
    mkdir -p /integral/config/preference/pref && \
    chown -R integral:integral /integral/app/uig && \
    chown -R integral:integral /integral/logs/uig && \
    chown -R integral:integral /integral/config/preference/clientpref && \
    chown -R integral:integral /integral/config/preference/pref

USER integral

ENV MEM 4096
ENV HTTP_PORT 8080
ENV JMS_PORT 8878
ENV DBNAME dbname
ENV DBPORT 1521
ENV DBUSER dbusername
ENV DBPASSWD dbpasswd
ENV VSNAME vsname
ENV VSGROUP vsgroup
ENV MONGO_URL mongoUrl
ENV SONIC_HOST sonichost
ENV SONIC_PORT 2507
ENV RABBITMQ_URL rabbitUrl
ENV RDS_URL rdsUrl

ENV CONSUL_IP x.x.x.x
ENV CONSUL_PORT 8500
ENV ENVIRONMENT xxx

ENTRYPOINT ["/integral/app/uig/bin/dockerStartup.sh"]
