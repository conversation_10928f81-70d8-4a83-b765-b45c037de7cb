package com.integral.fxiapi.order.spaces;

import java.sql.Time;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import com.integral.finance.dealing.RequestUtils;
import com.integral.fxiapi.FXIConstants;
import com.integral.is.common.query.DealingModelUtil;
import com.integral.is.common.util.RateRoundingService;
import com.integral.model.dealing.*;
import com.integral.model.dealing.descriptor.algo.AlgoDescriptor;
import com.integral.model.dealing.descriptor.algo.AlgoParameters;
import com.integral.model.dealing.descriptor.algo.AlgoParametersAtBest;
import com.integral.model.dealing.descriptor.algo.AlgoParametersExternalAlgo;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.uig.UIGConstants;
import com.integral.util.IdcUtilC;
import com.integral.util.MathUtilC;
import org.apache.commons.lang.StringUtils;

import com.integral.businessCenter.BusinessCenter;
import com.integral.classification.Classification;
import com.integral.compaction.model.MessageType;
import com.integral.finance.businessCenter.EndOfDayServerC;
import com.integral.finance.businessCenter.EndOfDayService;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.ExecutionFlags;
import com.integral.finance.dealing.RequestClassification;
import com.integral.finance.dealing.facade.OrderStateFacade;
import com.integral.finance.dealing.fx.FXDealLeg;
import com.integral.finance.dealing.fx.FXSingleLegOrder;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
//import com.integral.fix.client.FixConstants;
import com.integral.fxiapi.model.ContingencyParameter;
import com.integral.fxiapi.model.ExecutionStrategyType;
import com.integral.fxiapi.model.Order;
import com.integral.fxiapi.model.OrderExpiryType;
import com.integral.fxiapi.model.OrderMessage;
import com.integral.fxiapi.model.OrderSideType;
import com.integral.fxiapi.model.OrderStatusType;
import com.integral.fxiapi.model.OrderType;
import com.integral.fxiapi.model.Trade;
import com.integral.fxiapi.model.reference.ExecutionState;
import com.integral.fxiapi.order.BaseOrderTranslator;
import com.integral.fxiapi.order.FXIOrderTranslator;
import com.integral.fxiapi.trade.spaces.SingleLegTradeService;
import com.integral.fxiapi.trade.spaces.SingleLegTradeTranslator;
import com.integral.fxiapi.util.DateUtils;
import com.integral.fxiapi.util.FXIApiUtil;
import com.integral.fxiapi.util.FormatUtils;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
//import com.integral.is.oms.OrderConstants;
//import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
//import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
//import com.integral.is.spaces.fx.service.RateRoundingService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageEvent;
import com.integral.message.WorkflowMessage;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.user.Organization;
import com.integral.user.User;

/**
 * <AUTHOR>
 */
public class SingleLegOrderTranslator extends BaseOrderTranslator implements FXIOrderTranslator {
    private static final Log log = LogFactory.getLog(SingleLegOrderTranslator.class);

    private SingleLegOrderTranslator() {
    }

    private static class FXISingleLegOrderTranslatorHolder {
        private static final SingleLegOrderTranslator INSTANCE = new SingleLegOrderTranslator();
    }

    public static SingleLegOrderTranslator getInstance() {
        return FXISingleLegOrderTranslatorHolder.INSTANCE;
    }

    public static FXIOrderTranslator getFXIOrderTranslator() {
        return FXISingleLegOrderTranslatorHolder.INSTANCE;
    }

 //   @Override
    public Order getOrderSubmittedMessage(WorkflowMessage wfMsg, User user) {
        Object msgObj = wfMsg.getObject();
        SingleLegOrder singleLegOrder = (msgObj instanceof SingleLegOrder) ? (SingleLegOrder)msgObj : ((SingleLegTrade)msgObj).getOrderRequest();
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);

        Order apiOrder = generateOrder(singleLegOrder, user);

        OrderMessage message = new OrderMessage();
        apiOrder.setOrderMessage(message);
        message.setOrderId(singleLegOrder.get_id());
        message.setEventName(ORDER_SUBMITTED_EVENT);
        message.setEventDetails(getOrderSubmittedMessageDetails(singleLegOrder, apiOrder));
        message.setTransactionId(singleLegOrder.getTransactionId());

        OrderRequestEventTimes eventTimes = singleLegOrder.getOrderRequestEventTimes();
        if (eventTimes != null) {
            message.setEventTime(dateTimeFormat.format(eventTimes.getSubmissionTime()));
        } else {
            log.error("Failed to get submission time for order " + singleLegOrder.get_id());
        }

        return apiOrder;
    }

    @Override
    public Order generateOrder( Object obj, User user ) {
        return generateOrder( obj, user, false );
    }

    @Override
    public Order generateOrder( Object obj, User user, boolean enrichTrades ) {
        try {
            if ( obj == null ) {
                return null;
            }

            if ( obj instanceof SingleLegOrder ) {
                return generateOrder( ( SingleLegOrder ) obj, user, false );
            }

            if ( obj instanceof com.integral.finance.dealing.Order ) {
                return generateOrder( ( FXSingleLegOrder ) obj, user, true, enrichTrades );
            }

            throw new IllegalArgumentException( "Unsupported: " + this.getClass().getSimpleName() );
        }
        catch ( Exception e ) {
            log.error( "Failed to generate order, ", e );
        }
        return null;
    }

    /**
     * Transforms the given SingleLegOrder to Api Order.
     *
     * @param singleLegOrder
     * @param user
     * @param queryTradeIds if true the returned Api Order will have the corresponding trade ids
     * @return Api Order
     */
    public Order generateOrder(SingleLegOrder singleLegOrder, User user, boolean queryTradeIds) {
        return generateOrder( singleLegOrder, user, queryTradeIds, false );
    }

    /**
     * Transforms the given SingleLegOrder to Api Order.
     *
     * @param singleLegOrder
     * @param user
     * @param queryTradeIds if true the returned Api Order will have the corresponding trade ids
     * @return Api Order
     */
    public Order generateOrder( SingleLegOrder singleLegOrder, User user, boolean queryTradeIds, boolean enrichTrades )
    {
        Order apiOrder = new Order();
        String orderId = singleLegOrder.get_id();
        try {
            Currency dealtCurrency = singleLegOrder.getDealtCurrency();
            if (dealtCurrency == null)
                throw new NullPointerException("dealtCurrency");

            Currency termCurrency = singleLegOrder.getTermCurrency();
            if (termCurrency == null)
                throw new NullPointerException("termCurrency");

            FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();
            if (fxRateBasis == null)
                throw new NullPointerException("fxRateBasis");

            DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
            SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
            DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());

            boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, termCurrency);
            DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

            apiOrder.setOrderId(orderId);
            OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
            if (requestLeg == null)
                throw new NullPointerException("requestLeg");

            copyParentOrderAttributes(user, singleLegOrder, requestLeg, apiOrder, dealtCurrency, fxRateBasis, userDecFormat, dealtCcyFormat, rateFormat, dateFormat, queryTradeIds, enrichTrades);

            if(singleLegOrder.getClientDescriptor().getReferenceQuoteId() != null ) {
                apiOrder.setReferenceQuoteId(singleLegOrder.getClientDescriptor().getReferenceQuoteId());
            }

            apiOrder.setOrderStatus(getOrderStatus(singleLegOrder));
            apiOrder.setFillRate(rateFormat.format(requestLeg.getAverageRate()));
            apiOrder.setAggregatedFills(singleLegOrder.isNettingEnabled());
            double filledAmt = requestLeg.getFilledAmount();
            if(singleLegOrder.isFixing()) {
                filledAmt = requestLeg.getTotalfilledAmount();
            }
            if(singleLegOrder.isNettingEnabled() && !DealingModelUtil.isOrderInFinalState(singleLegOrder) && !singleLegOrder.isShowFills()) {
                apiOrder.setFillRate(rateFormat.format(0));
                apiOrder.setOrderStatus(OrderStatusType.Pending);
            }
            double unFilledAmt = requestLeg.getAmount() - filledAmt;    // was FXLegDealingPrice.getDealtAmount() - filledAmt;

            apiOrder.setTransactionId(singleLegOrder.getTransactionId());
            apiOrder.setFilledAmount(dealtCcyFormat.format(filledAmt));
            apiOrder.setUnfilledAmount(dealtCcyFormat.format(unFilledAmt));
            apiOrder.setParentOrderId(singleLegOrder.get_id());

            double pendingAmt = singleLegOrder.getPendingDealtCurrencyAmount();
            if (pendingAmt > 0 && singleLegOrder.isCancelReceived()) {
                // If order is cancelled by user but trade response is pending, we set 'Cancelling' status.
                apiOrder.setPendingAmount(dealtCcyFormat.format(pendingAmt));
                apiOrder.setOrderStatus(OrderStatusType.Cancelling);
            }

            if (!DealingModelUtil.isESPOrder(singleLegOrder))
                return apiOrder;

            State singleLegOrderState = singleLegOrder.getState();
            if (singleLegOrderState == null)
                throw new NullPointerException("singleLegOrderState");

            //ExecutionState INACTIVE has higher precedence then STOPPED. An order may be in both state...
            if( singleLegOrder.isOTOInactiveOrder() )
            {
            	apiOrder.setExecutionState(ExecutionState.INACTIVE);
            }
            else if (singleLegOrderState.getName() == State.Name.RSSUSPENDED ||
                    singleLegOrder.isExecutionSuspended())
           {
               apiOrder.setExecutionState(ExecutionState.STOPPED);
           }

            OrderStrategy orderStrategy = singleLegOrder.getOrderStrategy();
            if (orderStrategy != null) {
                apiOrder.setExecutionStrategyName(orderStrategy.getStrategyName());
				if ( orderStrategy.getStrategyType() != null ) {
					apiOrder.setStrategyType(orderStrategy.getStrategyType().name());
					//override to Algo for
					apiOrder.setOrderType(OrderType.Algo);
				}
                long orderExecutionStartTime = orderStrategy.getOrderExecutionStartTime();
                if (orderExecutionStartTime != 0L) {
                    apiOrder.setExecutionStartTime(new SimpleDateFormat(ISCommonConstants.TWAP_TIME_FORMAT_MILLIS).format(orderExecutionStartTime));
                }

                long orderExecutionEndTime = orderStrategy.getOrderExecutionEndTime();
                if (orderExecutionEndTime != 0L) {
                    apiOrder.setExecutionEndTime(new SimpleDateFormat(ISCommonConstants.TWAP_TIME_FORMAT_MILLIS).format(orderExecutionEndTime));
                }

                long pegTime = orderStrategy.getPegTime();
                if (pegTime != 0L) {
                    apiOrder.setPegTime(pegTime);
                }

                OrderStrategy.ActionOnExpirationType actionOnExp = orderStrategy.getActionOnExpiration();
                if (actionOnExp != null) {
                    switch (actionOnExp) {
                        case FILL_AT_MARKET:
                            apiOrder.setActionOnExpiry(ISCommonConstants.TWAP_ACTIONORDEREXPIRY_FillAtMarket);
                            break;
                        case CANCEL:
                            apiOrder.setActionOnExpiry(ISCommonConstants.TWAP_ACTIONORDEREXPIRY_CancelOrder);
                            break;
                        default:
                            log.error("Invalid actionOnExp " + actionOnExp + ", orderId " + orderId);
                            break;
                    }
                }
            }

            if(apiOrder.getTradeClassification() == null) { apiOrder.setTradeClassification(singleLegOrder.getOrderTradeClassification()); }
            if(apiOrder.getCustomParameters() == null){ apiOrder.setCustomParameters(IdcUtilC.serializeMapToJSON(singleLegOrder.getCustomParametersMap())); }
            apiOrder.setCancelledBy(singleLegOrder.getCancelledBy());
            OrderCancellationCode cancellationCode = singleLegOrder.getCancellationCode();
            if (cancellationCode != null) {
                apiOrder.setCancelBySystem(singleLegOrder.isUnsolicitedCancel());
            }

            populateContingencyParams(apiOrder, singleLegOrder);

            //hard-coded format for trade date. IdcDate.YYYY_MM_DD_HYPHEN
            apiOrder.setTradeDate(getFormattedDate(new Date(singleLegOrder.getCreatedBusinessDate()), FXIConstants.API_DATE_FORMAT));
            apiOrder.setOrderNotes(singleLegOrder.getNotes());
                 if (singleLegOrder.getMarketRange() >= 0.0) {
                        double marketRange = Double.valueOf(singleLegOrder.getMarketRange());
                        apiOrder.setRange(String.valueOf(marketRange / fxRateBasis.getPipsFactor()));
                    }

            if(singleLegOrder.getRejectionCode() != null) apiOrder.setReason("RequestValidationError." + singleLegOrder.getRejectionCode().name());
            apiOrder.setOmsOrder(singleLegOrder.isOMSOrder());
            // populating order submission field in header object here.
            // TODO: remove header fields from Order objects
            if (StringUtils.isNotBlank(apiOrder.getOrderSubmissionTime())) {
                apiOrder.getHeader().setSendingTime(dateFormat.parse(apiOrder.getOrderSubmissionTime()).getTime());
            }
        } catch (Exception e) {
            log.error("Failed to generate order, ", e);
        }

        return apiOrder;
    }

    /**
     * Transforms the given CDQ order to Api Order.
     * @param cdqOrder
     * @param user
     * @param queryTradeIds if true the returned Api Order will have the corresponding trade ids
     * @return Api Order
     */
    public Order generateOrder(FXSingleLegOrder cdqOrder, User user, boolean queryTradeIds, boolean enrichTrades) {
        Order apiOrder = new Order();
        String orderId = cdqOrder.getOrderId();
        try {
            FXDealLeg cdqFXDealLeg = cdqOrder.getFXDealLeg();
            if (cdqFXDealLeg == null)
                throw new NullPointerException("cdqFXDealLeg");

            Currency dealtCurrency = cdqFXDealLeg.getDealtCurrency();
            if (dealtCurrency == null)
                throw new NullPointerException("dealtCurrency");

            Currency termCurrency = cdqFXDealLeg.getVariableCurrency();
            if (termCurrency == null)
                throw new NullPointerException("termCurrency");

            FXRateConvention fxRateConvention = cdqFXDealLeg.getFxRateConvention();
            if (fxRateConvention == null)
                throw new NullPointerException("fxRateConvention");

            FXRateBasis fxRateBasis = fxRateConvention.getFXRateBasis(cdqFXDealLeg.getCurrencyPair());
            if (fxRateBasis == null)
                throw new NullPointerException("fxRateBasis");

            DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
            SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
            //binodbinod
            DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());

            boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, termCurrency);
            DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

            apiOrder.setOrderId(orderId);
            copyParentOrderAttributes(user, cdqOrder, cdqFXDealLeg, apiOrder, dealtCurrency, userDecFormat, dealtCcyFormat, rateFormat, dateFormat, queryTradeIds, enrichTrades );

            apiOrder.setOrderStatus(getOrderStatus(cdqOrder));
            apiOrder.getHeader().setMsgType(convertOrderStatusToMsgType(getOrderStatus(cdqOrder)));
            apiOrder.setFillRate(rateFormat.format(cdqOrder.getAverageRate()));
			apiOrder.setReferenceQuoteId(cdqOrder.getQuoteId());

            double filledAmt = cdqOrder.getFilledAmount();
            // TODO Netting
//            if(cdqOrder.isNettingEnabled() && !DealingModelUtil.isOrderInFinalState(cdqOrder) && !cdqOrder.isShowFills()) {
//                if(requestLeg.getNettedAmount() <= filledAmt) {
//                    filledAmt = filledAmt - requestLeg.getNettedAmount();
//                }
//                if(filledAmt == 0) {
//                    //override average rate and order status
//                    apiOrder.setFillRate(rateFormat.format(0));
//                    apiOrder.setOrderStatus(OrderStatusType.Pending);
//                }
//            }
            double unFilledAmt = cdqOrder.getAmount() - filledAmt;

            apiOrder.setTransactionId(cdqOrder.getTransactionId());
            apiOrder.setFilledAmount(dealtCcyFormat.format(filledAmt));
            apiOrder.setUnfilledAmount(dealtCcyFormat.format(unFilledAmt));
            apiOrder.setParentOrderId(cdqOrder.getOrderId());
            apiOrder.setTaker(cdqOrder.isTaker());


            String linkedOrderId = cdqOrder.getLinkedOrderId();
            if( linkedOrderId != null )
            {
                apiOrder.setLinkedISOrderId(linkedOrderId);
                apiOrder.setContingencyType(cdqOrder.getContingentType());
                apiOrder.setLinkedId(cdqOrder.getGroupId());
                // below value is also required but, this is already done in the populateContingencyParameters
                // apiOrder.setcParams(RequestUtils.serializeContingencyParameters(cdqOrder.getContingencyParameters());
            }
            apiOrder.setOrigOrderRate(String.valueOf(cdqFXDealLeg.getOriginalOrderRate()));
            apiOrder.setOrigOrderAmount(String.valueOf(cdqFXDealLeg.getOriginalOrderAmount()));
            if (!isESPOrder(cdqOrder))
                return apiOrder;

            com.integral.workflow.State cdqOrderState = cdqOrder.getState();
            if (cdqOrderState == null)
            {
                log.error("OrderState/ExecutionState of the order is not set. ");
                throw new NullPointerException("cdqOrderState");
            }
            else
            {
                if (!cdqOrderState.isActive())
                {                        // TODO correct?
                    apiOrder.setExecutionState(ExecutionState.STOPPED);
                }
            }

            long orderModifiedTime = cdqOrder.getModifiedTime();
            apiOrder.setOrderTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS zzz").format(orderModifiedTime));
            long orderExecStartTime = cdqOrder.getOrderExecutionStartTime()  != null  ? cdqOrder.getOrderExecutionStartTime().getTime() : 0;
            long orderExecEndTime =  cdqOrder.getOrderExecutionEndTime()  != null  ? cdqOrder.getOrderExecutionEndTime().getTime() : 0;

            apiOrder.setExecutionStartTime(new SimpleDateFormat(ISCommonConstants.TWAP_TIME_FORMAT).format(orderExecStartTime));
            if(orderExecEndTime != 0) apiOrder.setExecutionEndTime(new SimpleDateFormat(ISCommonConstants.TWAP_TIME_FORMAT).format(orderExecEndTime));
            OrderStrategy orderStrategy = null;                       // TODO not supported
            if (orderStrategy != null) {
                apiOrder.setExecutionStrategyName(orderStrategy.getStrategyName());

                long orderExecutionStartTime = orderStrategy.getOrderExecutionStartTime();
                if (orderExecutionStartTime != 0L) {
                    apiOrder.setExecutionStartTime(new SimpleDateFormat(ISCommonConstants.TWAP_TIME_FORMAT_MILLIS).format(orderExecutionStartTime));
                }

                long orderExecutionEndTime = orderStrategy.getOrderExecutionEndTime();
                if (orderExecutionEndTime != 0L) {
                    apiOrder.setExecutionEndTime(new SimpleDateFormat(ISCommonConstants.TWAP_TIME_FORMAT_MILLIS).format(orderExecutionEndTime));
                }

                long pegTime = orderStrategy.getPegTime();
                if (pegTime != 0L) {
                    apiOrder.setPegTime(pegTime);
                }

                OrderStrategy.ActionOnExpirationType actionOnExp = orderStrategy.getActionOnExpiration();
                if (actionOnExp != null) {
                    switch (actionOnExp) {
                        case FILL_AT_MARKET:
                            apiOrder.setActionOnExpiry(ISCommonConstants.TWAP_ACTIONORDEREXPIRY_FillAtMarket);
                            break;
                        case CANCEL:
                            apiOrder.setActionOnExpiry(ISCommonConstants.TWAP_ACTIONORDEREXPIRY_CancelOrder);
                            break;
                        default:
                            log.error("Invalid actionOnExp " + actionOnExp + ", orderId " + orderId);
                            break;
                    }
                }
            }

            Timestamp twapst = cdqOrder.getOrderExecutionStartTime();
            if( twapst != null )
            {
                if (twapst.getTime() > System.currentTimeMillis())
                {
                    apiOrder.setExecutionState(ExecutionState.STOPPED);
                }
            }
            apiOrder.setTradeClassification(cdqOrder.getTradeClassification()!=null ? cdqOrder.getTradeClassification().getShortName() : ISConstantsC.TRD_CLSF_SP);
            apiOrder.setCustomParameters(cdqOrder.getCustomParameters());
            apiOrder.setCancelledBy(cdqOrder.getCancelledBy());
            String unsolicitedCancelBy = cdqOrder.getUnsolicitedCancelBy();
            if (unsolicitedCancelBy != null) {
                apiOrder.setCancelBySystem(unsolicitedCancelBy.equals(ISCommonConstants.SYSTEM_USER));
            }

            populateContingencyParams(apiOrder, cdqOrder);

            //hard-coded format for trade date. IdcDate.YYYY_MM_DD_HYPHEN
            //apiOrder.setTradeDate(cdqOrder.getCreatedDate(), "yyyy-MM-dd");
            long tradeDateMilli  = cdqOrder.getTradeDate().asJdkDate().getTime();
            apiOrder.setTradeDate(new SimpleDateFormat(FXIConstants.API_DATE_FORMAT).format(tradeDateMilli));
           apiOrder.setOrderMetaData(cdqOrder.getOrderMetaData());
            apiOrder.setOrderNotes(cdqOrder.getNotes());

            if(cdqOrder.getMarketRange() != null && !cdqOrder.getMarketRange().equals("")) {
                String mktRng = cdqOrder.getMarketRange().trim();
                try {
                    double marketRange = Double.valueOf(mktRng);
                    if (marketRange >= 0.0) {
                        apiOrder.setRange(String.valueOf(marketRange / fxRateBasis.getPipsFactor()));
                 }
                } catch (NumberFormatException e) {
                    log.error("Error while formatting market range" + mktRng);
                }
            }

            String rejectionReason = null;
			OrderRejectionCode rejectionCode = cdqOrder.getOrderRejectionCode();
			if(rejectionCode != null) rejectionReason = rejectionCode.name();
			if(rejectionReason == null) rejectionReason = cdqOrder.getOrderRejectionReason();
			if(rejectionReason != null) {
				if(rejectionReason.startsWith("Request.Validation.")) rejectionReason = rejectionReason.substring("Request.Validation.".length());
				apiOrder.setReason("RequestValidationError." + rejectionReason);
			}
            if (StringUtils.isNotBlank(apiOrder.getOrderSubmissionTime())) {
                apiOrder.getHeader().setSendingTime(dateFormat.parse(apiOrder.getOrderSubmissionTime()).getTime());
            }
            apiOrder.setOmsOrder(cdqOrder.isOmsOrder());
        } catch (Exception e) {
            log.error("Failed to generate order, ", e);
            return null;
        }

        apiOrder.setcParams(RequestUtils.serializeContingencyParameters(cdqOrder.getContingencyParameters()));
        return apiOrder;
    }

    private void _populateContingencyParams(Order apiOrder, SingleLegOrder singleLegOrder) {
    	OrderContingency orderContingency = singleLegOrder.getOrderContingency();
    	if(orderContingency == null){
    		return;
    	}

    	List<ContingencyParameter> cParams= new ArrayList<ContingencyParameter>();
    	ContingencyParameter contParam = new ContingencyParameter();
    	int type = 0;

    	switch(orderContingency.getType()){
    	case OCO:
    		type = 1;
    		break;
    	case OTO:
    		type = 2;
    		break;
    	case OUO:
    		type = 3;
    		// type 4 OUO_PROPORTIONAL is not supported in OrderContingency
    		break;
    	case AUTOCOVER:
    		type = 5;
    		break;
        case COPY:
            type = 6;
            break;
    	}
    	contParam.setType(type);
    	contParam.addLinkedOrderIds(orderContingency.getLinkId());

    	cParams.add(contParam);
    	apiOrder.setContingencyParameters(cParams);
    }

    private void populateContingencyParams(Order apiOrder, SingleLegOrder singleLegOrder)
    {
    	if( singleLegOrder.getOrderContingencies() == null || singleLegOrder.getOrderContingencies().isEmpty() )
    	{
    		_populateContingencyParams(apiOrder, singleLegOrder);
    		return;
    	}

    	List<ContingencyParameter> cParams= new ArrayList<ContingencyParameter>();
    	for( com.integral.finance.dealing.ContingencyParameter cp : singleLegOrder.getOrderContingencies() )
    	{
	    	ContingencyParameter contParam = new ContingencyParameter();
	    	contParam.setType( cp.getType() );
	    	if( cp.getLinkedOrderIds() != null && !cp.getLinkedOrderIds().isEmpty() )
	    	{
	    		contParam.setLinkedOrderIds( cp.getLinkedOrderIds() );
	    	}
	    	else //hack for .NET client to work
	    	{
	    		contParam.setLinkedOrderIds( new ArrayList<String>(1) );
	    	}
	    	contParam.setGroupId( cp.getGroupId() );
	    	cParams.add(contParam);
    	}
    	apiOrder.setContingencyParameters(cParams);
    }

    private void populateContingencyParams(Order apiOrder, FXSingleLegOrder cdqOrder)
    {
    	if( cdqOrder.getContingencyParameters() == null || cdqOrder.getContingencyParameters().isEmpty() )
    	{
    		return;
    	}

    	List<ContingencyParameter> cParams= new ArrayList<ContingencyParameter>();
    	for( com.integral.finance.dealing.ContingencyParameter cp : cdqOrder.getContingencyParameters() )
    	{
	    	ContingencyParameter contParam = new ContingencyParameter();
	    	contParam.setType( cp.getType() );
	    	if( cp.getLinkedOrderIds() != null && !cp.getLinkedOrderIds().isEmpty() )
	    	{
	    		contParam.setLinkedOrderIds( cp.getLinkedOrderIds() );
	    	}
	    	else //hack for .NET client to work
	    	{
	    		contParam.setLinkedOrderIds( new ArrayList<String>(1) );
	    	}
	    	contParam.setGroupId( cp.getGroupId() );
	    	cParams.add(contParam);
    	}
    	apiOrder.setContingencyParameters(cParams);
    }

    private void setRequestEventTimes(SingleLegOrder singleLegOrder, Order apiOrder, SimpleDateFormat dateFormat) {
        OrderStrategy orderStrategy               = singleLegOrder.getOrderStrategy();
        OrderRequestEventTimes orderReqEventTimes = singleLegOrder.getOrderRequestEventTimes();
        if ( orderReqEventTimes != null ) {
            long orderSubmissionTime = orderReqEventTimes.getSubmissionTime();
            long expiryTime          = orderReqEventTimes.getExpirationTime();

            if ( expiryTime == 0 ) {
                expiryTime = singleLegOrder.getExpireTime() - orderSubmissionTime;
            }

            //
            // For strategy order with delayed execution, the expiration time is set as expiryTime + executionStartTime
            // Hence while returning back to client, removing the executionStartTime from totalExpirationTime to
            // provide only expiryTime, as submitted by the client.
            //
            int execFlags      = singleLegOrder.getExecutionFlags();
            long execStartTime = ( orderStrategy != null ) ? orderStrategy.getOrderExecutionStartTime() : 0;

            if ( ( ( execFlags & ExecutionFlags.STRATEGY ) == ExecutionFlags.STRATEGY ) && execStartTime > 0 ) {
                expiryTime = singleLegOrder.getExpireTime() - execStartTime;
            }

            if ( expiryTime > 0 ) {
                apiOrder.setExpiryTime( expiryTime / 1000 );
            }
            else if (singleLegOrder.getTimeInForce() == TimeInForce.DAY && singleLegOrder.getOrganization() != null) {
                // populate expiry time for day order as requested as it is required for HTML client team. We don't
                // use this expiry time internally to expire day orders.
                BusinessCenter businessCenter = singleLegOrder.getOrganization().getBusinessCenter(EndOfDayServerC.ROLL_TIME_BUSINESS_CENTER_NAME);
                if (businessCenter == null) {
                    final EndOfDayService dayService = EndOfDayServiceFactory.getEndOfDayService();
                    businessCenter = dayService.getRollTimeBusinessCenter();
                }

                if (businessCenter != null) {
                    Time rollTime = businessCenter.getRollTime();
                    TimeZone tz = businessCenter.getTimeZone();
                    IdcDateTime currentRollTime = DateTimeFactory.newDateTime(DateTimeFactory.newDate(tz), rollTime, tz);
                    expiryTime = currentRollTime.asJdkDate().getTime();
                    if (expiryTime <= orderSubmissionTime) {
                        currentRollTime = DateTimeFactory.newDateTime(DateTimeFactory.newDate(tz).addDays(1), rollTime, tz);
                        expiryTime = currentRollTime.asJdkDate().getTime();
                    }

                    if (expiryTime > 0) {
                        expiryTime -= orderSubmissionTime;
                        apiOrder.setExpiryTime(expiryTime / 1000);
                    }
                }
            }

            apiOrder.setOrderSubmissionTime(dateFormat.format(orderSubmissionTime));
            apiOrder.setOrderSubmissionTimeInMillis(orderSubmissionTime);
        }

    }

    private void copyParentOrderAttributes( User                    user,
                                            SingleLegOrder          singleLegOrder,
                                            OrderRequest.RequestLeg requestLeg,
                                            Order                   apiOrder,
                                            Currency                dealtCurrency,
                                            FXRateBasis             fxRateBasis,
                                            DecimalFormat           userDecFormat,
                                            DecimalFormat           dealtCcyFormat,
                                            DecimalFormat           rateFormat,
                                            SimpleDateFormat        dateFormat,
                                            boolean                 queryTradeIds,
                                            boolean                 enrichTrades ) throws NullPointerException
    {

        CurrencyPair currencyPair = singleLegOrder.getCurrencyPair();

        if (currencyPair == null)
            throw new NullPointerException("currencyPair");

        Currency baseCurrency = currencyPair.getBaseCurrency();
        if (baseCurrency == null)
            throw new NullPointerException("baseCurrency");

        Currency settledCurrency = (baseCurrency.getShortName().equals(dealtCurrency.getShortName())) ? currencyPair.getVariableCurrency() : currencyPair.getBaseCurrency();
        if (settledCurrency == null)
            throw new NullPointerException("settledCurrency");

        OrderRequest.RequestLeg.BuySellMode buySellMode = requestLeg.getBuySellMode();
        if (buySellMode == null)
            throw new NullPointerException("buySellMode");

        setRequestEventTimes(singleLegOrder, apiOrder, dateFormat);

        if (singleLegOrder.getUser() != null) {
            apiOrder.setCustomerId(singleLegOrder.getUser().getShortName());
        }
        else {
            apiOrder.setCustomerId(user.getShortName());
        }

        apiOrder.setCustomerOrder(singleLegOrder.getClientReferenceId());
        apiOrder.setAmount(dealtCcyFormat.format(requestLeg.getAmount()));

        //get original ccy pair , tenor , settlment type
        FXIApiUtil.getInstance().updateInstrumentDetailsWithOriginal( currencyPair.getBaseCurrency() , currencyPair.getVariableCurrency(),
                apiOrder, dealtCurrency , settledCurrency, fxRateBasis);

        boolean isBid = (buySellMode == OrderRequest.RequestLeg.BuySellMode.BUY);
        boolean isBaseCcyOrder = singleLegOrder.getDealtCurrency().isSameAs(singleLegOrder.getBaseCurrency());
        apiOrder.setOrderSide((isBid && isBaseCcyOrder) || (!isBid && !isBaseCcyOrder) ? OrderSideType.Buy : OrderSideType.Sell);

        apiOrder.setSpotRate(rateFormat.format(singleLegOrder.getOrderSpotRate()));
        apiOrder.setTimeInForce(getTimeInForce(singleLegOrder));
        apiOrder.setExecFlags(getExecutionStrategies(singleLegOrder));

        long orderModifiedTime = singleLegOrder.getModifiedTime();
        apiOrder.setOrderTime(dateFormat.format(orderModifiedTime));     //Biswa ??
        apiOrder.setOrderTimeInMillis(orderModifiedTime);

        apiOrder.setCustomerOrg(singleLegOrder.getOrgShortName());
        apiOrder.setCustomerAccount(singleLegOrder.getLegalEntityName());
        String longName = singleLegOrder.getOrganization() != null ? singleLegOrder.getOrganization().getLongName() : null;
        apiOrder.setExternalRequestID(singleLegOrder.getClientReferenceId());
        apiOrder.setTradeChannel(singleLegOrder.getChannel());

        if (requestLeg.getValueDate() > 0) {
            apiOrder.setValueDate(DateUtils.getDateStrFromTimeInMills(requestLeg.getValueDate()));
        }

        if(apiOrder.getTenor() == null)
        {
            apiOrder.setTenor( requestLeg.getTenor() );
        }
//        apiOrder.setStopPrice(rateFormat.format(singleLegOrder.getOrderTrigger().getTriggerRate()));

        // if maxShow > 0, order is DISPLAY otherwise HIDDEN
        OrderType orderType = getOrderType(singleLegOrder);
        apiOrder.setOrderType(orderType);
        if(orderType == OrderType.Market && ( singleLegOrder.getOrderStrategy().getPegType() != Character.MIN_VALUE) ) {
            apiOrder.setLimitRate(null);
        } else {
            apiOrder.setLimitRate(rateFormat.format(singleLegOrder.getOrderSpotRate()));
            apiOrder.setOrderRate(singleLegOrder.getOrderSpotRate());
        }
        apiOrder.setMaxShow( dealtCcyFormat.format( singleLegOrder.getMaxShowAmount() ) );
        apiOrder.setMinQuantity(dealtCcyFormat.format(requestLeg.getMinFillAmount()));
        apiOrder.setTriggerReached(singleLegOrder.isStopLossTriggered());
        OrderTrigger orderTrigger = singleLegOrder.getOrderTrigger();       // currently orders are triggered only for stop loss
        if (orderTrigger != null) {
            double triggerRate = orderTrigger.getTriggerRate();
            if (triggerRate > 0) {
                apiOrder.setStopPrice(rateFormat.format(triggerRate));
            }
        }

        // Add trailing distance handling for regular SingleLegOrder
        // Note: This assumes getTrailingDistance() method exists in SingleLegOrder
        // If it doesn't exist, this will need to be implemented differently
        try {
            Double trailingDistance = singleLegOrder.getOrderTrigger().getTrailingDistance();
            if (trailingDistance != null && trailingDistance > 0) {
                apiOrder.setTrailingDistance(rateFormat.format(trailingDistance));
            }
        } catch (Exception e) {
            // Method might not exist in SingleLegOrder, log and continue
            log.debug("getTrailingDistance method not available in SingleLegOrder: " + e.getMessage());
        }

        double marketRange = singleLegOrder.getMarketRange();
        if (marketRange >= 0.0) {
            apiOrder.setRange(userDecFormat.format(marketRange * fxRateBasis.getPipsFactor()));
        }

        List<String> tradeIds = new ArrayList<String>();
        if (queryTradeIds) {
            List<Trade> trades = SingleLegTradeService.getInstance().getDoneTradesForSelectedOrderId(user, apiOrder.getOrderId(), null );
            if (trades != null && !trades.isEmpty()) {
                if ( enrichTrades ) {
                    apiOrder.setTrades( trades );
                }
                for (Trade trade : trades) {
                    if(trade == null) continue;
                    tradeIds.add(trade.getTradeId());
                }
            }
        }

        apiOrder.setTradeIds(tradeIds);        // list of tradeIds that have been done against this order at the time this order was queried
        apiOrder.setTradeDate(getFormattedDate(new Date(singleLegOrder.getCreatedBusinessDate()), FXIConstants.API_DATE_FORMAT));
        // Outright Order Fields
       	apiOrder.setSpotRate(requestLeg.getSpotRate() +"");
       	apiOrder.setForwardPoints(requestLeg.getForwardPoint() +"");

       	String tenor = requestLeg.getTenor();
        if(tenor != null && apiOrder.getTenor() == null)
        {
        	apiOrder.setTenor(tenor);
        }

        String fixingTenor = requestLeg.getFixingTenor();
        if(fixingTenor != null && apiOrder.getFixingTenor() == null)
        {
        	apiOrder.setFixingTenor(fixingTenor);
        }
        long valueDate = requestLeg.getValueDate();
        if (valueDate > 0) {
        	IdcDate idcDate = DateTimeFactory.newDate(new Date(valueDate));
        	apiOrder.setValueDate(idcDate.getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN));
        }

        long fixingDate = requestLeg.getFixingDate();
        if (fixingDate > 0) {
           	IdcDate idcDate = DateTimeFactory.newDate(new Date(fixingDate));
           	apiOrder.setFixingDate(idcDate.getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN));
        }

        if ( singleLegOrder.isOTOInactiveOrder() )
        {
        	apiOrder.setExecutionState(ExecutionState.INACTIVE);
        }

        apiOrder.setOrderMetaData( singleLegOrder.getOrderMetaData() );
        apiOrder.setExecutionStrategyName( singleLegOrder.getOrderStrategy().getStrategyName());
        apiOrder.setCustomParameters(IdcUtilC.serializeMapToJSON(singleLegOrder.getCustomParametersMap()));
    }

    private void copyParentOrderAttributes( User             user,
                                            FXSingleLegOrder cdqOrder,
                                            FXDealLeg        cdqFXDealLeg,
                                            Order            apiOrder,
                                            Currency         dealtCurrency,
                                            DecimalFormat    userDecFormat,
                                            DecimalFormat    dealtCcyFormat,
                                            DecimalFormat    rateFormat,
                                            SimpleDateFormat dateFormat,
                                            boolean          queryTradeIds,
                                            boolean          enrichTrades ) throws NullPointerException
    {

        Currency settledCurrency = cdqFXDealLeg.getSettledCurrency();
        if (settledCurrency == null)
            throw new NullPointerException("settledCurrency");

        long orderSubmissionTime = cdqOrder.getSubmissionTimestamp().getTime();
        apiOrder.setOrderSubmissionTime(dateFormat.format(orderSubmissionTime));
        apiOrder.setOrderSubmissionTimeInMillis(orderSubmissionTime);

        long expiryTime = 0;
        if( cdqOrder.getExpiryTimestamp() != null ) {
            long refTime = orderSubmissionTime;

            //
            // For strategy order with delayed execution, the expiration time is set as expiryTime + executionStartTime
            // Hence while returning back to client, removing the executionStartTime from totalExpirationTime to
            // provide only expiryTime, as submitted by the client.
            //
            int  execFlags     = cdqOrder.getExecutionFlags();
            long execStartTime = ( cdqOrder.getOrderExecutionStartTime() != null ) ?
                                    cdqOrder.getOrderExecutionStartTime().getTime() : 0;
            if ( ( ( execFlags & ExecutionFlags.STRATEGY ) == ExecutionFlags.STRATEGY ) && execStartTime > 0 ) {
                refTime = execStartTime;
            }

            expiryTime = cdqOrder.getExpiryTimestamp().getTime() - refTime;

            if (expiryTime > 0) {
                apiOrder.setExpiryTime(expiryTime / 1000);
            }
        }

        if (cdqOrder.getUser() != null) {
            apiOrder.setCustomerId(cdqOrder.getUser().getShortName());
        }
        else {
            apiOrder.setCustomerId(user.getShortName());
        }

        apiOrder.setCustomerOrder(cdqOrder.getClientReferenceId());   // TODO-CDQ =?= singleLegOrder.getClientReferenceId(), also fix OrderQueryService._queryForOrderByClientReferenceId

        apiOrder.setAmount(dealtCcyFormat.format(cdqOrder.getAmount()));

        //get original ccy pair , tenor , settlment type
        FXRateConvention fxRateConvention = cdqFXDealLeg.getFxRateConvention();
        FXRateBasis fxRateBasis = null;
        if(fxRateConvention != null && cdqFXDealLeg.getCurrencyPair() != null){
            fxRateBasis = fxRateConvention.getFXRateBasis(cdqFXDealLeg.getCurrencyPair());
        }
        FXIApiUtil.getInstance().updateInstrumentDetailsWithOriginal( cdqFXDealLeg.getBaseCurrency() , cdqFXDealLeg.getVariableCurrency(),
                apiOrder, dealtCurrency , settledCurrency, fxRateBasis);

        boolean isBid = cdqFXDealLeg.getAcceptedBidOfferMode() == DealingPrice.BID ? true : false;
        boolean isBaseCcyOrder = cdqFXDealLeg.isDealtCurrency1();
        apiOrder.setOrderSide((isBid && isBaseCcyOrder) || (!isBid && !isBaseCcyOrder) ? OrderSideType.Buy : OrderSideType.Sell);

        apiOrder.setLimitRate(rateFormat.format(cdqFXDealLeg.getRate()));
        apiOrder.setOrderRate(cdqFXDealLeg.getRate());
        apiOrder.setTimeInForce(getTimeInForce(cdqOrder));
        apiOrder.setExecFlags(getExecutionStrategies(cdqOrder));

        long orderModifiedTime = cdqOrder.getModifiedTime();
        apiOrder.setOrderTime(dateFormat.format(orderModifiedTime));
        apiOrder.setOrderTimeInMillis(orderModifiedTime);

        apiOrder.setCustomerOrg(cdqOrder.getCustomerOrg().getShortName()); //AP-8243 Customer Org should be FI Org
        if(cdqOrder.getPlacedByOrg() != null) apiOrder.setPlacedByOrg(cdqOrder.getPlacedByOrg().getShortName());
        apiOrder.setCustomerAccount(cdqOrder.getlegalEntityCptyA().getShortName());
        apiOrder.setExternalRequestID(cdqOrder.getClientReferenceId());   // TODO-CDQ =?= singleLegOrder.getClientReferenceId(), also fix OrderQueryService._queryForOrderByClientReferenceId
        apiOrder.setTradeChannel(cdqOrder.getDealingChannel());

        // if maxShow > 0, order is DISPLAY otherwise HIDDEN
        apiOrder.setMaxShow(dealtCcyFormat.format(cdqOrder.getDisplayLimit()));
        OrderType orderType = getOrderType(cdqOrder);
        if(cdqOrder.isAlgoOrderAtBest()){
            apiOrder.setOrderType(OrderType.AtBest);
        }else {
            apiOrder.setOrderType(orderType);
        }
        apiOrder.setMinQuantity(dealtCcyFormat.format( cdqOrder.getMinFillAmount()));                       // TODO not found equivalent to requestLeg.getMinFillAmount()

        Timestamp triggerReachedAt = cdqOrder.getTriggerReachedAt();
        if (triggerReachedAt != null) {
            apiOrder.setTriggerReached(triggerReachedAt.getTime() > 0L);
        }
        Double stopPrice = cdqOrder.getStopPrice();
        if(stopPrice != null) {
            apiOrder.setStopPrice(rateFormat.format(stopPrice.doubleValue()));
        }
        Double trailingDistance = cdqOrder.getTrailingDistanceInPips();
        if(trailingDistance != null) {
            apiOrder.setTrailingDistance(rateFormat.format(trailingDistance.doubleValue()));
        }
        String mktRng = cdqOrder.getMarketRange();
        if(mktRng != null && !mktRng.equals("")){
            try {
                double marketRange = Double.valueOf(mktRng);
                if (marketRange >= 0.0) {
                    apiOrder.setRange(userDecFormat.format(marketRange));
                }
            }
            catch (NumberFormatException e){
                log.error("Error while formatting market range" + mktRng);
            }
        }
        apiOrder.setExecutionStrategyName(cdqOrder.getOrderExecutionStrategyName());

        List<String> tradeIds = new ArrayList<String>();
        if (queryTradeIds) {
            List<Trade> trades = SingleLegTradeService.getInstance().getDoneTradesForSelectedOrderId(user, apiOrder.getOrderId(), null );
            if (trades != null && !trades.isEmpty()) {
                if ( enrichTrades ) {
                    apiOrder.setTrades( trades );
                }
                for (Trade trade : trades) {
                    if(trade == null) continue;
                    tradeIds.add( trade.getTradeId() );
                }
            }
        }

        apiOrder.setTradeIds(tradeIds);        // list of tradeIds that have been done against this order at the time this order was queried
        apiOrder.setTwapSliceRegularSize(cdqOrder.getTwapSliceRegularSize());
        apiOrder.setTwapSliceTopOfBookPercent(cdqOrder.getTwapSliceTopOfBookPercent());
        apiOrder.setTwapMinimumSliceInterval(cdqOrder.getTwapMinimumSliceInterval());
        apiOrder.setTwapFOKSlice(cdqOrder.isTwapFOKSlice());
        apiOrder.setTwapMinSliceSize(cdqOrder.getTwapMinSliceSize());
        apiOrder.setTwapSliceTopOfBookRange(cdqOrder.getTwapSliceTopOfBookRange());
        apiOrder.setTwapSliceSize(cdqOrder.getTwapSliceSize());
        apiOrder.setTwapSliceInterval(cdqOrder.getTwapSliceInterval());
        apiOrder.setTwapSliceSizeRandomizationFactor(cdqOrder.getTwapSliceSizeRandomizationFactor());
        apiOrder.setTwapSliceIntervalRandomizationFactor(cdqOrder.getTwapSliceIntervalRandomizationFactor());
        apiOrder.setActionOnExpiry(cdqOrder.getActionOnOrderExpitation());
        apiOrder.setCustomParameters(cdqOrder.getCustomParameters());
        try{
            if(cdqOrder.getAlgoDescriptor() != null &&
                    cdqOrder.getAlgoDescriptor().getAlgoParameters() != null){
                AlgoDescriptor algoDescriptor = cdqOrder.getAlgoDescriptor();
                AlgoParameters algoParameters = algoDescriptor.getAlgoParameters();
                if (algoParameters instanceof AlgoParametersAtBest) {
                    AlgoParametersAtBest algoParametersAtBest = (AlgoParametersAtBest)algoDescriptor.getAlgoParameters();
                    apiOrder.setTenor(algoParametersAtBest.getTenor());
                    apiOrder.setValueDate(algoParametersAtBest.getValueDate());
                } else if (algoParameters instanceof AlgoParametersExternalAlgo){
                    String algoName = algoDescriptor.getName();
                    AlgoDescriptor.Type algoType = algoDescriptor.getType();
                    apiOrder.setExecutionStrategyName(algoName);
                    if (AlgoDescriptor.Type.ExternalAlgo == algoType) {
                        apiOrder.setOrderType(OrderType.Algo);
                    }
                    String executionStrategyDestination = ((AlgoParametersExternalAlgo) algoParameters).getExDestCode();
                    apiOrder.setExecutionStrategyDestination(executionStrategyDestination);
                    apiOrder.setExecutionDestination(executionStrategyDestination);
                    String params = ((AlgoParametersExternalAlgo) algoParameters).getParameters();
                    apiOrder.setExternalAlgoParameters(params);
                }
            }
        }catch (Exception e){
            log.error("Exception during parsing algo parameters", e);
        }

        RoutingInstruction routingInstruction = cdqOrder.getRoutingInstruction();
        if( routingInstruction != null ){
            if( routingInstruction.getExternalDestinationCode() != null )
            {
                apiOrder.setExecutionDestination(routingInstruction.getExternalDestinationCode());
                apiOrder.setExecutionStrategyDestination(routingInstruction.getExternalDestinationCode());
            }
            else if (routingInstruction.getTradingVenue() != null)
            { //For some unknown reason, the external destination code is not set in the routing instruction
                apiOrder.setExecutionDestination(routingInstruction.getTradingVenue());
                apiOrder.setExecutionStrategyDestination(routingInstruction.getTradingVenue());
            }
        }
    }

// //   @Override
//    public Order getOrderRejectedMessage(WorkflowMessage wfMsg, User user, String eventName) {
//        Object obj = wfMsg.getObject();
//        Order apiOrderResponse = generateOrder(obj, user);
//        OrderMessage message = new OrderMessage();
//        apiOrderResponse.setOrderMessage(message);
//        message.setEventName(eventName);
//
//        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
//
//        if (obj instanceof SingleLegOrder) {
//            SingleLegOrder singleLegOrder = (SingleLegOrder) obj;
//            message.setOrderId(singleLegOrder.get_id());
//            message.setTransactionId(singleLegOrder.getTransactionId());
//            message.setEventTime(dateTimeFormat.format(singleLegOrder.getModifiedTime()));
//            apiOrderResponse.setOrderStatus(getOrderStatus(singleLegOrder));
//        }
//        else if (obj instanceof com.integral.finance.dealing.Order) {
//            com.integral.finance.dealing.Order cdqOrder = (com.integral.finance.dealing.Order) obj;
//            message.setOrderId(cdqOrder.get_id());
//            message.setTransactionId(cdqOrder.getTransactionId());
//            message.setEventTime(dateTimeFormat.format(cdqOrder.getModifiedTime()));
//        }
//
//        message.setEventDetails(SingleLegTradeService.getInstance().getOrderRejectionMessage(apiOrderResponse, eventName));
//        return apiOrderResponse;
//    }

   // @Override
    public Order getAcceptanceMessage(WorkflowMessage wfMsg, User user) {
        Object msgObject = wfMsg.getObject();
        Order apiOrder = new Order();
        try {
            if (!(msgObject instanceof SingleLegTrade || (wfMsg.getTopic().equals(ISCommonConstants.MSG_TOPIC_MATCH) && wfMsg.getEventName().equals(MessageEvent.VENUE_ORDER_SUBMIT.getName()) && msgObject instanceof SingleLegOrder)))
                throw new IllegalArgumentException("msgObject not SingleLegTrade or SingleLegOrder");

            SingleLegTrade singleLegTrade = null;
            SingleLegOrder singleLegOrder = null;
			if (msgObject instanceof SingleLegTrade) {
				singleLegTrade = (SingleLegTrade) msgObject;
				singleLegOrder = singleLegTrade.getOrderRequest();
			} else {
				singleLegOrder = (SingleLegOrder) msgObject;
			}
            if (singleLegOrder == null)
                throw new NullPointerException("singleLegOrder");

            Currency dealtCurrency = singleLegOrder.getDealtCurrency();
            if (dealtCurrency == null)
                throw new NullPointerException("dealtCurrency");

            CurrencyPair currencyPair = singleLegOrder.getCurrencyPair();
            if (currencyPair == null)
                throw new NullPointerException("currencyPair");

            Currency baseCurrency = currencyPair.getBaseCurrency();
            if (baseCurrency == null)
                throw new NullPointerException("baseCurrency");

            Currency settledCcy = (baseCurrency.getShortName().equals(dealtCurrency.getShortName())) ? currencyPair.getVariableCurrency() : currencyPair.getBaseCurrency();

            Currency variableCurrency = currencyPair.getVariableCurrency();
            if (variableCurrency == null)
                throw new NullPointerException("variableCurrency");

            FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();
            if (fxRateBasis == null)
                throw new NullPointerException("fxRateBasis");

            SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateFormat(user);
            SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
            DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
            DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());
            DecimalFormat settledCcyFormat = settledCcy.getDecimalFormat(userDecFormat.clone());

            boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, variableCurrency);
            DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

            apiOrder.setOrderId(singleLegOrder.get_id());
            OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
            if (requestLeg == null)
                throw new NullPointerException("requestLeg");

            copyParentOrderAttributes(user, singleLegOrder, requestLeg, apiOrder, dealtCurrency, fxRateBasis, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false, false);

            SingleLegTrade.SingleLegTradeAttributes tradeAttributes = null;
			if (singleLegTrade != null) {
				tradeAttributes = singleLegTrade.getAttributes();
			}
            double avgFilledPrice = 0.0D;
            double unfilledAmount = 0.0D;
            double totalFilledAmt = 0.0D;
            if( tradeAttributes != null ){
                avgFilledPrice = tradeAttributes.getOrderAverageFillRate();
                unfilledAmount = tradeAttributes.getOrderUnfilledAmount();
                totalFilledAmt = tradeAttributes.getTotalFilledAmount();
            }
            apiOrder.setTransactionId(singleLegOrder.getTransactionId());
            apiOrder.setParentOrderId(singleLegOrder.get_id());
            apiOrder.setFillRate(rateFormat.format(avgFilledPrice));
            apiOrder.setFilledAmount(dealtCcyFormat.format(totalFilledAmt));
            apiOrder.setUnfilledAmount(dealtCcyFormat.format(unfilledAmount));
            apiOrder.setOrderStatus(getOrderStatus(singleLegOrder));
			if (singleLegTrade != null) {
				Trade trade = SingleLegTradeTranslator.getInstance().generateTrade(singleLegTrade, user);
				trade.setIsnet(wfMsg.getParameterValue(ISCommonConstants.DEAL_NET) != null);
				apiOrder.setAcceptedTrade(trade);
			}
			if (singleLegTrade != null) {
				OrderMessage message = new OrderMessage();
				apiOrder.setOrderMessage(message);
				message.setOrderId(singleLegOrder.get_id());
				message.setEventName(TRADE_VERIFIED_EVENT);
				message.setEventDetails(getTradeMessageDetails(singleLegTrade, singleLegOrder, dealtCurrency,
						dealtCcyFormat, settledCcy, settledCcyFormat, dateFormat, true));
				message.setEventTime(dateTimeFormat.format(singleLegTrade.getExecutionTime()));
				message.setTransactionId(singleLegTrade.get_id());
			}
        } catch (Exception e) {
            log.error("Failed in acceptance message, ", e);
        }

        return apiOrder;
    }

    //@Override
    public Order getPreRateAcceptanceMessage( WorkflowMessage wfMsg, User user )
    {
        Object msgObject = wfMsg.getObject();
        Order apiOrder = new Order();
        try {
            if (!(msgObject instanceof SingleLegTrade))
                throw new IllegalArgumentException("msgObject not SingleLegTrade");

            SingleLegTrade singleLegTrade = (SingleLegTrade)msgObject;
            SingleLegOrder singleLegOrder = singleLegTrade.getOrderRequest();
            if (singleLegOrder == null)
                throw new NullPointerException("singleLegOrder");

            Currency dealtCurrency = singleLegOrder.getDealtCurrency();
            if (dealtCurrency == null)
                throw new NullPointerException("dealtCurrency");

            CurrencyPair currencyPair = singleLegOrder.getCurrencyPair();
            if (currencyPair == null)
                throw new NullPointerException("currencyPair");

            Currency baseCurrency = currencyPair.getBaseCurrency();
            if (baseCurrency == null)
                throw new NullPointerException("baseCurrency");

            Currency settledCcy = (baseCurrency.getShortName().equals(dealtCurrency.getShortName())) ? currencyPair.getVariableCurrency() : currencyPair.getBaseCurrency();

            Currency variableCurrency = currencyPair.getVariableCurrency();
            if (variableCurrency == null)
                throw new NullPointerException("variableCurrency");

            FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();
            if (fxRateBasis == null)
                throw new NullPointerException("fxRateBasis");

            SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateFormat(user);
            SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
            DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
            DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());
            DecimalFormat settledCcyFormat = settledCcy.getDecimalFormat(userDecFormat.clone());

            boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, variableCurrency);
            DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

            apiOrder.setOrderId(singleLegOrder.get_id());
            OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
            if (requestLeg == null)
                throw new NullPointerException("requestLeg");

            copyParentOrderAttributes(user, singleLegOrder, requestLeg, apiOrder, dealtCurrency, fxRateBasis, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false, false);

            SingleLegTrade.SingleLegTradeAttributes tradeAttributes = singleLegTrade.getAttributes();
            double unfilledAmount = 0.0D;
            double totalFilledAmt = 0.0D;
            if( tradeAttributes != null ){
                unfilledAmount = tradeAttributes.getOrderUnfilledAmount();
                totalFilledAmt = tradeAttributes.getTotalFilledAmount();
            }
            apiOrder.setTransactionId(singleLegOrder.getTransactionId());
            apiOrder.setParentOrderId(singleLegOrder.get_id());
            apiOrder.setFilledAmount(dealtCcyFormat.format(totalFilledAmt));
            apiOrder.setUnfilledAmount(dealtCcyFormat.format(unfilledAmount));
            apiOrder.setOrderStatus(getOrderStatus(singleLegOrder));
            Trade trade = SingleLegTradeTranslator.getInstance().generateTrade(singleLegTrade, user);
            trade.setIsnet(wfMsg.getParameterValue(ISCommonConstants.DEAL_NET)!=null);
            apiOrder.setAcceptedTrade(trade);

            OrderMessage message = new OrderMessage();
            apiOrder.setOrderMessage(message);
            message.setOrderId(singleLegOrder.get_id());
            message.setEventName(TRADE_PRE_RATE_VERIFIED_EVENT);
            message.setEventDetails(getTradeMessageDetails(singleLegTrade, singleLegOrder, dealtCurrency, dealtCcyFormat, settledCcy, settledCcyFormat, dateFormat, true));
            message.setEventTime(dateTimeFormat.format(singleLegTrade.getExecutionTime()));
            message.setTransactionId(singleLegTrade.get_id());
        } catch (Exception e) {
            log.error("Failed in acceptance message, ", e);
        }

        return apiOrder;
    }


//    @Override
//    public Order getCancellationMessage(WorkflowMessage wfMsg, User user) {
//        Object msgObject = wfMsg.getObject();
//        Order apiOrder = new Order();
//        try {
//            if (!(msgObject instanceof SingleLegTrade))
//                throw new IllegalArgumentException("msgObject not SingleLegTrade");
//
//            SingleLegTrade singleLegTrade = (SingleLegTrade)msgObject;
//            SingleLegOrder singleLegOrder = singleLegTrade.getOrderRequest();
//            if (singleLegOrder == null)
//                throw new NullPointerException("singleLegOrder");
//
//            Currency dealtCurrency = singleLegOrder.getDealtCurrency();
//            if (dealtCurrency == null)
//                throw new NullPointerException("dealtCurrency");
//
//            CurrencyPair currencyPair = singleLegOrder.getCurrencyPair();
//            if (currencyPair == null)
//                throw new NullPointerException("currencyPair");
//
//            Currency baseCurrency = currencyPair.getBaseCurrency();
//            if (baseCurrency == null)
//                throw new NullPointerException("baseCurrency");
//
//            Currency settledCcy = (baseCurrency.getShortName().equals(dealtCurrency.getShortName())) ? currencyPair.getVariableCurrency() : currencyPair.getBaseCurrency();
//
//            Currency variableCurrency = currencyPair.getVariableCurrency();
//            if (variableCurrency == null)
//                throw new NullPointerException("variableCurrency");
//
//            FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();
//            if (fxRateBasis == null)
//                throw new NullPointerException("fxRateBasis");
//
//            SimpleDateFormat dateFormat = FormatUtils.getFormatUtil().getDateFormat(user);
//            SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
//            DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
//            DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());
//            DecimalFormat settledCcyFormat = settledCcy.getDecimalFormat(userDecFormat.clone());
//
//            boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, variableCurrency);
//            DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);
//
//            apiOrder.setOrderId(singleLegOrder.get_id());
//            OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
//            if (requestLeg == null)
//                throw new NullPointerException("requestLeg");
//
//            copyParentOrderAttributes(user, singleLegOrder, requestLeg, apiOrder, dealtCurrency, fxRateBasis, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false, false);
//
//            apiOrder.setTransactionId(singleLegOrder.getTransactionId());
//            apiOrder.setParentOrderId(singleLegOrder.get_id());
//            apiOrder.setOrderStatus(getOrderStatus(singleLegOrder));
//
//            Trade trade = SingleLegTradeTranslator.getInstance().generateTrade(singleLegTrade, user);
//            apiOrder.setCancelledTrade(trade);
//
//            OrderMessage message = new OrderMessage();
//            apiOrder.setOrderMessage(message);
//            message.setOrderId(singleLegOrder.get_id());
//            message.setEventName(TRADE_CANCELLED_EVENT);
//            message.setEventDetails(getTradeMessageDetails(singleLegTrade, singleLegOrder, dealtCurrency, dealtCcyFormat, settledCcy, settledCcyFormat, dateFormat, false));
//            message.setEventTime(dateTimeFormat.format(singleLegTrade.getExecutionTime()));
//            message.setTransactionId(singleLegTrade.get_id());
//        } catch (Exception e) {
//            log.error("Failed in acceptance message, ", e);
//        }
//
//        return apiOrder;
//    }

    //@Override
    public Order getPreAggregatedFill(WorkflowMessage wfMsg, User user) {
        Object msgObj = wfMsg.getObject();
        if(msgObj == null) return null;

        SingleLegOrder singleLegOrder = (msgObj instanceof SingleLegOrder) ? (SingleLegOrder)msgObj : ((SingleLegTrade)msgObj).getOrderRequest();
        CurrencyPair currencyPair = singleLegOrder.getCurrencyPair();

        if (currencyPair == null)
            throw new NullPointerException("currencyPair");

        Currency baseCurrency = currencyPair.getBaseCurrency();
        if (baseCurrency == null)
            throw new NullPointerException("baseCurrency");

        Currency dealtCurrency = singleLegOrder.getDealtCurrency();
        if (dealtCurrency == null)
            throw new NullPointerException("dealtCurrency");

        Currency termCurrency = singleLegOrder.getTermCurrency();
        if (termCurrency == null)
            throw new NullPointerException("termCurrency");

        Currency settledCurrency = (baseCurrency.getShortName().equals(dealtCurrency.getShortName())) ? currencyPair.getVariableCurrency() : currencyPair.getBaseCurrency();
        if (settledCurrency == null)
            throw new NullPointerException("settledCurrency");

        OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
        if (requestLeg == null)
            throw new NullPointerException("requestLeg");

        OrderRequest.RequestLeg.BuySellMode buySellMode = requestLeg.getBuySellMode();
        if (buySellMode == null)
            throw new NullPointerException("buySellMode");
        FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();

        if (fxRateBasis == null)
            throw new NullPointerException("fxRateBasis");

        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
        DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());
        boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, termCurrency);
        DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

        Order apiOrder = new Order();
        apiOrder.setOrderId(singleLegOrder.get_id());

        //get original ccy pair , tenor , settlment type
        FXIApiUtil.getInstance().updateInstrumentDetailsWithOriginal( currencyPair.getBaseCurrency() , currencyPair.getVariableCurrency(),
                apiOrder, dealtCurrency , settledCurrency, fxRateBasis);

        boolean isBid = (buySellMode == OrderRequest.RequestLeg.BuySellMode.BUY);
        boolean isBaseCcyOrder = singleLegOrder.getDealtCurrency().isSameAs(singleLegOrder.getBaseCurrency());
        apiOrder.setOrderSide((isBid && isBaseCcyOrder) || (!isBid && !isBaseCcyOrder) ? OrderSideType.Buy : OrderSideType.Sell);

        OrderType orderType = getOrderType(singleLegOrder);
        apiOrder.setOrderType(orderType);
        OrderExpiryType timeInForce = getTimeInForce(singleLegOrder);
        apiOrder.setTimeInForce(timeInForce);

        apiOrder.setTransactionId(singleLegOrder.getTransactionId());
        apiOrder.setOrderStatus(OrderStatusType.PreAggregatedFill);
        apiOrder.setSpotRate(rateFormat.format(singleLegOrder.getOrderSpotRate()));
        apiOrder.setAmount(dealtCcyFormat.format(requestLeg.getAmount()));

        double filledAmt = requestLeg.getFilledAmount();
        double unfilledAmt = requestLeg.getAmount() - filledAmt;
        apiOrder.setFilledAmount(dealtCcyFormat.format(filledAmt));
        apiOrder.setUnfilledAmount(dealtCcyFormat.format(unfilledAmt));
        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
        setRequestEventTimes(singleLegOrder, apiOrder, dateTimeFormat);
        return apiOrder;
    }

    //@Override
    public Order getOrderExpirationMessage(WorkflowMessage wfMsg, User user) {
        Order apiOrder = new Order();
        Object msgObj = wfMsg.getObject();
        SingleLegOrder singleLegOrder = (msgObj instanceof SingleLegOrder) ? (SingleLegOrder)msgObj : ((SingleLegTrade)msgObj).getOrderRequest();
        try {
            DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
            Currency dealtCurrency = singleLegOrder.getDealtCurrency();
            if (dealtCurrency == null)
                throw new NullPointerException("dealtCurrency");

            Currency termCurrency = singleLegOrder.getTermCurrency();
            if (termCurrency == null)
                throw new NullPointerException("termCurrency");

            FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();
            if (fxRateBasis == null)
                throw new NullPointerException("fxRateBasis");

            SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
            DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());

            boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, termCurrency);
            DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

            double avgFilledPrice = singleLegOrder.getOrderAverageFillRate();
            double unfilledAmount = singleLegOrder.getDealtCurrency().round( singleLegOrder.getOrderAmount() - singleLegOrder.getOrderFilledAmount() );
            double totalFilledAmt = singleLegOrder.getOrderFilledAmount();
            apiOrder.setOrderId(singleLegOrder.get_id());
            OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
            if (requestLeg == null)
                throw new NullPointerException("requestLeg");

            copyParentOrderAttributes(user, singleLegOrder, requestLeg, apiOrder, dealtCurrency, fxRateBasis, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false, false);

            apiOrder.setTransactionId(singleLegOrder.getTransactionId());
            apiOrder.setFillRate(rateFormat.format(avgFilledPrice));
            apiOrder.setFilledAmount(dealtCcyFormat.format(totalFilledAmt));
            apiOrder.setUnfilledAmount(dealtCcyFormat.format(unfilledAmount));
            apiOrder.setOrderStatus(getOrderStatus(singleLegOrder));
            apiOrder.setTradeClassification(singleLegOrder.getOrderTradeClassification());

            State singleLegOrderState = singleLegOrder.getState();
            if (singleLegOrderState == null)
                throw new NullPointerException("singleLegOrderState");

            if (singleLegOrderState.getName() == State.Name.RSSUSPENDED) {
                apiOrder.setExecutionState(ExecutionState.STOPPED);
            }

            OrderMessage message = new OrderMessage();
            apiOrder.setOrderMessage(message);
            message.setOrderId(singleLegOrder.get_id());
            message.setEventName(ORDER_CANCELED_EVENT);
            message.setTransactionId(singleLegOrder.getTransactionId());

            if (singleLegOrder.isServerManaged() && singleLegOrder.isExpiredByScheduler()) {
                apiOrder.setCancelBySystem(true);
                message.setEventTime(dateTimeFormat.format(singleLegOrder.getExpireTime()));
			}
			else if ( singleLegOrder.isUnsolicitedCancel() ) {
            	 apiOrder.setCancelBySystem(true);
            } else {
                apiOrder.setCancelBySystem(false);
                OrderRequestEventTimes orderReqEventTimes = singleLegOrder.getOrderRequestEventTimes();
                if (orderReqEventTimes != null) {
                    message.setEventTime(dateTimeFormat.format(orderReqEventTimes.getCancellationTime()));
                }
            }
            apiOrder.setCancelledBy(singleLegOrder.getCancelledBy());
            message.setEventDetails(FXIApiUtil.getInstance().getOrderCancellationMessage(apiOrder));
        } catch (Exception e) {
            log.error("Failed to get order expiration message, ", e);
        }

        return apiOrder;
    }

    //@Override
    public Order getOrderMessageForEvent(String event, WorkflowMessage wfMsg, User user , OrderStatusType status) {
        Object msgObj = wfMsg.getObject();
        SingleLegOrder singleLegOrder = (msgObj instanceof SingleLegOrder) ? (SingleLegOrder)msgObj : ((SingleLegTrade)msgObj).getOrderRequest();
        if (singleLegOrder == null) {
            log.error("Failed to get order message for event " + event, new NullPointerException("singleLegOrder"));
            return null;
        }

        OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
        if (requestLeg == null) {
            log.error("Failed to get order message for event " + event, new NullPointerException("requestLeg"));
            return null;
        }

        Currency dealtCurrency = singleLegOrder.getDealtCurrency();
        if (dealtCurrency == null) {
            log.error("Failed to get order message for event " + event, new NullPointerException("dealtCurrency"));
            return null;
        }

        Currency termCurrency = singleLegOrder.getTermCurrency();
        if (termCurrency == null) {
            log.error("Failed to get order message for event " + event, new NullPointerException("termCurrency"));
            return null;
        }

        FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();
        if (fxRateBasis == null) {
            log.error("Failed to get order message for event " + event, new NullPointerException("fxRateBasis"));
            return null;
        }

        SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
        DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
        DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());

        boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, termCurrency);
        DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

        double avgFilledPrice = singleLegOrder.getOrderAverageFillRate();
        double unfilledAmount = singleLegOrder.getDealtCurrency().round( singleLegOrder.getOrderAmount() - singleLegOrder.getOrderFilledAmount() );
        double totalFilledAmt = singleLegOrder.getOrderFilledAmount();

        Order apiOrderResponse = new Order();
        apiOrderResponse.setOrderId(singleLegOrder.get_id());
        apiOrderResponse.setTransactionId(singleLegOrder.getTransactionId());
        copyParentOrderAttributes(user, singleLegOrder, requestLeg, apiOrderResponse, dealtCurrency, fxRateBasis, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false, false);
        apiOrderResponse.setFillRate(rateFormat.format(avgFilledPrice));
        apiOrderResponse.setFilledAmount(dealtCcyFormat.format(totalFilledAmt));
        apiOrderResponse.setUnfilledAmount(dealtCcyFormat.format(unfilledAmount));
        apiOrderResponse.setOrderStatus(status);

        if(status.name().equalsIgnoreCase(OrderStatusType.Amended.name()) || status.name().equalsIgnoreCase(OrderStatusType.Restated.name())) {
            double origOrderAmount = 0;         // TODO must be added to spaces when Amend is supported
            if(origOrderAmount > 0.0) {
                apiOrderResponse.setOrigOrderAmount(userDecFormat.format(origOrderAmount));
            }

            double origOrderRate = requestLeg.getOriginalSpotRate();
            if(origOrderRate > 0.0) {
                apiOrderResponse.setOrigOrderRate(rateFormat.format(origOrderRate));
            }

            apiOrderResponse.setEventSeqId((Long) wfMsg.getParameterValue(ISCommonConstants.EVENT_SEQ_ID));
            //orderResponse.setLstOrdAmt((String) wfMsg.getParameterValue(ISCommonConstants.LAST_ORDER_AMOUNT));
            //orderResponse.setAvailableAmount()
        }

        if(singleLegOrder.isExecutionSuspended()) {
            apiOrderResponse.setExecutionState(ExecutionState.STOPPED);
        }

        OrderMessage message = new OrderMessage();
        apiOrderResponse.setOrderMessage(message);
        message.setOrderId(singleLegOrder.get_id());
        message.setEventName(event);
        message.setEventDetails(getEventDetails(apiOrderResponse, event));
        message.setEventTime(dateTimeFormat.format(singleLegOrder.getModifiedTime()));
        message.setTransactionId(singleLegOrder.getTransactionId());

        return apiOrderResponse;
    }

    public SingleLegOrder newOrderRequest() {
        SingleLegOrder orderRequest = new SingleLegOrder();
        setDealingModelFields(orderRequest);
        return orderRequest;
    }

    private static void setDealingModelFields(DealingModel dmo) {
        dmo.setCreatedTime(System.currentTimeMillis());
        dmo.setVirtualServer(ConfigurationFactory.getServerMBean().getVirtualServerName());
        long currentTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().asJdkDate().getTime();
        dmo.setCreatedBusinessDate(currentTradeDate);
    }

    public SingleLegOrder createSingleLegOrder(Organization customerOrg, LegalEntity customerLe, com.integral.fxiapi.model.OrderRequest apiOrder, User user , String portfolioId) throws RuntimeException {
        SingleLegOrder singleLegOrder = newOrderRequest();

        if (user == null)
            throw new NullPointerException("user");

        singleLegOrder.setUser(user);

        com.integral.model.dealing.OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
        requestLeg.setTenor(ISCommonConstants.SPOT);

        double orderRate = 0.0;
        double stopRate = 0.0;
        switch (apiOrder.getOrderType()) {
            case Limit:
                singleLegOrder.setType(com.integral.model.dealing.OrderRequest.Type.LIMIT);
                orderRate = apiOrder.getLimitPrice();
                break;

            case Market:
                singleLegOrder.setType(com.integral.model.dealing.OrderRequest.Type.MARKET);
                orderRate = apiOrder.getLimitPrice();
                break;

            case Algo:
                singleLegOrder.setType(com.integral.model.dealing.OrderRequest.Type.MARKET);
                if(apiOrder.getNwtp() > 0.0){
                    orderRate = apiOrder.getNwtp();
                    singleLegOrder.setNoWorseThanPriceProvided( true );
                }
                break;

            case Stop:
                singleLegOrder.setType(com.integral.model.dealing.OrderRequest.Type.STOP);
                stopRate = apiOrder.getStopPrice();
                break;

            case StopLimit:
                singleLegOrder.setType(com.integral.model.dealing.OrderRequest.Type.STOPLIMIT);
                stopRate = apiOrder.getStopPrice();
                orderRate = apiOrder.getLimitPrice();
                break;

            case TrailingStop:
                singleLegOrder.setType(com.integral.model.dealing.OrderRequest.Type.STOP);
                stopRate = apiOrder.getStopPrice();
                break;

            default:
                log.error("Invalid order type " + apiOrder.getOrderType());
                return null;
        }

        switch (apiOrder.getOrderSide()) {     // in order request B/S is always in terms of base ccy
            case Buy:
                requestLeg.setBuySellMode(com.integral.model.dealing.OrderRequest.RequestLeg.BuySellMode.BUY);
                break;

            case Sell:
                requestLeg.setBuySellMode(com.integral.model.dealing.OrderRequest.RequestLeg.BuySellMode.SELL);
                break;

            default:
                log.error("Invalid order side " + apiOrder.getOrderSide());
                return null;
        }

        String instrument = apiOrder.getSimulatedInstrument();
        CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(instrument);
        singleLegOrder.setCurrencyPair(currencyPair);

        String dealtInstrument = apiOrder.getSimulatedDealtInstrument();
        Currency dealtCurrency = CurrencyFactory.getCurrency(dealtInstrument);
        singleLegOrder.setDealtCurrency(dealtCurrency);

        FXRateConvention fxRateConvention = QuoteConventionUtilC.getInstance().getFXRateConvention(customerOrg);
        DealingModelUtil.setFXRateBasisRef(singleLegOrder, fxRateConvention);

        singleLegOrder.setStopLossTriggerRate(stopRate);
        singleLegOrder.setStopLossOriginalTriggerRate(stopRate);
        requestLeg.setSpotRate(orderRate);
        requestLeg.setOriginalSpotRate(orderRate);
        singleLegOrder.setChannel(apiOrder.getTradeChannel());
        String channel = FXIApiUtil.getInstance().getChannel(user);

        singleLegOrder.setRequestChannel(ISUtilImpl.getInstance().getExternalSys(channel).getShortName());
        singleLegOrder.setOrderMetaData(apiOrder.getOrderMetadata());

        String clientOrderId = apiOrder.getClientOrderId();
        singleLegOrder.setClientReferenceId(clientOrderId);
        if (customerOrg != null) {
            singleLegOrder.setOrganization(customerOrg);
            singleLegOrder.setNamespace(customerOrg.getNamespace());
        }
        if (!"WarmUpTestReq".equalsIgnoreCase(clientOrderId)) {
            singleLegOrder.setLegalEntity(customerLe);
        }

        double dealtAmount = FXIApiUtil.getInstance().expandAmount(apiOrder.getAmount());
        requestLeg.setAmount(dealtAmount);

        OrderVisibility.Type orderVisibilityType;
        double maxShowAmount;
        if (apiOrder.getMaxShow() != null) {
            maxShowAmount = FXIApiUtil.getInstance().expandAmount(apiOrder.getMaxShow());
            if( maxShowAmount < MathUtilC.getMinAmount(singleLegOrder.getDealtCurrency())){
                orderVisibilityType = OrderVisibility.Type.HIDDEN;
            }
            else if( maxShowAmount < singleLegOrder.getOrderAmount() ){
                orderVisibilityType = OrderVisibility.Type.ICEBERG;
            }
            else {
                orderVisibilityType = OrderVisibility.Type.DISPLAY;
            }
        }
        else{
            maxShowAmount = singleLegOrder.getOrderAmount();
            orderVisibilityType = OrderVisibility.Type.DISPLAY;
        }
        singleLegOrder.setOrderVisibilityType(orderVisibilityType);
        singleLegOrder.setMaxShowAmount( maxShowAmount );
        singleLegOrder.setOriginalMaxShowAmount( maxShowAmount );
        singleLegOrder.getExecutionInstructions().setFixingName(apiOrder.getFixingName());
        singleLegOrder.setFixing(apiOrder.getFixingName() != null);
        if (apiOrder.getMinQty() != null) {
            double minDealtAmount = FXIApiUtil.getInstance().expandAmount(apiOrder.getMinQty());
            requestLeg.setMinFillAmount(minDealtAmount);
        }

        ArrayList<ExecutionStrategyType> execFlags = apiOrder.getExecFlags();
        if(execFlags != null && execFlags.contains(ExecutionStrategyType.TradingVenue)) {
            /**
             * Fixing order - show amount should be set to 0 and visibility should be HIDDEN
             */
            singleLegOrder.setMaxShowAmount(0.0d);
            singleLegOrder.setOriginalMaxShowAmount(0.0d);
            singleLegOrder.setOrderVisibilityType(OrderVisibility.Type.HIDDEN);
            requestLeg.setMinFillAmount(0.0d);
        }

        OrderRequestEventTimes orderReqEventTimes = singleLegOrder.getOrderRequestEventTimes();
        if (orderReqEventTimes == null)
            throw new NullPointerException("orderReqEventTimes");

        Date clientOrderTime = apiOrder.getClientOrderTime();
        if (clientOrderTime != null) {
            orderReqEventTimes.setSentByConsumer(clientOrderTime.getTime());
        }

        requestLeg.setExternalSpreads(new ArrayList<SpreadInfo>());
        if(StringUtils.isNotEmpty(apiOrder.getSpotSpread()))
        {
            Double dbl = Double.valueOf(apiOrder.getSpotSpread());
            SpreadInfo spreadInfo = new SpreadInfo();
            spreadInfo.setSpread(dbl);
            spreadInfo.setFromOrgName(user.getOrganization().getShortName());
            if (customerOrg != null) {
                spreadInfo.setToOrgName(customerOrg.getShortName());
            }
            spreadInfo.setType(SpreadInfo.Type.EXT);
            spreadInfo.setSpreadEnabled(true);
            requestLeg.getExternalSpreads().add(spreadInfo);
        }
        setTimeInforce(singleLegOrder, apiOrder.getExpiryType());

        String orderNotes = apiOrder.getOrderNotes();
        if(!StringUtils.isBlank(orderNotes))
        {
            singleLegOrder.setNotes(apiOrder.getOrderNotes());
        }

        if(!StringUtils.isBlank(portfolioId))
        {
            singleLegOrder.setPortfolioId(portfolioId);
            singleLegOrder.setOriginatingPortfolioId(portfolioId);
        }
        if(execFlags != null && execFlags.contains(ExecutionStrategyType.FAStreamsOnly) && isFOKTypeOrder(apiOrder)){
            singleLegOrder.setExecutionFlags(singleLegOrder.getExecutionFlags() | ExecutionFlags.ONLY_FA_STREAMS);
        }
        if ( apiOrder.getCustomParameters() != null && !apiOrder.getCustomParameters().isEmpty() )
        {
            singleLegOrder.setCustomParameters(IdcUtilC.serializeMapToJSON(apiOrder.getCustomParameters()));
        }

        return singleLegOrder;
    }

    private boolean isFOKTypeOrder(com.integral.fxiapi.model.OrderRequest orderRequest){
        boolean fok = orderRequest.getExpiryType() != null && orderRequest.getExpiryType().equals(OrderExpiryType.FOK);
        boolean qtyCheck = orderRequest.getMinQty() != null && FXIApiUtil.getInstance().expandAmount(orderRequest.getAmount()) == FXIApiUtil.getInstance().expandAmount(orderRequest.getMinQty());
        return fok || qtyCheck;
    }

    private OrderExpiryType getTimeInForce(SingleLegOrder singleLegOrder) {
        TimeInForce tif = singleLegOrder.getTimeInForce();
        if (tif == null)
            return null;

        switch (tif) {
            case GTC:
                return OrderExpiryType.GTC;

            case GTD:
                return OrderExpiryType.GTD;

            case FOK:
                return OrderExpiryType.FOK;

            case DAY:
                return OrderExpiryType.DO;

            case IOC:
                return OrderExpiryType.IOC;

            case GTF:
                return OrderExpiryType.GTF;
        }

        return null;
    }

    private OrderExpiryType getTimeInForce(FXSingleLegOrder cdqOrder) {
        String expClf = cdqOrder.getExpirationClassification();
        if (expClf == null)
            return null;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_GTC))
            return OrderExpiryType.GTC;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_GTD))
            return OrderExpiryType.GTD;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_FOK))
            return OrderExpiryType.FOK;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_DAY))
            return OrderExpiryType.DO;

        if (expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_IOC))
            return OrderExpiryType.IOC;

        if(expClf.equals(ISConstantsC.ORDER_CLASSIFICATION_GTF))
            return OrderExpiryType.GTF;

        return null;
    }

    private void setTimeInforce(SingleLegOrder singleLegOrder, OrderExpiryType orderExpiryType) {
        if( orderExpiryType == null ) {
            log.error("Invalid orderExpiryType " + orderExpiryType);
            singleLegOrder.setTimeInForce(TimeInForce.UNDEFINED);
            return;
        }

        switch (orderExpiryType) {
            case GTC:
                singleLegOrder.setTimeInForce(TimeInForce.GTC);
                break;

            case GTD:       // Good Till Date (not Day)
                singleLegOrder.setTimeInForce(TimeInForce.GTD);
                break;

            case FOK:
                singleLegOrder.setTimeInForce(TimeInForce.FOK);
                break;

            case DO:        // Day Order
                singleLegOrder.setTimeInForce(TimeInForce.DAY);
                break;

            case IOC:
                singleLegOrder.setTimeInForce(TimeInForce.IOC);
                break;

            case GTF:
                singleLegOrder.setTimeInForce(TimeInForce.GTF);
                break;

            default:
                log.error("Invalid orderExpiryType " + orderExpiryType);
                singleLegOrder.setTimeInForce(TimeInForce.UNDEFINED);
                break;
        }
    }

    private OrderType getOrderType(SingleLegOrder singleLegOrder) {
        OrderRequest.Type type = singleLegOrder.getType();
        if (type == null)
            return null;

        if (singleLegOrder.isPQOrder())
            return OrderType.PQ;

        switch (type) {
            case LIMIT:
                return OrderType.Limit;
            case STOP:
                // Check if this is a trailing stop order by examining execution flags
                int execFlags = singleLegOrder.getExecutionFlags();
                if ((execFlags & ExecutionFlags.TRAILING) > 0) {
                    return OrderType.TrailingStop;
                }
                return OrderType.Stop;
            case MARKET:
                return OrderType.Market;
            case STOPLIMIT:
                return OrderType.StopLimit;
            case TLSTOP:
                return OrderType.TrailingStop;
        }

        return null;
    }

    private OrderType getOrderType(FXSingleLegOrder cdqOrder) {
    	if(cdqOrder.isPQOrder()) return OrderType.PQ;
        String type = cdqOrder.getOrderType();
        if (type.equals(ISCommonConstants.MARKET) || type.equals(ISCommonConstants.MARKET_ORDER)) {
            return OrderType.Market;
        }

        if (type.equals(ISCommonConstants.LIMIT_ORDER)) {
            return OrderType.Limit;
        }

        if (type.equals(ISCommonConstants.STOP_TYPE) || type.equals(UIGConstants.CDQ_ORDER_TYPE_STOP)) {
            // Check if this is a trailing stop order by examining execution flags
            int execFlags = cdqOrder.getExecutionFlags();
            if ((execFlags & ExecutionFlags.TRAILING) > 0) {
                return OrderType.TrailingStop;
            }
            return OrderType.Stop;
        }

        if( type.equals(UIGConstants.TRAILING_STOP_TYPE) ) {
            return OrderType.TrailingStop;
        }

        if (type.equals(ISCommonConstants.STOPLIMIT_TYPE) || type.equals(UIGConstants.CDQ_ORDER_TYPE_STOPLIMIT)) {
            return OrderType.StopLimit;
        }

        return null;
    }

    private OrderStatusType getOrderStatus(SingleLegOrder singleLegOrder) throws NullPointerException {
        State singleLegOrderState = singleLegOrder.getState();
        if (singleLegOrderState == null)
            throw new NullPointerException("singleLegOrderState");

        switch (singleLegOrderState.getName()) {
            case  RSINIT:
                if (singleLegOrder.isStrategyExecutionSuspended()) {
                    return OrderStatusType.Suspended;
                }
                return OrderStatusType.Pending;
            case RSACTIVE:
                return OrderStatusType.Accepted;
            case RSDECLINED:
                return OrderStatusType.Rejected;
            case RSCANCELLED:
                return OrderStatusType.Cancelled;
            case RSEXECUTED:
                return OrderStatusType.Filled;
            case RSPARTIAL:
                if (singleLegOrder.isStrategyExecutionSuspended()) {
                    return OrderStatusType.Suspended;
                }
                return OrderStatusType.Partial;
            case RSEXCEPTION:
                return OrderStatusType.Rejected;
            case RSSUSPENDED:
                return OrderStatusType.Suspended;
            case RSEXPIRED:
                return OrderStatusType.Expired;
            case RSPRERATEPARTIAL:
                return OrderStatusType.PreRatePartial;
            case RSPRERATECOMPLETE:
                return OrderStatusType.PreRateFilled;
            case RSPRERATECANCELLED:
                return OrderStatusType.PreRateCancelled;
            case RSPRERATEEXPIRED:
                return OrderStatusType.PreRateExpired;
            default:
                log.error("Invalid state " + singleLegOrderState.getName() + ", orderId " + singleLegOrder.get_id());
                break;
        }

        return null;
    }

    private MessageType  convertOrderStatusToMsgType(OrderStatusType orderStatusType)
    {
            switch (orderStatusType)
            {
                case Pending:
                    return MessageType.ORDER_SUBMITTED;
                case Cancelled:
                case Expired:
                    return MessageType.ORDER_CANCELLED;        // in both case of Expired also we are sending that.
                case Amended:
                    return MessageType.ORDER_AMENDED;
                case Suspended:
                    return MessageType.ORDER_SUSPENDED;
                case Resumed:
                    return MessageType.ORDER_RESUMED;
                case Filled:
                    return MessageType.ORDER_FILLED;
            }
        return null;
    }

    private OrderStatusType getOrderStatus(FXSingleLegOrder cdqOrder) throws NullPointerException {

        OrderStateFacade cdqOrderState = (OrderStateFacade)cdqOrder.getFacade(OrderStateFacade.FACADE_NAME);
        if (cdqOrderState == null)
            throw new NullPointerException("cdqOrderState");

        if (cdqOrderState.isCancelled())
            return OrderStatusType.Cancelled;

        if (cdqOrderState.isFailed())
            return OrderStatusType.Rejected;

        if (cdqOrderState.isFilled())
            return OrderStatusType.Filled;

        if (cdqOrderState.isExpired())
            return OrderStatusType.Expired;

        if (cdqOrder.isStrategyExecutionSuspended())
            return OrderStatusType.Suspended;

        if (cdqOrderState.isCreated()) {
            if (cdqOrder.isOmsOrder())
                return OrderStatusType.Pending_New;
            return OrderStatusType.Pending;
        }

        if(cdqOrderState.isPreRatePartial())
            return OrderStatusType.PreRatePartial;

        if(cdqOrderState.isPreRateFilled())
            return OrderStatusType.PreRateFilled;

        if(cdqOrderState.isPreRateCancelled())
            return OrderStatusType.PreRateCancelled;

        if(cdqOrderState.isPreRateExpired())
            return OrderStatusType.PreRateExpired;

        if (cdqOrderState.isActive()) {
            if (cdqOrder.isOmsOrder())
                return OrderStatusType.New;
            return OrderStatusType.Accepted;
        }

        if(cdqOrderState.isCancelPending())
            return OrderStatusType.PendingCancel;

        if(cdqOrderState.isAmendPending())
            return OrderStatusType.PendingReplace;

        return null;
    }

    private ArrayList<ExecutionStrategyType> getExecutionStrategies(SingleLegOrder singleLegOrder) {
        ArrayList<ExecutionStrategyType> execStrategies = new ArrayList<ExecutionStrategyType>();
        execStrategies.clear();
        int execFlags = singleLegOrder.getExecutionFlags();
        if ((execFlags & ExecutionFlags.ALLOW_QUOTE_CROSS) > 0) {
            execStrategies.add(ExecutionStrategyType.OK_TO_CROSS);
        }
        if ((execFlags & ExecutionFlags.BEST_PRICE) > 0) {
            execStrategies.add(ExecutionStrategyType.BestPrice);
        }
        if ((execFlags & ExecutionFlags.SWEEP) > 0) {
            execStrategies.add(ExecutionStrategyType.Sweep);
        }
        if ((execFlags & ExecutionFlags.TRAILING) > 0) {
            execStrategies.add(ExecutionStrategyType.TrailingStopPeg);
        }
        if ((execFlags & ExecutionFlags.TWAP) > 0) {
            execStrategies.add(ExecutionStrategyType.TWAP);
        }
        if ((execFlags & ExecutionFlags.VWAP) > 0) {
            execStrategies.add(ExecutionStrategyType.PegToVWAP);
        }
        if((execFlags & ExecutionFlags.ONLY_FA_STREAMS) > 0){
            execStrategies.add(ExecutionStrategyType.FAStreamsOnly);
        }

        ExecutionInstructions.SecondaryPricePriority secondaryPricePriority = singleLegOrder.getSecondaryPricePriority();
        if (secondaryPricePriority != null) {
            switch (secondaryPricePriority) {
                case SPP:
                    execStrategies.add(ExecutionStrategyType.ProviderPriority);
                    break;
                case SPS:
                    execStrategies.add(ExecutionStrategyType.SizePriority);
                    break;
                case SPT:
                    execStrategies.add(ExecutionStrategyType.TimePriority);
                    break;
                case SPTL:
                    execStrategies.add(ExecutionStrategyType.LatestTimePriority);
                    break;
                default:
                    log.error("Invalid order strategy SecondaryPricePriority " + secondaryPricePriority);
                    break;
            }
        }

        OrderRequest.Type type = singleLegOrder.getType();
        if (type == null)
            return execStrategies;

        RequestClassification singleLetOrderRequestClassification = ISUtilImpl.getInstance().getRequestClassification(type.name());
        String clsf = singleLetOrderRequestClassification.getShortName();
        if (ISCommonConstants.STOP_TYPE.equals(clsf) || ISCommonConstants.STOPLIMIT_TYPE.equals(clsf)) {
            if ((execFlags & ExecutionFlags.AT_RATE) > 0) {
                execStrategies.add(ExecutionStrategyType.PrimaryPeg);
            }

            OrderTrigger orderTrigger = singleLegOrder.getOrderTrigger();       // currently orders are triggered only for stop loss
            if (orderTrigger != null) {
                OrderTrigger.Type triggerType = orderTrigger.getTriggerType();
                if (triggerType != null) {
                    switch (triggerType) {
                        case BID:
                            execStrategies.add(ExecutionStrategyType.StayOnBidSide);
                            break;
                        case OFFER:
                            execStrategies.add(ExecutionStrategyType.StayOnOfferSide);
                            break;
                        case MID:
                            execStrategies.add(ExecutionStrategyType.MidPrice);
                            break;
                        default:
                            log.error("Invalid trigger type " + triggerType);
                            break;
                    }
                }
            }
        }
        return execStrategies;
    }

    private ArrayList<ExecutionStrategyType> getExecutionStrategies(FXSingleLegOrder cdqOrder) {
        ArrayList<ExecutionStrategyType> execStrategies = new ArrayList<ExecutionStrategyType>();
        execStrategies.clear();
        int execFlags = cdqOrder.getExecutionFlags();
        if ((execFlags & ExecutionFlags.ALLOW_QUOTE_CROSS) > 0) {
            execStrategies.add(ExecutionStrategyType.OK_TO_CROSS);
        }
        if ((execFlags & ExecutionFlags.BEST_PRICE) > 0) {
            execStrategies.add(ExecutionStrategyType.BestPrice);
        }
        if ((execFlags & ExecutionFlags.SWEEP) > 0) {
            execStrategies.add(ExecutionStrategyType.Sweep);
        }
        if ((execFlags & ExecutionFlags.TRAILING) > 0) {
            execStrategies.add(ExecutionStrategyType.TrailingStopPeg);
        }
        if ((execFlags & ExecutionFlags.TWAP) > 0) {
            execStrategies.add(ExecutionStrategyType.TWAP);
        }
        if ((execFlags & ExecutionFlags.VWAP) > 0) {
            execStrategies.add(ExecutionStrategyType.PegToVWAP);
        }
        if((execFlags & ExecutionFlags.ONLY_FA_STREAMS) > 0){
            execStrategies.add(ExecutionStrategyType.FAStreamsOnly);
        }

        ExecutionInstructions.SecondaryPricePriority secondaryPricePriority = cdqOrder.getSecondaryPricePriority();
        if (secondaryPricePriority != null) {
            switch (secondaryPricePriority) {
                case SPP:
                    execStrategies.add(ExecutionStrategyType.ProviderPriority);
                    break;
                case SPS:
                    execStrategies.add(ExecutionStrategyType.SizePriority);
                    break;
                case SPT:
                    execStrategies.add(ExecutionStrategyType.TimePriority);
                    break;
                case SPTL:
                    execStrategies.add(ExecutionStrategyType.LatestTimePriority);
                    break;
                default:
                    log.error("Invalid order strategy SecondaryPricePriority " + secondaryPricePriority);
                    break;
            }
        }

        Classification ordrClsfn = cdqOrder.getOrderClassification();
        if(ordrClsfn == null)
            return execStrategies;

        String clsf = ordrClsfn.getShortName();
        if (ISCommonConstants.STOP_TYPE.equals(clsf) || ISCommonConstants.STOPLIMIT_TYPE.equals(clsf)) {
            if ((execFlags & ExecutionFlags.AT_RATE) > 0) {
                execStrategies.add(ExecutionStrategyType.PrimaryPeg);
            }

            OrderTrigger.Type triggerType = cdqOrder.getTriggerType();       // currently orders are triggered only for stop loss
            if (triggerType != null) {
                switch (triggerType) {
                    case BID:
                        execStrategies.add(ExecutionStrategyType.StayOnBidSide);
                        break;
                    case OFFER:
                        execStrategies.add(ExecutionStrategyType.StayOnOfferSide);
                        break;
                    case MID:
                        execStrategies.add(ExecutionStrategyType.MidPrice);
                        break;
                    default:
                        log.error("Invalid trigger type " + triggerType);
                        break;
                }
            }
         }
        return execStrategies;
    }

    private boolean isESPOrder(FXSingleLegOrder cdqOrder) {
        String type = cdqOrder.getOrderType();
        if (type == null)
            return false;

        if (type.equals(ISCommonConstants.LIMIT_ORDER) ||  type.equals(ISCommonConstants.MARKET) ||
        	type.equals(ISCommonConstants.MARKET_ORDER) ||
            type.equals(ISCommonConstants.STOP_TYPE) ||  type.equals(ISCommonConstants.STOPLIMIT_TYPE)) {

            return true;
        }

        return false;
    }

    private String getOrderSubmittedMessageDetails(SingleLegOrder singleLegOrder, Order apiOrder) {
        String bidOffer;
        OrderSideType orderSide;

        // if base ccy is not dealt ccy then flip the buy/sell sides
        if (!apiOrder.getCurrency().equals(CurrencyFactory.getBaseCurrency(apiOrder.getInstrument()))) {
            bidOffer = apiOrder.getOrderSide() == OrderSideType.Buy ? "Offer" : "Bid";
            orderSide = apiOrder.getOrderSide() == OrderSideType.Buy ? OrderSideType.Sell : OrderSideType.Buy;
        } else {
            bidOffer = apiOrder.getOrderSide() == OrderSideType.Buy ? "Bid" : "Offer";
            orderSide = apiOrder.getOrderSide();
        }

        String eventDetails = "";
        String valueDate = (StringUtils.isNotBlank(apiOrder.getValueDate())) ? apiOrder.getValueDate() : "";

        switch (apiOrder.getOrderType()) {
            case Limit:

                eventDetails = new StringBuilder(200).append("Submitted ").append(apiOrder.getLimitRate())
                                    .append(" ").append(bidOffer).append(" to ").append(orderSide)
                                    .append(' ').append(apiOrder.getAmount()).append(' ').append(apiOrder.getCurrency())
                                    .append(" vs. ").append(apiOrder.getSettledCurrency())
                                    .append(". Value Date: ").append(valueDate)
                                    .append(" Tenor: SPOT.").toString();
                break;

            case Market:

                eventDetails = new StringBuilder(200).append("Submitted ").append(apiOrder.getLimitRate())
                                    .append(" ").append(bidOffer).append(" to ").append(orderSide).append(' ')
                                    .append(apiOrder.getAmount()).append(' ').append(apiOrder.getCurrency())
                                    .append(" vs. ").append(apiOrder.getSettledCurrency()).append(". Value Date: ")
                                    .append(valueDate).append("Tenor: SPOT.").append(" Market Range:")
                                    .append(apiOrder.getRange()).toString();
                break;

            case Stop:
                String triggerType = "";
                OrderTrigger orderTrigger = singleLegOrder.getOrderTrigger();       // currently orders are triggered only for stop loss
                if (orderTrigger != null) {
                    OrderTrigger.Type orderTriggerType = orderTrigger.getTriggerType();
                    if (orderTriggerType != null) {
                        switch (orderTriggerType) {
                            case BID:
                                triggerType = "Bid";
                                break;
                            case OFFER:
                                triggerType = "Offer";
                                break;
                            case MID:
                                triggerType = "Mid";
                                break;
                            default:
                                log.error("Invalid trigger type " + orderTriggerType);
                                break;
                        }
                    }
                }

                eventDetails = new StringBuilder(200).append("Submitted ").append(apiOrder.getStopPrice())
                                    .append(" Stop MKT ").append(bidOffer).append(" to ").append(orderSide)
                                    .append(' ').append(apiOrder.getAmount()).append(" ").append(apiOrder.getCurrency())
                                    .append(" vs. ").append(apiOrder.getSettledCurrency()).append(". Value Date: ")
                                    .append(valueDate).append("Tenor: SPOT. ").append(" Trigger side: ")
                                    .append(triggerType).toString();
                break;

            case StopLimit:
                break;
        }

        return eventDetails;
    }

    private String getTradeMessageDetails(SingleLegTrade singleLegTrade, SingleLegOrder singleLegOrder, Currency dealtCcy, DecimalFormat dealtCcyFormat, Currency settledCcy, DecimalFormat settledCcyFormat, SimpleDateFormat dateFormat, boolean isVerification) {
        TradeLeg tradeLeg = singleLegTrade.getTradeLeg();
        if (tradeLeg == null)
            throw new NullPointerException("tradeLeg");

        LegalEntity counterpartyLegalEntity = DealingModelUtil.getCounterPartyLegalEntity(singleLegTrade);
        if (counterpartyLegalEntity == null)
            throw new NullPointerException("counterpartyLegalEntity");

        Organization tradingPartyOrg = counterpartyLegalEntity.getOrganization();
        if (tradingPartyOrg == null)
            throw new NullPointerException("tradingPartyOrg");

        OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
        if (requestLeg == null)
            throw new NullPointerException("requestLeg");

        OrderRequest.RequestLeg.BuySellMode buySellMode = requestLeg.getBuySellMode();
        if (buySellMode == null)
            throw new NullPointerException("buySellMode");

        String provider = tradingPartyOrg.getShortName();
        String providerCpty = counterpartyLegalEntity.getShortName();

        double dealtAmount = singleLegTrade.getDealtAmount();
        double orderAmount = singleLegOrder.getOrderAmount();
        double settledAmt = singleLegTrade.getSettledAmount();
        String dealtCcyName = dealtCcy.getShortName();
        String settledCcyName = settledCcy.getShortName();
        String valueDate = dateFormat.format(tradeLeg.getValueDate());

        boolean isBuyMode = (buySellMode == OrderRequest.RequestLeg.BuySellMode.BUY);
        String buySell;
        if (!singleLegOrder.getTermCurrency().equals(singleLegOrder.getDealtCurrency())) {
            buySell = isBuyMode ? "Buy" : "Sell";
        } else {
            buySell = isBuyMode ? "Sell" : "Buy";
        }
        StringBuilder eventDetails = new StringBuilder(200).append(buySell).append(' ').append(dealtCcyFormat.format(dealtAmount)).append(" of ").append(dealtCcyFormat.format(orderAmount))
                .append(' ').append(dealtCcyName).append(" vs. ").append(settledCcyFormat.format(settledAmt)).append(' ').append(settledCcyName).append(" against ")
                .append(singleLegOrder.getTransactionId()).append(". Value Date: ").append(valueDate).append(". From: ").append(provider).append(". Counterparty ")
                .append(providerCpty);
        if(isVerification) {
            eventDetails.append(". External ID: ").append(singleLegTrade.getExternalReferenceId()).append(" Price verified.").toString();
        }

        return eventDetails.toString();
    }

    protected String getFormattedDate(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.format(date);
        }
        catch (Exception ex) {
            log.error("getFormattedDate : Exception in formatting date " + ex);
            return "";
        }
    }

	//@Override
	public Order getOrderPendingMessage(WorkflowMessage wfMsg, User user)
	{
		Order apiOrder = new Order();
        Object msgObj = wfMsg.getObject();
        SingleLegOrder singleLegOrder = (msgObj instanceof SingleLegOrder) ? (SingleLegOrder)msgObj : ((SingleLegTrade)msgObj).getOrderRequest();
        try {
            DecimalFormat userDecFormat = FormatUtils.getFormatUtil().getAmountFormat(user);
            Currency dealtCurrency = singleLegOrder.getDealtCurrency();
            if (dealtCurrency == null)
                throw new NullPointerException("dealtCurrency");

            Currency termCurrency = singleLegOrder.getTermCurrency();
            if (termCurrency == null)
                throw new NullPointerException("termCurrency");

            FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();
            if (fxRateBasis == null)
                throw new NullPointerException("fxRateBasis");

            SimpleDateFormat dateTimeFormat = FormatUtils.getFormatUtil().getDateTimeFormat(user);
            DecimalFormat dealtCcyFormat = dealtCurrency.getDecimalFormat(userDecFormat.clone());

            boolean isRateInverted = RateRoundingService.isRateInverted(fxRateBasis, termCurrency);
            DecimalFormat rateFormat = RateRoundingService.getSpotRateFormat(userDecFormat, fxRateBasis, isRateInverted);

            double avgFilledPrice = (Double)wfMsg.getParameterValue(ISConstantsC.ORDER_AVG_FILL_PRICE);
            String ORDER_UNMATCHED_AMOUNT = "UnFillAmt";
            String ORDER_PENDING_AMOUNT = "PendingAmt";
            String ORDER_VERIFIED_AMOUNT = "VerifiedAmt";
            double unfilledAmount = (Double)wfMsg.getParameterValue(ORDER_UNMATCHED_AMOUNT);
            double totalFilledAmt = (Double)wfMsg.getParameterValue(ORDER_VERIFIED_AMOUNT);
            double pendingAmt = (Double)wfMsg.getParameterValue(ORDER_PENDING_AMOUNT);

            apiOrder.setOrderId(singleLegOrder.get_id());
            OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
            if (requestLeg == null)
                throw new NullPointerException("requestLeg");

            copyParentOrderAttributes(user, singleLegOrder, requestLeg, apiOrder, dealtCurrency, fxRateBasis, userDecFormat, dealtCcyFormat, rateFormat, dateTimeFormat, false, false);

            apiOrder.setTransactionId(singleLegOrder.getTransactionId());
            apiOrder.setFillRate(rateFormat.format(avgFilledPrice));
            apiOrder.setFilledAmount(dealtCcyFormat.format(totalFilledAmt));
            apiOrder.setUnfilledAmount(dealtCcyFormat.format(unfilledAmount));
            apiOrder.setPendingAmount(dealtCcyFormat.format(pendingAmt));
            apiOrder.setOrderStatus(OrderStatusType.NotifPending);
            apiOrder.setTradeClassification(singleLegOrder.getOrderTradeClassification());

            State singleLegOrderState = singleLegOrder.getState();
            if (singleLegOrderState == null)
                throw new NullPointerException("singleLegOrderState");

            apiOrder.setExecutionState(ExecutionState.ACTIVE);

            OrderMessage message = new OrderMessage();
            apiOrder.setOrderMessage(message);
            message.setOrderId(singleLegOrder.get_id());
            message.setEventName(ORDER_PENDING_EVENT);
            message.setTransactionId(singleLegOrder.getTransactionId());

            if (singleLegOrder.isServerManaged() && singleLegOrder.isExpiredByScheduler()) {
                apiOrder.setCancelBySystem(true);
                message.setEventTime(dateTimeFormat.format(singleLegOrder.getExpireTime()));
            } else {
                apiOrder.setCancelBySystem(false);
                OrderRequestEventTimes orderReqEventTimes = singleLegOrder.getOrderRequestEventTimes();
                if (orderReqEventTimes != null) {
                    message.setEventTime(dateTimeFormat.format(orderReqEventTimes.getCancellationTime()));
                }
            }
            apiOrder.setCancelledBy(singleLegOrder.getCancelledBy());
            message.setEventDetails(FXIApiUtil.getInstance().getOrderPendingMessage(apiOrder));
        } catch (Exception e) {
            log.error("Failed to get order pending message, ", e);
        }

        return apiOrder;
	}
}
