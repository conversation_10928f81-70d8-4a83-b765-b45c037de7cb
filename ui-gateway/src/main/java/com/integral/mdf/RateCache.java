package com.integral.mdf;


import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.fxiapi.model.v2.RateTypes;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.provision.MDFAggregationType;
import com.integral.uig.PriceBook;
import com.integral.user.Organization;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Tags;
import org.springframework.stereotype.Component;

import static com.integral.provision.MDFAggregationType.*;
import static com.integral.uig.PriceDistributionManager.getLineKey;

@Component
public class RateCache {

    private static class INSTANCE_HOLDER {
        public static RateCache instance = new RateCache();
    }

    ConcurrentHashMap<Long,PriceBook> cache = new ConcurrentHashMap<>(100);
    RateCachePrometheusMetrics prometheusMetrics;
    RateCacheMetrics metrics = new RateCacheMetrics();

    public static RateCache getInstance() {
        return INSTANCE_HOLDER.instance;
    }
    
    private RateCache(){
        MetricsManager.instance().register(metrics);
    }
    
    public void setMeterRegistry(MeterRegistry meterRegistry) {
        this.prometheusMetrics = new RateCachePrometheusMetrics(meterRegistry, cache);
    }

    public boolean update(long key, UnSafeBuffer book) {
        Timer.Sample sample = prometheusMetrics != null ? prometheusMetrics.startUpdateTimer(key) : null;
        
        metrics.incrementRateCount(key);
        if (prometheusMetrics != null) prometheusMetrics.incrementRateUpdate(key);
        
        PriceBook cBook = cache.get(key);
        if (cBook == null) {
            cBook = new PriceBook();
            cache.put(key, cBook);
            metrics.incrementCacheAdd(key);
            if (prometheusMetrics != null) prometheusMetrics.incrementCacheAdd(key);
        }
        cBook.readFrom(book);
        
        if (sample != null) sample.stop(prometheusMetrics.getUpdateTimer(key));
        return true;
    }

    public void setStaleRate(long key){
        metrics.incrementStaleCount(key);
        if (prometheusMetrics != null) prometheusMetrics.incrementStaleRate(key);
        PriceBook book = cache.get(key);
        if(book == null) return;

        book.setBidPrices(new double[0]);
        book.setBidQtys(new double[0]);
        book.setOfferPrices(new double[0]);
        book.setOfferQtys(new double[0]);

        book.setTimeEffective(System.currentTimeMillis());
        if (book.getVersion() >= 2) {
            book.setBidNoLP(new int[0]);
            book.setOfferNoLP(new int[0]);
            book.setNumBids(0);
            book.setNumOffers(0);
        }
        if (book.getVersion() >= 3) {
            book.setQuoteId(0);
            book.setQuoteTimeEffective(book.getTimeEffective());
        }
    }

    /**
     * Customer specific Full Book price.
     * @param fi - FI Index
     * @param bc - Base currency Index
     * @param vc - Variable currency Index
     * @return
     */
    public Optional<PriceBook> getFullBook(int fi, int bc, int vc) {
        return _getBook(fi, bc, vc, FULL_BOOK);
    }

    /**
     * Customer specific Top-of-book price .
     * @param fi - FI Index
     * @param bc - Base currency Index
     * @param vc - Variable currency Index
     * @return
     */
    public Optional<PriceBook> getTOB(int fi, int bc, int vc) {
        return _getBook(fi, bc, vc, BEST_PRICE);
    }

    /**
     * @param fi - FI Index
     * @param bc - Base currency Index
     * @param vc - Variable currency Index
     * @return
     */
    public Optional<PriceBook> getRawBook(int fi, int bc, int vc) {
        return _getBook(fi, bc, vc, RAW_BOOK);
    }

    /**
     * @param bc - Base currency Index
     * @param vc - Variable currency Index
     * @return
     */
    public Optional<PriceBook> getGenericFullBook(int bc, int vc) {
        Optional<String> fbSource = MDFConfigMBeanC.getInstance().getFullBookGenericFeedSource();
        if (fbSource.isPresent()) {
            Organization org = ReferenceDataCacheC.getInstance().getOrganization(fbSource.get());
            if (org != null) {
                return getFullBook(org.getIndex(), bc, vc);
            }
        }
        return Optional.empty();
    }

    /**
     * @param bc - Base currency Index
     * @param vc - Variable currency Index
     * @return
     */
    public Optional<PriceBook> getGenericTOB(int bc, int vc) {
        Optional<String> tobSource = MDFConfigMBeanC.getInstance().getTOBGenericFeedSource();
        if (tobSource.isPresent()) {
            Organization org = ReferenceDataCacheC.getInstance().getOrganization(tobSource.get());
            if (org != null) {
                return getTOB(org.getIndex(), bc, vc);
            }
        }
        return Optional.empty();
    }

    /**
     * @param bc - Base currency Index
     * @param vc - Variable currency Index
     * @return
     */
    public Optional<PriceBook> getGenericRawBook(int bc, int vc) {
        Optional<String> rawSource = MDFConfigMBeanC.getInstance().getRawBookGenericFeedSource();
        if (rawSource.isPresent()) {
            Organization org = ReferenceDataCacheC.getInstance().getOrganization(rawSource.get());
            if (org != null) {
                return getRawBook(org.getIndex(), bc, vc);
            }
        }
        return Optional.empty();
    }

    private Optional<PriceBook> _getBook(int fi, int bc, int vc, MDFAggregationType at) {
        long key = getLineKey(fi, bc, vc, at.getIndex());
        PriceBook book = cache.get(key);
        if (book != null) {
            metrics.incrementCacheHit(key);
            if (prometheusMetrics != null) prometheusMetrics.incrementCacheHit(key);
        } else {
            metrics.incrementCacheMiss(key);
            if (prometheusMetrics != null) prometheusMetrics.incrementCacheMiss(key);
        }
        return Optional.ofNullable(book);
    }

    public Optional<PriceBook> getGenericBook(int bc, int vc, RateTypes type){
        switch (type){
            case TOB: return getGenericTOB(bc,vc);
            case FULL:return getGenericFullBook(bc,vc);
            case RAW:return getGenericRawBook(bc,vc);
        }
        return Optional.empty();
    }

    public Optional<PriceBook> getBook(int fi, int bc, int vc, RateTypes type){
        switch (type){
            case TOB: return getTOB(fi,bc,vc);
            case FULL:return getFullBook(fi,bc,vc);
            case RAW:return getRawBook(fi,bc,vc);
        }
        return Optional.empty();
    }
}

class RateCacheMetrics implements Metrics {
    private ConcurrentHashMap<Long, AtomicLong> rateCountMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, AtomicLong> staleCountMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, AtomicLong> cacheHitMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, AtomicLong> cacheMissMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, AtomicLong> cacheAddMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, AtomicLong> updateLatencyMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<Long, AtomicLong> updateLatencyCountMap = new ConcurrentHashMap<>();

    @Override
    public StringBuilder report() {
        StringBuilder sb = new StringBuilder("RateCache Metrics:\n");
        
        sb.append("=== Key-Level Statistics ===\n");
        for (Long key : getAllKeys()) {
            sb.append(String.format("Key %d:\n", key));
            sb.append(String.format("  Rate Updates: %d\n", rateCountMap.getOrDefault(key, new AtomicLong()).get()));
            sb.append(String.format("  Stale Rates: %d\n", staleCountMap.getOrDefault(key, new AtomicLong()).get()));
            sb.append(String.format("  Cache Hits: %d\n", cacheHitMap.getOrDefault(key, new AtomicLong()).get()));
            sb.append(String.format("  Cache Misses: %d\n", cacheMissMap.getOrDefault(key, new AtomicLong()).get()));
            sb.append(String.format("  Cache Adds: %d\n", cacheAddMap.getOrDefault(key, new AtomicLong()).get()));
            
            long totalLatency = updateLatencyMap.getOrDefault(key, new AtomicLong()).get();
            long latencyCount = updateLatencyCountMap.getOrDefault(key, new AtomicLong()).get();
            if (latencyCount > 0) {
                sb.append(String.format("  Avg Update Latency: %.2f ns\n", (double) totalLatency / latencyCount));
            }
            
            long hits = cacheHitMap.getOrDefault(key, new AtomicLong()).get();
            long misses = cacheMissMap.getOrDefault(key, new AtomicLong()).get();
            if (hits + misses > 0) {
                sb.append(String.format("  Hit Ratio: %.2f%%\n", (double) hits / (hits + misses) * 100));
            }
            sb.append("\n");
        }

        rateCountMap.clear();
        staleCountMap.clear();
        cacheHitMap.clear();
        cacheMissMap.clear();
        cacheAddMap.clear();
        updateLatencyMap.clear();
        updateLatencyCountMap.clear();
        return sb;
    }

    private java.util.Set<Long> getAllKeys() {
        java.util.Set<Long> allKeys = new java.util.HashSet<Long>();
        allKeys.addAll(rateCountMap.keySet());
        allKeys.addAll(staleCountMap.keySet());
        allKeys.addAll(cacheHitMap.keySet());
        allKeys.addAll(cacheMissMap.keySet());
        allKeys.addAll(cacheAddMap.keySet());
        return allKeys;
    }

    public void incrementRateCount(long key){
        rateCountMap.computeIfAbsent(key, rateKey -> new AtomicLong()).incrementAndGet();
    }
    
    public void incrementStaleCount(long key){
        staleCountMap.computeIfAbsent(key, rateKey -> new AtomicLong()).incrementAndGet();
    }
    
    public void incrementCacheHit(long key){
        cacheHitMap.computeIfAbsent(key, rateKey -> new AtomicLong()).incrementAndGet();
    }
    
    public void incrementCacheMiss(long key){
        cacheMissMap.computeIfAbsent(key, rateKey -> new AtomicLong()).incrementAndGet();
    }
    
    public void incrementCacheAdd(long key){
        cacheAddMap.computeIfAbsent(key, rateKey -> new AtomicLong()).incrementAndGet();
    }
    
    public void recordUpdateLatency(long key, long latencyNs){
        updateLatencyMap.computeIfAbsent(key, rateKey -> new AtomicLong()).addAndGet(latencyNs);
        updateLatencyCountMap.computeIfAbsent(key, rateKey -> new AtomicLong()).incrementAndGet();
    }
}

class RateCachePrometheusMetrics {
    private final MeterRegistry meterRegistry;
    private final ConcurrentHashMap<Long, PriceBook> cache;
    private final ConcurrentHashMap<Long, Counter> rateUpdateCounters = new ConcurrentHashMap<Long, Counter>();
    private final ConcurrentHashMap<Long, Counter> staleRateCounters = new ConcurrentHashMap<Long, Counter>();
    private final ConcurrentHashMap<Long, Counter> cacheHitCounters = new ConcurrentHashMap<Long, Counter>();
    private final ConcurrentHashMap<Long, Counter> cacheMissCounters = new ConcurrentHashMap<Long, Counter>();
    private final ConcurrentHashMap<Long, Counter> cacheAddCounters = new ConcurrentHashMap<Long, Counter>();
    private final ConcurrentHashMap<Long, Timer> updateTimers = new ConcurrentHashMap<Long, Timer>();
    
    public RateCachePrometheusMetrics(MeterRegistry meterRegistry, ConcurrentHashMap<Long, PriceBook> cache) {
        this.meterRegistry = meterRegistry;
        this.cache = cache;
        registerCacheSizeGauge();
    }
    
    private void registerCacheSizeGauge() {
        Gauge.builder("rate_cache_size", cache, new java.util.function.ToDoubleFunction<ConcurrentHashMap<Long, PriceBook>>() {
                @Override
                public double applyAsDouble(ConcurrentHashMap<Long, PriceBook> map) {
                    return map.size();
                }
            })
            .description("Current size of the rate cache")
            .register(meterRegistry);
    }
    
    public void incrementRateUpdate(long key) {
        getRateUpdateCounter(key).increment();
    }
    
    public void incrementStaleRate(long key) {
        getStaleRateCounter(key).increment();
    }
    
    public void incrementCacheHit(long key) {
        getCacheHitCounter(key).increment();
    }
    
    public void incrementCacheMiss(long key) {
        getCacheMissCounter(key).increment();
    }
    
    public void incrementCacheAdd(long key) {
        getCacheAddCounter(key).increment();
    }
    
    public Timer.Sample startUpdateTimer(long key) {
        return Timer.start(meterRegistry);
    }
    
    public Timer getUpdateTimer(long key) {
        return updateTimers.computeIfAbsent(key, new java.util.function.Function<Long, Timer>() {
            @Override
            public Timer apply(Long k) {
                return Timer.builder("rate_cache_update_duration")
                    .description("Time taken to update rate cache entry")
                    .tag("key", String.valueOf(k))
                    .register(meterRegistry);
            }
        });
    }
    
    private Counter getRateUpdateCounter(long key) {
        return rateUpdateCounters.computeIfAbsent(key, new java.util.function.Function<Long, Counter>() {
            @Override
            public Counter apply(Long k) {
                return Counter.builder("rate_cache_updates_total")
                    .description("Total number of rate updates")
                    .tag("key", String.valueOf(k))
                    .register(meterRegistry);
            }
        });
    }
    
    private Counter getStaleRateCounter(long key) {
        return staleRateCounters.computeIfAbsent(key, new java.util.function.Function<Long, Counter>() {
            @Override
            public Counter apply(Long k) {
                return Counter.builder("rate_cache_stale_total")
                    .description("Total number of stale rates")
                    .tag("key", String.valueOf(k))
                    .register(meterRegistry);
            }
        });
    }
    
    private Counter getCacheHitCounter(long key) {
        return cacheHitCounters.computeIfAbsent(key, new java.util.function.Function<Long, Counter>() {
            @Override
            public Counter apply(Long k) {
                return Counter.builder("rate_cache_hits_total")
                    .description("Total number of cache hits")
                    .tag("key", String.valueOf(k))
                    .register(meterRegistry);
            }
        });
    }
    
    private Counter getCacheMissCounter(long key) {
        return cacheMissCounters.computeIfAbsent(key, new java.util.function.Function<Long, Counter>() {
            @Override
            public Counter apply(Long k) {
                return Counter.builder("rate_cache_misses_total")
                    .description("Total number of cache misses")
                    .tag("key", String.valueOf(k))
                    .register(meterRegistry);
            }
        });
    }
    
    private Counter getCacheAddCounter(long key) {
        return cacheAddCounters.computeIfAbsent(key, new java.util.function.Function<Long, Counter>() {
            @Override
            public Counter apply(Long k) {
                return Counter.builder("rate_cache_additions_total")
                    .description("Total number of cache additions")
                    .tag("key", String.valueOf(k))
                    .register(meterRegistry);
            }
        });
    }
}