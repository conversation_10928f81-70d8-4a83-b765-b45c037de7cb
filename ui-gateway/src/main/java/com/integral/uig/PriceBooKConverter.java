package com.integral.uig;

import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.price.FXPriceBookC;
import com.integral.aggregation.price.FXPriceC;
import com.integral.finance.currency.CurrencyPair;
import com.integral.fxiapi.model.v2.RateBook;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

import static java.lang.Math.min;

public class PriceBooKConverter {

    /**
     * An <code>int</code> that represents the number of seconds in a day - does not adjust for leap seconds.
     */
    public static final int SECONDS_PER_DAY = 24 * 60 * 60;

    /**
     * A <code>long</code> that represents the number of milliseconds in a day.
     */
    public static final long MILLISECONDS_PER_DAY = SECONDS_PER_DAY * 1000;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    private final Organization org;
    private final CurrencyPair ccyPair;
    private final String matchingVenue;
    private final String venueType;

    public PriceBooKConverter(Organization org,CurrencyPair ccyPair,String matchingVenue, String venueType){
        this.org = org;
        this.ccyPair = ccyPair;
        this.matchingVenue = matchingVenue;
        this.venueType = venueType;
    }


    /**
     * Converts PriceBook into FXPricebook based on customer's choice of tiers.
     * @param book
     * @param subscriber
     * @return
     */
    public FXPriceBook convert(PriceBook book, PriceDistributionLine.Subscriber subscriber) {
        if(subscriber.tiers.isEmpty())
            return convert(book);
        int tSize = subscriber.tiers.size();
        int bids = min(book.getNumBids() , tSize );
        int offers = min(book.getNumOffers() , tSize );
        FXPriceBook fxBook = new FXPriceBookC(bids, offers);
        boolean isStale = false;

        int src=0,t=0,dst=0;//bid and tier count
        while(src<book.getNumBids() && t<tSize){
            double lmt = book.getBidQtys()[src];
            double tier = subscriber.tiers.get(t);
            if(lmt>=tier){
                isStale &= getFXPrice(book,fxBook,src,dst++,tier,true);
                t++;//goto next tier
                if(lmt==tier)//goto next limit
                    src++;
            }else{
                //goto next limit
                src++;
                if(src==book.getNumBids()){//last available limt
                    isStale &= getFXPrice(book,fxBook,src-1,dst++,lmt,true);
                    break;
                }
            }
        }



        src=0;t=0;dst=0;//offer and tier count
        while(src<book.getNumOffers()  && t<tSize){
            double lmt = book.getOfferQtys()[src];
            double tier = subscriber.tiers.get(t);
            if(lmt>=tier){
                isStale &= getFXPrice(book,fxBook,src,dst++,tier,false);
                t++;//goto next tier
                if(lmt==tier)//goto next limit
                    src++;
            }else{
                //goto next limit
                src++;
                if(src==book.getNumOffers()){
                    isStale &= getFXPrice(book,fxBook,src-1,dst++,lmt,false);
                    break;
                }
            }
        }

        fxBook.setCurrencyPair(ccyPair);
        fxBook.setGenerationTimeStamp(book.getTimeEffective());

        fxBook.setOrganization(org);
        long valueDateMilliseconds = book.getValueDate() * MILLISECONDS_PER_DAY;
        fxBook.setValueDate(sdf.format(new Date(valueDateMilliseconds)));
        fxBook.setValueDateMilliseconds(valueDateMilliseconds);
        fxBook.setStale(isStale);

        fxBook.setMultiTier(false);
        fxBook.setBookId("G-" + String.valueOf(book.getBookId()) + "-" + Long.toHexString(book.getTimeEffective()));
        return fxBook;
    }

    boolean getFXPrice(PriceBook book,FXPriceBook fxBook, int srcIdx, int dstIdx,double tierLimit, boolean isBid){
        FXPrice tier = new FXPriceC();
        tier.setTier(dstIdx);
        double prc = isBid ? book.getBidPrices()[srcIdx] : book.getOfferPrices()[srcIdx];
        double qty = isBid ? book.getBidQtys()[srcIdx] : book.getOfferQtys()[srcIdx];
        int lpIdx = isBid ? book.getBidNoLP()[srcIdx] : book.getOfferNoLP()[srcIdx];
        tier.setRate(prc);
        tier.setSpotRate(prc);
        tier.setLimit(tierLimit);
        if (lpIdx!=0) {
            String lp = getLPName(lpIdx);
            if (lp != null) {
                tier.setLPName(lp);
            }
        } else {
            tier.setLPName(venueType.equals("MDF") ? matchingVenue : org.getShortName());
        }
        if(isBid)
            fxBook.addBid(tier);
        else
            fxBook.addOffer(tier);

        if (prc <= 0 || qty <= 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Straight conversion of @link{PriceBook} to target object.
     * @param book
     * @return
     */
    public FXPriceBook convert(PriceBook book) {
        FXPriceBook fxBook = new FXPriceBookC(book.getNumBids(), book.getNumOffers());
        boolean isStale = false;

        for (int i = 0; i < book.getNumBids(); i++) {
            FXPrice bid = new FXPriceC();
            bid.setTier(i);
            bid.setRate(book.getBidPrices()[i]);
            bid.setSpotRate(book.getBidPrices()[i]);
            bid.setLimit(book.getBidQtys()[i]);
            if ( book.isRawBook() || book.isFullBook() || book.isRawBook() ) {
                String lp = getLPName(book.getBidNoLP()[i]);
                if (lp != null) {
                    bid.setLPName(lp);
                }
            } else {
                bid.setLPName(venueType.equals("MDF") ? matchingVenue : org.getShortName());
            }

            if (book.getBidPrices()[i] <= 0 || book.getBidQtys()[i] <= 0) {
                isStale &= true;
            } else {
                isStale &= false;
            }
            /*bid.setLPTier();
            bid.setQuoteId();
            bid.setForwardPoint();
            bid.setMidRate();
            bid.setSettleAmount();*/

            fxBook.addBid(bid);
        }

        for (int i = 0; i < book.getNumOffers(); i++) {
            FXPrice offer = new FXPriceC();
            offer.setTier(i);
            offer.setRate(book.getOfferPrices()[i]);
            offer.setSpotRate(book.getOfferPrices()[i]);
            offer.setLimit(book.getOfferQtys()[i]);
            if ( book.isRawBook() || book.isFullBook() || book.isRawBook() ) {
                String lp = getLPName(book.getOfferNoLP()[i]);
                if (lp != null) {
                    offer.setLPName(lp);
                }
            } else {
                offer.setLPName(venueType.equals("MDF") ? matchingVenue : org.getShortName());
            }
            if (book.getOfferPrices()[i] <= 0 || book.getOfferQtys()[i] <= 0) {
                isStale &= true;
            } else {
                isStale &= false;
            }
            /*
            bid.setLPTier();
            bid.setQuoteId();
            bid.setForwardPoint();
            bid.setMidRate();
            bid.setSettleAmount();*/

            fxBook.addOffer(offer);
        }
        fxBook.setCurrencyPair(ccyPair);
        fxBook.setGenerationTimeStamp(book.getTimeEffective());

        fxBook.setOrganization(org);
        long valueDateMilliseconds = book.getValueDate() * MILLISECONDS_PER_DAY;
        fxBook.setValueDate(sdf.format(new Date(valueDateMilliseconds)));
        fxBook.setValueDateMilliseconds(valueDateMilliseconds);
        fxBook.setStale(isStale);

        fxBook.setMultiTier(false);
        fxBook.setBookId("G-" + String.valueOf(book.getBookId()) + "-" + Long.toHexString(book.getTimeEffective()));

        /*fxBook.setSequenceNo(book.getBookId());
        fxBook.setExpiryTime();*/

        return fxBook;
    }

    public static RateBook convertTo(PriceBook book,String symbol) {
        return convertTo(book, symbol, false);
    }

    public static RateBook convertTo(PriceBook book,String symbol,boolean isRateValidationEnabled) {
        // Validate prices if rate validation is enabled
        if(isRateValidationEnabled) {
            double[] bidPrices = book.getBidPrices();
            double[] offerPrices = book.getOfferPrices();

            // Check if any bid prices are zero
            if(bidPrices != null) {
                if(bidPrices.length == 0 || book.getNumBids() == 0) {
                    return null;
                }

                for(int i = 0; i < book.getNumBids(); i++) {
                    if(bidPrices[i] == 0.0) {
                        return null;
                    }
                }
            }else {
                // If bid prices are null, return null
                return null;
            }

            // Check if any offer prices are zero
            if(offerPrices != null) {
                if(offerPrices.length == 0 || book.getNumOffers() == 0) {
                    return null;
                }

                for(int i = 0; i < book.getNumOffers(); i++) {
                    if(offerPrices[i] == 0.0) {
                        return null;
                    }
                }
            } else {
                // If offer prices are null, return null
                return null;
            }
        }

        RateBook rb = new RateBook();
        rb.setBid(Arrays.copyOf(book.getBidPrices(),book.getNumBids()));
        rb.setBidLimit(Arrays.copyOf(book.getBidQtys(),book.getNumBids()));
        rb.setOffer(Arrays.copyOf(book.getOfferPrices(),book.getNumOffers()));
        rb.setOfferLimit(Arrays.copyOf(book.getOfferQtys(),book.getNumOffers()));
        rb.setSymbol(symbol);
        rb.setTime(book.getTimeEffective());
        if(book.isRawBook() || book.isBPBook() || book.isFullBook()){
            String[] bidProviders = new String[book.getNumBids()];
            for(int i=0;i<book.getNumBids();i++){
                String lp = getLPName(book.getBidNoLP()[i]);
                if (lp != null) {
                    bidProviders[i] = lp;
                }
            }
            rb.setBidProviders(bidProviders);
            String[] offerProviders = new String[book.getNumOffers()];
            for(int i=0;i<book.getNumOffers();i++){
                String lp = getLPName(book.getOfferNoLP()[i]);
                if (lp != null) {
                    offerProviders[i] = lp;
                }
            }
            rb.setOfferProviders(offerProviders);
        }
        return rb;
    }

    static String getLPName(int lpIdx){
        char prefix = lpIdx < 0 ? '+' : ' ';
        lpIdx = lpIdx < 0 ? -lpIdx : lpIdx;
        Organization lp = ReferenceDataCacheC.getInstance().getOrganization(lpIdx);
        if (lp != null) {
            return lp.getShortName()+prefix;
        }
        return null;
    }

}
