
> Task :dependencies

------------------------------------------------------------
Root project
------------------------------------------------------------

annotationProcessor - Annotation processors and their dependencies for source set 'main'.
\--- org.mapstruct:mapstruct-processor:1.4.1.Final

apiElements - API elements for main. (n)
No dependencies

archives - Configuration for archive artifacts. (n)
No dependencies

bootArchives - Configuration for Spring Boot archive artifacts.
No dependencies

compileClasspath - Compile classpath for source set 'main'.
+--- org.springdoc:springdoc-openapi-ui:1.5.2
|    +--- org.springdoc:springdoc-openapi-webmvc-core:1.5.2
|    |    +--- org.springdoc:springdoc-openapi-common:1.5.2
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.0 -> 2.2.2.RELEASE
|    |    |    |    \--- org.springframework.boot:spring-boot:2.2.2.RELEASE
|    |    |    |         +--- org.springframework:spring-core:5.2.2.RELEASE
|    |    |    |         |    \--- org.springframework:spring-jcl:5.2.2.RELEASE
|    |    |    |         \--- org.springframework:spring-context:5.2.2.RELEASE
|    |    |    |              +--- org.springframework:spring-aop:5.2.2.RELEASE
|    |    |    |              |    +--- org.springframework:spring-beans:5.2.2.RELEASE
|    |    |    |              |    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |    |              +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              \--- org.springframework:spring-expression:5.2.2.RELEASE
|    |    |    |                   \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-web:5.3.1 -> 5.2.2.RELEASE
|    |    |    |    +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    +--- io.swagger.core.v3:swagger-models:2.1.6
|    |    |    |    \--- com.fasterxml.jackson.core:jackson-annotations:2.11.1 -> 2.10.1
|    |    |    +--- io.swagger.core.v3:swagger-annotations:2.1.6
|    |    |    +--- io.swagger.core.v3:swagger-integration:2.1.6
|    |    |    |    +--- io.swagger.core.v3:swagger-core:2.1.6
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.7 -> 3.9
|    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.11.1 -> 2.10.1
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.1 -> 2.10.2
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.10.2 -> 2.10.1
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.10.2
|    |    |    |    |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.11.1 -> 2.10.1
|    |    |    |    |    |    +--- org.yaml:snakeyaml:1.24 -> 1.25
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.1 -> 2.10.1
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.10.1
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    |    |    |    +--- io.swagger.core.v3:swagger-annotations:2.1.6
|    |    |    |    |    +--- io.swagger.core.v3:swagger-models:2.1.6 (*)
|    |    |    |    |    \--- jakarta.validation:jakarta.validation-api:2.0.2 -> 2.0.1
|    |    |    |    \--- io.swagger.core.v3:swagger-models:2.1.6 (*)
|    |    |    +--- io.github.classgraph:classgraph:4.8.69
|    |    |    \--- org.apache.commons:commons-lang3:3.11 -> 3.9
|    |    \--- org.springframework:spring-webmvc:5.3.1 -> 5.2.2.RELEASE
|    |         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-expression:5.2.2.RELEASE (*)
|    |         \--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    +--- org.webjars:swagger-ui:3.38.0
|    \--- org.webjars:webjars-locator-core:0.45 -> 0.41
|         +--- org.slf4j:slf4j-api:1.7.7 -> 1.7.29
|         +--- com.fasterxml.jackson.core:jackson-core:2.9.8 -> 2.10.2
|         \--- org.webjars.npm:angular__http:2.4.10
+--- com.integral:oracle-persistence:9.9.9-SNAPSHOT
|    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT
|    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.29
|    |    \--- com.googlecode.disruptor:disruptor:2.8
|    +--- com.integral:util:1.2
|    |    +--- org.jctools:jctools-core:1.1
|    |    \--- com.integral:log:1.1 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:spaces:2.3
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.0.1 -> 5.9
|    |    |    +--- com.integral:serializer:1.3 -> 1.3.1
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.10.1
|    |    |    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    |    |    +--- de.undercouch:bson4jackson:2.4.0
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    |    |    |    +--- org.apache.maven:maven-artifact:3.0.3
|    |    |    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
|    |    |    |    \--- org.javassist:javassist:3.13.0-GA
|    |    |    +--- junit:junit:4.11 -> 4.12
|    |    |    |    \--- org.hamcrest:hamcrest-core:1.3 -> 2.1
|    |    |    |         \--- org.hamcrest:hamcrest:2.1
|    |    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    |    \--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- org.mongodb:mongo-java-driver:1.0.0
|    |    +--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- com.rabbitmq:amqp-client:3.6.0
|    |    +--- com.integral:serializer:1.2 -> 1.3.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- com.googlecode.disruptor:disruptor:2.8
|    |    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    |    \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.integral:serializer:1.3.1 (*)
|    +--- com.integral:messaging:1.0.5 -> 1.1-SNAPSHOT
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.rabbitmq:amqp-client:3.6.0
|    |    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- com.integral:serializer:1.0.1 -> 1.3.1 (*)
|    |    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    |    \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.integral:model:5.7 -> 5.9 (*)
|    +--- com.integral:clustering:1.0.1
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    |    +--- com.google.guava:guava:15.0 -> 29.0-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.11.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.4
|    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    +--- com.101tec:zkclient:0.1
|    |    +--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    +--- org.apache.helix:helix-core:0.7.3
|    |    \--- org.apache.zookeeper:zookeeper:3.4.6 -> 3.5.3-beta
|    |         +--- org.slf4j:slf4j-api:1.7.5 -> 1.7.29
|    |         +--- org.slf4j:slf4j-log4j12:1.7.5 -> 1.7.29
|    |         +--- commons-cli:commons-cli:1.2
|    |         +--- log4j:log4j:1.2.17
|    |         \--- io.netty:netty:3.10.5.Final
|    +--- com.integral:multicast:9.9.9-SNAPSHOT
|    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.12
|    |    |    +--- commons-logging:commons-logging:1.2
|    |    |    \--- commons-codec:commons-codec:1.11 -> 1.13
|    |    +--- log4j:log4j:1.2.14 -> 1.2.17
|    |    +--- org.apache.httpcomponents:httpcore:4.1 -> 4.4.12
|    |    +--- com.integral:util:1.1 -> 1.2 (*)
|    |    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- junit:junit:4.11 -> 4.12 (*)
|    |    +--- com.integral:rds:2.3 -> 3.0-SNAPSHOT
|    |    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    |    +--- com.integral:model:4.2 -> 5.9 (*)
|    |    |    +--- com.integral:spaces:2.0 -> 2.3 (*)
|    |    |    +--- com.integral:messaging:1.1-SNAPSHOT (*)
|    |    |    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    |    |    +--- io.netty:netty-common:4.1.1.Final -> 4.1.43.Final
|    |    |    +--- io.netty:netty-buffer:4.1.1.Final -> 4.1.43.Final
|    |    |    |    \--- io.netty:netty-common:4.1.43.Final
|    |    |    +--- io.netty:netty-handler:4.1.1.Final -> 4.1.43.Final
|    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.43.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    |    \--- io.netty:netty-resolver:4.1.43.Final
|    |    |    |    |         \--- io.netty:netty-common:4.1.43.Final
|    |    |    |    \--- io.netty:netty-codec:4.1.43.Final
|    |    |    |         +--- io.netty:netty-common:4.1.43.Final
|    |    |    |         +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |         \--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    +--- io.netty:netty-codec-http:4.1.1.Final -> 4.1.43.Final
|    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-codec:4.1.43.Final (*)
|    |    |    |    \--- io.netty:netty-handler:4.1.43.Final (*)
|    |    |    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    |    |    +--- commons-codec:commons-codec:1.5 -> 1.13
|    |    |    +--- org.eclipse.jetty.aggregate:jetty-all-server:8.1.15.v20140411
|    |    |    \--- io.micrometer:micrometer-registry-prometheus:1.5.2 -> 1.3.1
|    |    |         +--- io.micrometer:micrometer-core:1.3.1
|    |    |         |    +--- org.hdrhistogram:HdrHistogram:2.1.11
|    |    |         |    \--- org.latencyutils:LatencyUtils:2.0.3
|    |    |         \--- io.prometheus:simpleclient_common:0.7.0
|    |    |              \--- io.prometheus:simpleclient:0.7.0
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    \--- com.integral:model:2.5 -> 5.9 (*)
|    +--- com.integral:rds:2.3 -> 3.0-SNAPSHOT (*)
|    +--- com.integral:services:2.0
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- org.apache.curator:curator-framework:2.5.0 -> 4.0.1
|    |    |    \--- org.apache.curator:curator-client:4.0.1
|    |    |         +--- org.apache.zookeeper:zookeeper:3.5.3-beta (*)
|    |    |         +--- com.google.guava:guava:20.0 -> 29.0-jre (*)
|    |    |         \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.29
|    |    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    |    +--- org.apache.curator:curator-recipes:2.5.0 -> 4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.apache.curator:curator-client:2.5.0 -> 4.0.1 (*)
|    |    +--- org.apache.curator:curator-x-discovery:2.5.0 -> 4.0.1
|    |    |    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    |    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    +--- org.apache.zookeeper:zookeeper:3.4.6 -> 3.5.3-beta (*)
|    |    \--- com.integral:imtp:1.0.1
|    |         +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |         +--- org.jboss.netty:netty:3.2.4.Final
|    |         +--- com.sleepycat:je:5.0.34
|    |         +--- commons-lang:commons-lang:2.2 -> 2.6
|    |         +--- com.integral:serializer:1.0.1 -> 1.3.1 (*)
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.googlecode.disruptor:disruptor:2.8
|    +--- org.drools:drools-compiler:6.0.1.Final
|    +--- org.drools:drools-core:6.0.1.Final
|    +--- org.drools:kie-internal:6.0.1.Final
|    +--- org.drools:kie-api:6.0.1.Final
|    +--- com.ibm:com.ibm.mq:7.0
|    +--- com.ibm:com.ibm.mq.pcf:7.0
|    +--- com.ibm:com.ibm.mqjms:7.0
|    +--- com.ibm:com.ibm.mq.headers:7.0
|    +--- com.ibm:com.ibm.mq.jmqi:7.0
|    +--- com.amazonaws:aws-java-sdk:1.3.26
|    +--- com.google.http-client:google-http-client:1.20.0 -> 1.37.0
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 -> 4.5.10 (*)
|    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.12
|    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    +--- com.google.guava:guava:29.0-android -> 29.0-jre (*)
|    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    +--- io.opencensus:opencensus-api:0.24.0
|    |    |    \--- io.grpc:grpc-context:1.22.1 -> 1.33.0
|    |    \--- io.opencensus:opencensus-contrib-http-util:0.24.0
|    |         +--- io.opencensus:opencensus-api:0.24.0 (*)
|    |         \--- com.google.guava:guava:26.0-android -> 29.0-jre (*)
|    +--- org.jctools:jctools-core:1.1
|    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    +--- junit:junit:4.11 -> 4.12 (*)
|    +--- ch.qos.logback:logback-classic:1.0.6 -> 1.2.3 (*)
|    +--- ch.qos.logback:logback-core:1.0.6 -> 1.2.3
|    +--- ch.qos.logback:slf4j-api:1.6.1
|    +--- org.mongodb:mongo-java-driver:1.0.0
|    +--- mx4j:mx4j-tools:1.0.0
|    +--- cactus:cactus:1.5-rc1
|    +--- commons-jxpath:commons-jxpath:1.2
|    +--- commons-lang:commons-lang:2.2 -> 2.6
|    +--- commons-math:commons-math:1.1
|    +--- commons-codec:commons-codec:1.5 -> 1.13
|    +--- com.integral:TOPLink:1.0
|    +--- commons-httpclient:commons-httpclient:3.0-rc2
|    +--- org.apache.commons:commons-beanutils:1.6
|    +--- org.apache.commons:commons-collections:3.1
|    +--- commons-logging:commons-logging:1.0 -> 1.2
|    +--- com.integral:instantj:1.6
|    +--- log4j:log4j:1.2.14 -> 1.2.17
|    +--- commons-pool:commons-pool:1.5.5 -> 1.6
|    +--- castor:castor:0.9.3.9-xml
|    +--- com.ibm:ibm-bsf-regx:1.0
|    +--- com.integral:soap:1.0.0
|    +--- com.oracle:classes12:1.0.0
|    +--- exml:exml:7.0
|    +--- javax.mail:mail:1.0
|    +--- xmlunit:xmlunit:1.0
|    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    +--- com.thoughtworks.xstream:xstream:1.4.5
|    +--- com.sonicmq:sonic_client:7.6.2
|    +--- com.sonicmq:mgmt_client:7.6.2
|    +--- com.sonicmq:mgmt_config:7.6.2
|    +--- com.sonicmq:sonic_mgmt_client:7.6.2
|    +--- xerces:xercesImpl:1.0.0
|    +--- xerces:xml-apis:1.0.0
|    +--- xalan:xalan:1.0.0
|    +--- org.apache.tomcat:catalina:7.0.65
|    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10 (*)
|    +--- org.apache.httpcomponents:httpcore:4.1 -> 4.4.12
|    +--- org.apache.helix:helix-core:0.7.3
|    +--- com.101tec:zkclient:0.1
|    +--- com.rabbitmq:amqp-client:3.6.0
|    +--- quickfixj:quickfixj-all:1.4.0.1 -> 1.4.2
|    \--- org.codehaus.groovy:groovy-all:2.0.2
+--- com.rabbitmq:amqp-client:3.6.0
+--- org.mongodb:mongo-java-driver:1.0.0
+--- com.integral:monitor-core:3.0.3
|    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    \--- com.integral:util:1.1 -> 1.2 (*)
+--- org.json:json:20080701
+--- com.integral:rds:3.0-SNAPSHOT (*)
+--- com.integral:log:2.3-SNAPSHOT (*)
+--- com.integral:services:2.0 (*)
+--- com.integral:unity-server:2.4
|    +--- com.integral:spaces:1.0.1 -> 2.3 (*)
|    +--- com.corundumstudio.socketio:netty-socketio:1.7.11
|    |    +--- io.netty:netty-buffer:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-common:4.1.1.Final -> 4.1.43.Final
|    |    +--- io.netty:netty-transport:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-handler:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-codec-http:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-codec:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-transport-native-epoll:4.1.1.Final -> 4.1.43.Final
|    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    +--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    \--- io.netty:netty-transport-native-unix-common:4.1.43.Final
|    |    |         +--- io.netty:netty-common:4.1.43.Final
|    |    |         +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |         \--- io.netty:netty-transport:4.1.43.Final (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.29
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.7.4 -> 2.10.2
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.7.4 -> 2.10.2 (*)
|    +--- commons-codec:commons-codec:1.5 -> 1.13
|    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    +--- com.integral:util:1.0 -> 1.2 (*)
|    +--- com.integral:messaging:1.0.1 -> 1.1-SNAPSHOT (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
+--- com.integral:riskmanagement-common:2.5
|    +--- com.integral:darwin-common:2.5
|    |    +--- commons-collections:commons-collections:3.2.2
|    |    +--- org.springframework:spring-beans:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- com.integral:util:1.2 (*)
|    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    +--- org.springframework:spring-core:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- com.integral:model:1.2 -> 5.9 (*)
|    |    +--- com.integral:rds:2.1 -> 3.0-SNAPSHOT (*)
|    |    +--- com.integral:log:1.0 -> 2.3-SNAPSHOT (*)
|    |    +--- org.springframework:spring-context:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-test-autoconfigure:1.4.4.RELEASE -> 2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-test:2.2.2.RELEASE
|    |    |    |    \--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-webmvc:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot:1.4.4.RELEASE -> 2.2.2.RELEASE (*)
|    |    \--- com.integral:oracle-persistence:1.0 -> 9.9.9-SNAPSHOT (*)
|    +--- com.integral:TOPLink:1.0
|    +--- com.integral:services:2.0 (*)
|    +--- quickfixj:quickfixj-all:1.4.0 -> 1.4.2
|    +--- com.integral:yieldmanager-api:2.6.2
|    |    +--- com.integral:darwin-common:2.5 (*)
|    |    +--- com.integral:services:2.0 (*)
|    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    \--- com.integral:model:2.6 -> 5.9 (*)
|    \--- com.integral:multicast:1.0 -> 9.9.9-SNAPSHOT (*)
+--- com.integral:darwin-auth:3.15.0
+--- com.integral:model:5.9 (*)
+--- struts:struts:1.0.0
+--- commons-io:commons-io:1.1
+--- org.restlet.jee:org.restlet:1.0.0
+--- org.restlet.jee:org.restlet.ext.json:1.0.0
+--- com.fasterxml.jackson.core:jackson-databind:2.10.2 (*)
+--- com.fasterxml.jackson.core:jackson-core:2.10.2
+--- org.hibernate:hibernate-validator:5.3.1.Final
|    +--- javax.validation:validation-api:1.1.0.Final -> 2.0.1.Final
|    +--- org.jboss.logging:jboss-logging:3.3.0.Final -> 3.4.1.Final
|    \--- com.fasterxml:classmate:1.3.1 -> 1.5.1
+--- com.ning:async-http-client:1.6.2
+--- aopalliance:aopalliance:1.0
+--- aspectj:aspectjweaver:1.6.12
+--- commons-fileupload:commons-fileupload:1.1.1
+--- joda-time:joda-time:2.2
+--- org.owasp.esapi:esapi:2.0.1
+--- org.eclipse.jetty:jetty-server:9.4.28.v20200408
|    +--- javax.servlet:javax.servlet-api:3.1.0 -> 4.0.1
|    +--- org.eclipse.jetty:jetty-http:9.4.28.v20200408 -> 9.4.24.v20191120
|    |    +--- org.eclipse.jetty:jetty-util:9.4.24.v20191120
|    |    \--- org.eclipse.jetty:jetty-io:9.4.24.v20191120
|    |         \--- org.eclipse.jetty:jetty-util:9.4.24.v20191120
|    \--- org.eclipse.jetty:jetty-io:9.4.28.v20200408 -> 9.4.24.v20191120 (*)
+--- quickfixj:quickfixj-all:1.4.2
+--- org.apache.mina:mina-core:1.1.7
+--- org.apache.logging.log4j:log4j-api:2.16.0
+--- com.hazelcast:hazelcast:5.1.2
+--- org.mapstruct:mapstruct:1.4.1.Final
+--- org.mapstruct:mapstruct-jdk8:1.4.1.Final
+--- io.jsonwebtoken:jjwt:0.9.1
|    \--- com.fasterxml.jackson.core:jackson-databind:2.9.6 -> 2.10.2 (*)
+--- org.springframework.boot:spring-boot-starter-web -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.2.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.12.1 -> 2.16.0
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.16.0
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.29
|    |    |         \--- org.slf4j:slf4j-api:1.7.29
|    |    +--- jakarta.annotation:jakarta.annotation-api:1.3.5
|    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-json:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.10.1
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.10.1
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.2.2.RELEASE
|    |    +--- jakarta.annotation:jakarta.annotation-api:1.3.5
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.29
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.29
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.29
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.29
|    +--- org.springframework.boot:spring-boot-starter-validation:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    +--- jakarta.validation:jakarta.validation-api:2.0.1
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.29
|    |    \--- org.hibernate.validator:hibernate-validator:6.0.18.Final
|    |         +--- org.jboss.logging:jboss-logging:3.3.2.Final -> 3.4.1.Final
|    |         \--- com.fasterxml:classmate:1.3.4 -> 1.5.1
|    +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-tomcat -> 2.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-websocket -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter-web:2.2.2.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.2.2.RELEASE
|    |    +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    \--- org.springframework:spring-websocket:5.2.2.RELEASE
|         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|         \--- org.springframework:spring-web:5.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    \--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    \--- io.micrometer:micrometer-core:1.3.1 (*)
+--- io.micrometer:micrometer-core -> 1.3.1 (*)
+--- io.micrometer:micrometer-registry-prometheus -> 1.3.1 (*)
+--- org.springframework.cloud:spring-cloud-starter-consul-discovery -> 2.2.6.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-consul:2.2.6.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:2.2.7.RELEASE
|    |    |    |    \--- org.springframework.security:spring-security-crypto:5.3.6.RELEASE -> 5.2.1.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:2.2.7.RELEASE
|    |    |    |    \--- org.springframework.security:spring-security-crypto:5.3.6.RELEASE -> 5.2.1.RELEASE
|    |    |    \--- org.springframework.security:spring-security-rsa:1.0.9.RELEASE
|    |    |         \--- org.bouncycastle:bcpkix-jdk15on:1.64
|    |    |              \--- org.bouncycastle:bcprov-jdk15on:1.64
|    |    +--- org.springframework.cloud:spring-cloud-consul-core:2.2.6.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot-starter-validation:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    +--- com.ecwid.consul:consul-api:1.4.5
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 -> 4.5.10 (*)
|    |    \--- org.apache.httpcomponents:httpcore:4.4.14 -> 4.4.12
|    +--- org.springframework.cloud:spring-cloud-consul-discovery:2.2.6.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-consul-core:2.2.6.RELEASE (*)
|    |    \--- commons-configuration:commons-configuration:1.8
|    |         \--- commons-lang:commons-lang:2.6
|    +--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.2.7.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-starter-aop:2.3.8.RELEASE -> 2.2.2.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|    |         \--- org.aspectj:aspectjweaver:1.9.5
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.7.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.2.7.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.2.7.RELEASE
|    |    |    +--- com.netflix.archaius:archaius-core:0.7.7
|    |    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    |    +--- com.netflix.ribbon:ribbon:2.3.0
|    |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|    |    +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|    |    \--- io.reactivex:rxjava:1.3.8
|    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-loadbalancer:2.2.7.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-validation:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    |    +--- io.projectreactor:reactor-core:3.3.13.RELEASE -> 3.3.1.RELEASE
|    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.3
|    |    |    \--- io.projectreactor.addons:reactor-extra:3.3.5.RELEASE -> 3.3.1.RELEASE
|    |    |         \--- io.projectreactor:reactor-core:3.3.1.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-cache:2.3.8.RELEASE -> 2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    |    \--- org.springframework:spring-context-support:5.2.2.RELEASE
|    |    |         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |    |         \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    \--- com.stoyanr:evictor:1.0.0
|    \--- joda-time:joda-time:2.10.5 -> 2.2
+--- org.springframework.boot:spring-boot-starter-hateoas -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter-web:2.2.2.RELEASE (*)
|    \--- org.springframework.hateoas:spring-hateoas:1.0.2.RELEASE
|         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|         +--- org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE
|         |    +--- org.springframework:spring-beans:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    +--- org.springframework:spring-context:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    +--- org.springframework:spring-aop:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|         +--- com.jayway.jsonpath:json-path:2.4.0
|         |    +--- net.minidev:json-smart:2.3
|         |    |    \--- net.minidev:accessors-smart:1.2
|         |    |         \--- org.ow2.asm:asm:5.0.4
|         |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.29
+--- org.springframework.boot:spring-boot-starter-validation -> 2.2.2.RELEASE (*)
+--- com.integral:portal-service-apps:3.13.2-jdk8
+--- com.integral:price-making-commons:9.9.9-jdk8-SNAPSHOT
+--- com.integral:darwin-taggable-entities:3.13.2-jdk8
+--- com.google.code.gson:gson:2.8.6
\--- org.apache.logging.log4j:log4j-to-slf4j:2.16.0 (*)

compileOnly - Compile only dependencies for source set 'main'. (n)
No dependencies

default - Configuration for default artifacts. (n)
No dependencies

implementation - Implementation only dependencies for source set 'main'. (n)
+--- org.springframework.boot:spring-boot-starter-web (n)
+--- org.springframework.boot:spring-boot-starter-tomcat (n)
+--- org.springframework.boot:spring-boot-starter-websocket (n)
+--- org.springframework.boot:spring-boot-starter-actuator (n)
+--- io.micrometer:micrometer-core (n)
+--- io.micrometer:micrometer-registry-prometheus (n)
+--- org.springframework.cloud:spring-cloud-starter-consul-discovery (n)
+--- org.springframework.boot:spring-boot-starter-hateoas (n)
+--- org.springframework.boot:spring-boot-starter-validation (n)
+--- com.integral:portal-service-apps:3.13.2-jdk8 (n)
+--- com.integral:price-making-commons:9.9.9-jdk8-SNAPSHOT (n)
+--- com.integral:darwin-taggable-entities:3.13.2-jdk8 (n)
+--- com.google.code.gson:gson:2.8.6 (n)
\--- org.apache.logging.log4j:log4j-to-slf4j:2.16.0 (n)

runtimeClasspath - Runtime classpath of source set 'main'.
+--- org.springdoc:springdoc-openapi-ui:1.5.2
|    +--- org.springdoc:springdoc-openapi-webmvc-core:1.5.2
|    |    +--- org.springdoc:springdoc-openapi-common:1.5.2
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.0 -> 2.2.2.RELEASE
|    |    |    |    \--- org.springframework.boot:spring-boot:2.2.2.RELEASE
|    |    |    |         +--- org.springframework:spring-core:5.2.2.RELEASE
|    |    |    |         |    \--- org.springframework:spring-jcl:5.2.2.RELEASE
|    |    |    |         \--- org.springframework:spring-context:5.2.2.RELEASE
|    |    |    |              +--- org.springframework:spring-aop:5.2.2.RELEASE
|    |    |    |              |    +--- org.springframework:spring-beans:5.2.2.RELEASE
|    |    |    |              |    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |    |              +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              \--- org.springframework:spring-expression:5.2.2.RELEASE
|    |    |    |                   \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-web:5.3.1 -> 5.2.2.RELEASE
|    |    |    |    +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    +--- io.swagger.core.v3:swagger-models:2.1.6
|    |    |    |    \--- com.fasterxml.jackson.core:jackson-annotations:2.11.1 -> 2.10.1
|    |    |    +--- io.swagger.core.v3:swagger-annotations:2.1.6
|    |    |    +--- io.swagger.core.v3:swagger-integration:2.1.6
|    |    |    |    +--- io.swagger.core.v3:swagger-core:2.1.6
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.7 -> 3.9
|    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.11.1 -> 2.10.1
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.1 -> 2.10.2
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.10.2 -> 2.10.1
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.10.2
|    |    |    |    |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.11.1 -> 2.10.1
|    |    |    |    |    |    +--- org.yaml:snakeyaml:1.24 -> 1.25
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.1 -> 2.10.1
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.10.1
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    |    |    |    +--- io.swagger.core.v3:swagger-annotations:2.1.6
|    |    |    |    |    +--- io.swagger.core.v3:swagger-models:2.1.6 (*)
|    |    |    |    |    \--- jakarta.validation:jakarta.validation-api:2.0.2 -> 2.0.1
|    |    |    |    \--- io.swagger.core.v3:swagger-models:2.1.6 (*)
|    |    |    +--- io.github.classgraph:classgraph:4.8.69
|    |    |    \--- org.apache.commons:commons-lang3:3.11 -> 3.9
|    |    \--- org.springframework:spring-webmvc:5.3.1 -> 5.2.2.RELEASE
|    |         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-expression:5.2.2.RELEASE (*)
|    |         \--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    +--- org.webjars:swagger-ui:3.38.0
|    \--- org.webjars:webjars-locator-core:0.45 -> 0.41
|         +--- org.slf4j:slf4j-api:1.7.7 -> 1.7.29
|         +--- com.fasterxml.jackson.core:jackson-core:2.9.8 -> 2.10.2
|         \--- org.webjars.npm:angular__http:2.4.10
+--- com.integral:oracle-persistence:9.9.9-SNAPSHOT
|    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT
|    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.29
|    |    \--- com.googlecode.disruptor:disruptor:2.8
|    +--- com.integral:util:1.2
|    |    +--- org.jctools:jctools-core:1.1
|    |    \--- com.integral:log:1.1 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:spaces:2.3
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.0.1 -> 5.9
|    |    |    +--- com.integral:serializer:1.3 -> 1.3.1
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.10.1
|    |    |    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    |    |    +--- de.undercouch:bson4jackson:2.4.0
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    |    |    |    +--- org.apache.maven:maven-artifact:3.0.3
|    |    |    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
|    |    |    |    \--- org.javassist:javassist:3.13.0-GA
|    |    |    +--- junit:junit:4.11 -> 4.12
|    |    |    |    \--- org.hamcrest:hamcrest-core:1.3 -> 2.1
|    |    |    |         \--- org.hamcrest:hamcrest:2.1
|    |    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    |    \--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- org.mongodb:mongo-java-driver:1.0.0
|    |    +--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- com.rabbitmq:amqp-client:3.6.0
|    |    +--- com.integral:serializer:1.2 -> 1.3.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- com.googlecode.disruptor:disruptor:2.8
|    |    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    |    \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.integral:serializer:1.3.1 (*)
|    +--- com.integral:messaging:1.0.5 -> 1.1-SNAPSHOT
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.rabbitmq:amqp-client:3.6.0
|    |    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- com.integral:serializer:1.0.1 -> 1.3.1 (*)
|    |    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    |    \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.integral:model:5.7 -> 5.9 (*)
|    +--- com.integral:clustering:1.0.1
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    |    +--- com.google.guava:guava:15.0 -> 29.0-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.11.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.4
|    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    +--- commons-cli:commons-cli:1.2
|    |    +--- log4j:log4j:1.2.14 -> 1.2.17
|    |    +--- com.101tec:zkclient:0.1
|    |    +--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    +--- org.apache.helix:helix-core:0.7.3
|    |    \--- org.apache.zookeeper:zookeeper:3.4.6 -> 3.5.3-beta
|    |         +--- org.slf4j:slf4j-api:1.7.5 -> 1.7.29
|    |         +--- org.slf4j:slf4j-log4j12:1.7.5 -> 1.7.29
|    |         +--- commons-cli:commons-cli:1.2
|    |         +--- log4j:log4j:1.2.17
|    |         \--- io.netty:netty:3.10.5.Final
|    +--- com.integral:multicast:9.9.9-SNAPSHOT
|    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.12
|    |    |    +--- commons-logging:commons-logging:1.2
|    |    |    \--- commons-codec:commons-codec:1.11 -> 1.13
|    |    +--- log4j:log4j:1.2.14 -> 1.2.17
|    |    +--- org.apache.httpcomponents:httpcore:4.1 -> 4.4.12
|    |    +--- com.integral:util:1.1 -> 1.2 (*)
|    |    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- junit:junit:4.11 -> 4.12 (*)
|    |    +--- com.integral:rds:2.3 -> 3.0-SNAPSHOT
|    |    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    |    +--- com.integral:model:4.2 -> 5.9 (*)
|    |    |    +--- com.integral:spaces:2.0 -> 2.3 (*)
|    |    |    +--- com.integral:messaging:1.1-SNAPSHOT (*)
|    |    |    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    |    |    +--- io.netty:netty-common:4.1.1.Final -> 4.1.43.Final
|    |    |    +--- io.netty:netty-buffer:4.1.1.Final -> 4.1.43.Final
|    |    |    |    \--- io.netty:netty-common:4.1.43.Final
|    |    |    +--- io.netty:netty-handler:4.1.1.Final -> 4.1.43.Final
|    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.43.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    |    \--- io.netty:netty-resolver:4.1.43.Final
|    |    |    |    |         \--- io.netty:netty-common:4.1.43.Final
|    |    |    |    \--- io.netty:netty-codec:4.1.43.Final
|    |    |    |         +--- io.netty:netty-common:4.1.43.Final
|    |    |    |         +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |         \--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    +--- io.netty:netty-codec-http:4.1.1.Final -> 4.1.43.Final
|    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-codec:4.1.43.Final (*)
|    |    |    |    \--- io.netty:netty-handler:4.1.43.Final (*)
|    |    |    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    |    |    +--- commons-codec:commons-codec:1.5 -> 1.13
|    |    |    +--- org.eclipse.jetty.aggregate:jetty-all-server:8.1.15.v20140411
|    |    |    \--- io.micrometer:micrometer-registry-prometheus:1.5.2 -> 1.3.1
|    |    |         +--- io.micrometer:micrometer-core:1.3.1
|    |    |         |    +--- org.hdrhistogram:HdrHistogram:2.1.11
|    |    |         |    \--- org.latencyutils:LatencyUtils:2.0.3
|    |    |         \--- io.prometheus:simpleclient_common:0.7.0
|    |    |              \--- io.prometheus:simpleclient:0.7.0
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    \--- com.integral:model:2.5 -> 5.9 (*)
|    +--- com.integral:rds:2.3 -> 3.0-SNAPSHOT (*)
|    +--- com.integral:services:2.0
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- org.apache.curator:curator-framework:2.5.0 -> 4.0.1
|    |    |    \--- org.apache.curator:curator-client:4.0.1
|    |    |         +--- org.apache.zookeeper:zookeeper:3.5.3-beta (*)
|    |    |         +--- com.google.guava:guava:20.0 -> 29.0-jre (*)
|    |    |         \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.29
|    |    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    |    +--- org.apache.curator:curator-recipes:2.5.0 -> 4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.apache.curator:curator-client:2.5.0 -> 4.0.1 (*)
|    |    +--- org.apache.curator:curator-x-discovery:2.5.0 -> 4.0.1
|    |    |    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    |    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    +--- org.apache.zookeeper:zookeeper:3.4.6 -> 3.5.3-beta (*)
|    |    \--- com.integral:imtp:1.0.1
|    |         +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |         +--- org.jboss.netty:netty:3.2.4.Final
|    |         +--- com.sleepycat:je:5.0.34
|    |         +--- commons-lang:commons-lang:2.2 -> 2.6
|    |         +--- com.integral:serializer:1.0.1 -> 1.3.1 (*)
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.googlecode.disruptor:disruptor:2.8
|    +--- org.drools:drools-compiler:6.0.1.Final
|    +--- org.drools:drools-core:6.0.1.Final
|    +--- org.drools:kie-internal:6.0.1.Final
|    +--- org.drools:kie-api:6.0.1.Final
|    +--- com.ibm:com.ibm.mq:7.0
|    +--- com.ibm:com.ibm.mq.pcf:7.0
|    +--- com.ibm:com.ibm.mqjms:7.0
|    +--- com.ibm:com.ibm.mq.headers:7.0
|    +--- com.ibm:com.ibm.mq.jmqi:7.0
|    +--- com.amazonaws:aws-java-sdk:1.3.26
|    +--- com.google.http-client:google-http-client:1.20.0 -> 1.37.0
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 -> 4.5.10 (*)
|    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.12
|    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    +--- com.google.guava:guava:29.0-android -> 29.0-jre (*)
|    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    +--- io.opencensus:opencensus-api:0.24.0
|    |    |    \--- io.grpc:grpc-context:1.22.1 -> 1.33.0
|    |    \--- io.opencensus:opencensus-contrib-http-util:0.24.0
|    |         +--- io.opencensus:opencensus-api:0.24.0 (*)
|    |         \--- com.google.guava:guava:26.0-android -> 29.0-jre (*)
|    +--- org.jctools:jctools-core:1.1
|    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    +--- junit:junit:4.11 -> 4.12 (*)
|    +--- ch.qos.logback:logback-classic:1.0.6 -> 1.2.3 (*)
|    +--- ch.qos.logback:logback-core:1.0.6 -> 1.2.3
|    +--- ch.qos.logback:slf4j-api:1.6.1
|    +--- org.mongodb:mongo-java-driver:1.0.0
|    +--- mx4j:mx4j-tools:1.0.0
|    +--- cactus:cactus:1.5-rc1
|    +--- commons-jxpath:commons-jxpath:1.2
|    +--- commons-lang:commons-lang:2.2 -> 2.6
|    +--- commons-math:commons-math:1.1
|    +--- commons-codec:commons-codec:1.5 -> 1.13
|    +--- com.integral:TOPLink:1.0
|    +--- commons-httpclient:commons-httpclient:3.0-rc2
|    +--- org.apache.commons:commons-beanutils:1.6
|    +--- org.apache.commons:commons-collections:3.1
|    +--- commons-logging:commons-logging:1.0 -> 1.2
|    +--- com.integral:instantj:1.6
|    +--- log4j:log4j:1.2.14 -> 1.2.17
|    +--- commons-pool:commons-pool:1.5.5 -> 1.6
|    +--- castor:castor:0.9.3.9-xml
|    +--- com.ibm:ibm-bsf-regx:1.0
|    +--- com.integral:soap:1.0.0
|    +--- com.oracle:classes12:1.0.0
|    +--- exml:exml:7.0
|    +--- javax.mail:mail:1.0
|    +--- xmlunit:xmlunit:1.0
|    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    +--- com.thoughtworks.xstream:xstream:1.4.5
|    +--- com.sonicmq:sonic_client:7.6.2
|    +--- com.sonicmq:mgmt_client:7.6.2
|    +--- com.sonicmq:mgmt_config:7.6.2
|    +--- com.sonicmq:sonic_mgmt_client:7.6.2
|    +--- xerces:xercesImpl:1.0.0
|    +--- xerces:xml-apis:1.0.0
|    +--- xalan:xalan:1.0.0
|    +--- org.apache.tomcat:catalina:7.0.65
|    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10 (*)
|    +--- org.apache.httpcomponents:httpcore:4.1 -> 4.4.12
|    +--- org.apache.helix:helix-core:0.7.3
|    +--- com.101tec:zkclient:0.1
|    +--- com.rabbitmq:amqp-client:3.6.0
|    +--- quickfixj:quickfixj-all:1.4.0.1 -> 1.4.2
|    \--- org.codehaus.groovy:groovy-all:2.0.2
+--- com.rabbitmq:amqp-client:3.6.0
+--- org.mongodb:mongo-java-driver:1.0.0
+--- com.integral:monitor-core:3.0.3
|    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    \--- com.integral:util:1.1 -> 1.2 (*)
+--- org.json:json:20080701
+--- com.integral:rds:3.0-SNAPSHOT (*)
+--- com.integral:log:2.3-SNAPSHOT (*)
+--- com.integral:services:2.0 (*)
+--- com.integral:unity-server:2.4
|    +--- com.integral:spaces:1.0.1 -> 2.3 (*)
|    +--- com.corundumstudio.socketio:netty-socketio:1.7.11
|    |    +--- io.netty:netty-buffer:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-common:4.1.1.Final -> 4.1.43.Final
|    |    +--- io.netty:netty-transport:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-handler:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-codec-http:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-codec:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-transport-native-epoll:4.1.1.Final -> 4.1.43.Final
|    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    +--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    \--- io.netty:netty-transport-native-unix-common:4.1.43.Final
|    |    |         +--- io.netty:netty-common:4.1.43.Final
|    |    |         +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |         \--- io.netty:netty-transport:4.1.43.Final (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.29
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.7.4 -> 2.10.2
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.7.4 -> 2.10.2 (*)
|    +--- commons-codec:commons-codec:1.5 -> 1.13
|    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    +--- com.integral:util:1.0 -> 1.2 (*)
|    +--- com.integral:messaging:1.0.1 -> 1.1-SNAPSHOT (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
+--- com.integral:riskmanagement-common:2.5
|    +--- com.integral:darwin-common:2.5 -> 3.15.0
|    |    +--- org.springframework:spring-core:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-beans:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-context:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-webmvc:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- com.integral:util:1.2 (*)
|    |    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.2 -> 5.9 (*)
|    |    +--- com.integral:rds:2.1 -> 3.0-SNAPSHOT (*)
|    |    +--- commons-collections:commons-collections:3.2.2
|    |    +--- com.integral:oracle-persistence:1.2.83.2 -> 9.9.9-SNAPSHOT (*)
|    |    \--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    +--- com.integral:TOPLink:1.0
|    +--- com.integral:services:2.0 (*)
|    +--- quickfixj:quickfixj-all:1.4.0 -> 1.4.2
|    +--- com.integral:yieldmanager-api:2.6.2
|    |    +--- com.integral:darwin-common:2.5 -> 3.15.0 (*)
|    |    +--- com.integral:services:2.0 (*)
|    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    \--- com.integral:model:2.6 -> 5.9 (*)
|    \--- com.integral:multicast:1.0 -> 9.9.9-SNAPSHOT (*)
+--- com.integral:darwin-auth:3.15.0
|    +--- com.integral:darwin-common:3.15.0 (*)
|    +--- com.integral:darwin-taggable-entities:3.15.0 -> 3.13.2-jdk8
|    |    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:2.7 -> 5.9 (*)
|    |    +--- com.integral:darwin-common:3.13.2-jdk8 -> 3.15.0 (*)
|    |    +--- com.integral:rds:2.4 -> 3.0-SNAPSHOT (*)
|    |    +--- org.springframework:spring-core:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-beans:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-context:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-webmvc:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    \--- com.integral:oracle-persistence:1.2.83.2 -> 9.9.9-SNAPSHOT (*)
|    +--- com.integral:model:4.9 -> 5.9 (*)
|    +--- com.integral:rds:2.1 -> 3.0-SNAPSHOT (*)
|    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT (*)
|    +--- io.jsonwebtoken:jjwt:0.6.0 -> 0.9.1
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.6 -> 2.10.2 (*)
|    \--- com.integral:oracle-persistence:1.2.83.2 -> 9.9.9-SNAPSHOT (*)
+--- com.integral:model:5.9 (*)
+--- struts:struts:1.0.0
+--- commons-io:commons-io:1.1
+--- org.restlet.jee:org.restlet:1.0.0
+--- org.restlet.jee:org.restlet.ext.json:1.0.0
+--- com.fasterxml.jackson.core:jackson-databind:2.10.2 (*)
+--- com.fasterxml.jackson.core:jackson-core:2.10.2
+--- org.hibernate:hibernate-validator:5.3.1.Final
|    +--- javax.validation:validation-api:1.1.0.Final -> 2.0.1.Final
|    +--- org.jboss.logging:jboss-logging:3.3.0.Final -> 3.4.1.Final
|    \--- com.fasterxml:classmate:1.3.1 -> 1.5.1
+--- com.ning:async-http-client:1.6.2
+--- aopalliance:aopalliance:1.0
+--- aspectj:aspectjweaver:1.6.12
+--- commons-fileupload:commons-fileupload:1.1.1
+--- joda-time:joda-time:2.2
+--- org.owasp.esapi:esapi:2.0.1
+--- org.eclipse.jetty:jetty-server:9.4.28.v20200408
|    +--- javax.servlet:javax.servlet-api:3.1.0 -> 4.0.1
|    +--- org.eclipse.jetty:jetty-http:9.4.28.v20200408 -> 9.4.24.v20191120
|    |    +--- org.eclipse.jetty:jetty-util:9.4.24.v20191120
|    |    \--- org.eclipse.jetty:jetty-io:9.4.24.v20191120
|    |         \--- org.eclipse.jetty:jetty-util:9.4.24.v20191120
|    \--- org.eclipse.jetty:jetty-io:9.4.28.v20200408 -> 9.4.24.v20191120 (*)
+--- quickfixj:quickfixj-all:1.4.2
+--- org.apache.mina:mina-core:1.1.7
+--- org.apache.logging.log4j:log4j-api:2.16.0
+--- com.hazelcast:hazelcast:5.1.2
+--- org.mapstruct:mapstruct:1.4.1.Final
+--- org.mapstruct:mapstruct-jdk8:1.4.1.Final
+--- io.jsonwebtoken:jjwt:0.9.1 (*)
+--- org.springframework.boot:spring-boot-starter-web -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.2.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.12.1 -> 2.16.0
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.16.0
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.29
|    |    |         \--- org.slf4j:slf4j-api:1.7.29
|    |    +--- jakarta.annotation:jakarta.annotation-api:1.3.5
|    |    +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.25
|    +--- org.springframework.boot:spring-boot-starter-json:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.10.1
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.10.1
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.2.2.RELEASE
|    |    +--- jakarta.annotation:jakarta.annotation-api:1.3.5
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.29
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.29
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.29
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.29
|    +--- org.springframework.boot:spring-boot-starter-validation:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    +--- jakarta.validation:jakarta.validation-api:2.0.1
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.29
|    |    \--- org.hibernate.validator:hibernate-validator:6.0.18.Final
|    |         +--- org.jboss.logging:jboss-logging:3.3.2.Final -> 3.4.1.Final
|    |         \--- com.fasterxml:classmate:1.3.4 -> 1.5.1
|    +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-tomcat -> 2.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-websocket -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter-web:2.2.2.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.2.2.RELEASE
|    |    +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    \--- org.springframework:spring-websocket:5.2.2.RELEASE
|         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|         \--- org.springframework:spring-web:5.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    \--- io.micrometer:micrometer-core:1.3.1 (*)
+--- io.micrometer:micrometer-core -> 1.3.1 (*)
+--- io.micrometer:micrometer-registry-prometheus -> 1.3.1 (*)
+--- org.springframework.cloud:spring-cloud-starter-consul-discovery -> 2.2.6.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-consul:2.2.6.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:2.2.7.RELEASE
|    |    |    |    \--- org.springframework.security:spring-security-crypto:5.3.6.RELEASE -> 5.2.1.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:2.2.7.RELEASE
|    |    |    |    \--- org.springframework.security:spring-security-crypto:5.3.6.RELEASE -> 5.2.1.RELEASE
|    |    |    \--- org.springframework.security:spring-security-rsa:1.0.9.RELEASE
|    |    |         \--- org.bouncycastle:bcpkix-jdk15on:1.64
|    |    |              \--- org.bouncycastle:bcprov-jdk15on:1.64
|    |    +--- org.springframework.cloud:spring-cloud-consul-core:2.2.6.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot-starter-validation:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    +--- com.ecwid.consul:consul-api:1.4.5
|    |    |    +--- com.google.code.gson:gson:2.8.2 -> 2.8.6
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.9 -> 4.4.12
|    |    |    \--- org.apache.httpcomponents:httpclient:4.5.5 -> 4.5.10 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 -> 4.5.10 (*)
|    |    \--- org.apache.httpcomponents:httpcore:4.4.14 -> 4.4.12
|    +--- org.springframework.cloud:spring-cloud-consul-discovery:2.2.6.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-consul-core:2.2.6.RELEASE (*)
|    |    \--- commons-configuration:commons-configuration:1.8
|    |         +--- commons-lang:commons-lang:2.6
|    |         \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    +--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.2.7.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-starter-aop:2.3.8.RELEASE -> 2.2.2.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|    |         \--- org.aspectj:aspectjweaver:1.9.5
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.7.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.2.7.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.2.7.RELEASE
|    |    |    +--- com.netflix.archaius:archaius-core:0.7.7
|    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|    |    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.29
|    |    |    |    +--- com.google.guava:guava:16.0 -> 29.0-jre (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.10.1
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    |    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
|    |    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    |    +--- com.netflix.ribbon:ribbon:2.3.0
|    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|    |    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.29
|    |    |    |    +--- com.google.guava:guava:16.0 -> 29.0-jre (*)
|    |    |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|    |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|    |    |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|    |    |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|    |    |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.29
|    |    |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.3.8
|    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.29
|    |    |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    |    |    \--- com.google.guava:guava:19.0 -> 29.0-jre (*)
|    |    |    |    |    +--- com.google.guava:guava:16.0.1 -> 29.0-jre (*)
|    |    |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|    |    |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.29
|    |    |    |    |         \--- javax.inject:javax.inject:1
|    |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    |    +--- io.reactivex:rxnetty:0.4.9
|    |    |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.29
|    |    |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|    |    |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|    |    |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|    |    |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|    |    |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|    |    |    |    +--- javax.inject:javax.inject:1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.29
|    |    |    |    +--- com.google.guava:guava:16.0.1 -> 29.0-jre (*)
|    |    |    |    \--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.29
|    |    |    |    +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.7 (*)
|    |    |    |    +--- io.reactivex:rxjava:1.2.0 -> 1.3.8
|    |    |    |    \--- org.hdrhistogram:HdrHistogram:2.1.9 -> 2.1.11
|    |    |    +--- javax.inject:javax.inject:1
|    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|    |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|    |    |    +--- com.google.guava:guava:16.0.1 -> 29.0-jre (*)
|    |    |    \--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|    |    +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|    |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|    |    |    +--- commons-collections:commons-collections:3.2.2
|    |    |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.10 (*)
|    |    |    +--- com.sun.jersey:jersey-client:1.19.1
|    |    |    |    \--- com.sun.jersey:jersey-core:1.19.1
|    |    |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|    |    |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|    |    |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10 (*)
|    |    |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.29
|    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|    |    |    +--- com.google.guava:guava:16.0.1 -> 29.0-jre (*)
|    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|    |    \--- io.reactivex:rxjava:1.3.8
|    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-loadbalancer:2.2.7.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-validation:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    |    +--- io.projectreactor:reactor-core:3.3.13.RELEASE -> 3.3.1.RELEASE
|    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.3
|    |    |    \--- io.projectreactor.addons:reactor-extra:3.3.5.RELEASE -> 3.3.1.RELEASE
|    |    |         \--- io.projectreactor:reactor-core:3.3.1.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-cache:2.3.8.RELEASE -> 2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    |    \--- org.springframework:spring-context-support:5.2.2.RELEASE
|    |    |         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |    |         \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    \--- com.stoyanr:evictor:1.0.0
|    \--- joda-time:joda-time:2.10.5 -> 2.2
+--- org.springframework.boot:spring-boot-starter-hateoas -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter-web:2.2.2.RELEASE (*)
|    \--- org.springframework.hateoas:spring-hateoas:1.0.2.RELEASE
|         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|         +--- org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE
|         |    +--- org.springframework:spring-beans:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    +--- org.springframework:spring-context:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    +--- org.springframework:spring-aop:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|         +--- com.jayway.jsonpath:json-path:2.4.0
|         |    +--- net.minidev:json-smart:2.3
|         |    |    \--- net.minidev:accessors-smart:1.2
|         |    |         \--- org.ow2.asm:asm:5.0.4
|         |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.29
+--- org.springframework.boot:spring-boot-starter-validation -> 2.2.2.RELEASE (*)
+--- com.integral:portal-service-apps:3.13.2-jdk8
|    +--- com.integral:darwin-common:3.13.2-jdk8 -> 3.15.0 (*)
|    +--- com.integral:darwin-taggable-entities:3.13.2-jdk8 (*)
|    +--- com.integral:model:5.9 (*)
|    +--- com.integral:rds:2.1 -> 3.0-SNAPSHOT (*)
|    +--- com.integral:oracle-persistence:1.2.83.2 -> 9.9.9-SNAPSHOT (*)
|    +--- org.springframework:spring-core:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- org.springframework:spring-context:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- org.springframework:spring-web:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- org.springframework:spring-webmvc:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- org.springframework:spring-beans:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    +--- org.apache.commons:commons-csv:1.4
|    \--- org.apache.httpcomponents:httpclient:4.4.1 -> 4.5.10 (*)
+--- com.integral:price-making-commons:9.9.9-jdk8-SNAPSHOT
|    \--- com.integral:oracle-persistence:9.9.9-SNAPSHOT (*)
+--- com.integral:darwin-taggable-entities:3.13.2-jdk8 (*)
+--- com.google.code.gson:gson:2.8.6
+--- org.apache.logging.log4j:log4j-to-slf4j:2.16.0 (*)
\--- net.sf.kxml:kxml2:2.3.0

runtimeElements - Elements of runtime for main. (n)
No dependencies

runtimeOnly - Runtime only dependencies for source set 'main'. (n)
No dependencies

testAnnotationProcessor - Annotation processors and their dependencies for source set 'test'.
No dependencies

testCompileClasspath - Compile classpath for source set 'test'.
+--- org.springdoc:springdoc-openapi-ui:1.5.2
|    +--- org.springdoc:springdoc-openapi-webmvc-core:1.5.2
|    |    +--- org.springdoc:springdoc-openapi-common:1.5.2
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.0 -> 2.2.2.RELEASE
|    |    |    |    \--- org.springframework.boot:spring-boot:2.2.2.RELEASE
|    |    |    |         +--- org.springframework:spring-core:5.2.2.RELEASE
|    |    |    |         |    \--- org.springframework:spring-jcl:5.2.2.RELEASE
|    |    |    |         \--- org.springframework:spring-context:5.2.2.RELEASE
|    |    |    |              +--- org.springframework:spring-aop:5.2.2.RELEASE
|    |    |    |              |    +--- org.springframework:spring-beans:5.2.2.RELEASE
|    |    |    |              |    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |    |              +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              \--- org.springframework:spring-expression:5.2.2.RELEASE
|    |    |    |                   \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-web:5.3.1 -> 5.2.2.RELEASE
|    |    |    |    +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    +--- io.swagger.core.v3:swagger-models:2.1.6
|    |    |    |    \--- com.fasterxml.jackson.core:jackson-annotations:2.11.1 -> 2.10.1
|    |    |    +--- io.swagger.core.v3:swagger-annotations:2.1.6
|    |    |    +--- io.swagger.core.v3:swagger-integration:2.1.6
|    |    |    |    +--- io.swagger.core.v3:swagger-core:2.1.6
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.7 -> 3.9
|    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.11.1 -> 2.10.1
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.1 -> 2.10.2
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.10.2 -> 2.10.1
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.10.2
|    |    |    |    |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.11.1 -> 2.10.1
|    |    |    |    |    |    +--- org.yaml:snakeyaml:1.24 -> 1.25
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.1 -> 2.10.1
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.10.1
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    |    |    |    +--- io.swagger.core.v3:swagger-annotations:2.1.6
|    |    |    |    |    +--- io.swagger.core.v3:swagger-models:2.1.6 (*)
|    |    |    |    |    \--- jakarta.validation:jakarta.validation-api:2.0.2 -> 2.0.1
|    |    |    |    \--- io.swagger.core.v3:swagger-models:2.1.6 (*)
|    |    |    +--- io.github.classgraph:classgraph:4.8.69
|    |    |    \--- org.apache.commons:commons-lang3:3.11 -> 3.9
|    |    \--- org.springframework:spring-webmvc:5.3.1 -> 5.2.2.RELEASE
|    |         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-expression:5.2.2.RELEASE (*)
|    |         \--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    +--- org.webjars:swagger-ui:3.38.0
|    \--- org.webjars:webjars-locator-core:0.45 -> 0.41
|         +--- org.slf4j:slf4j-api:1.7.7 -> 1.7.29
|         +--- com.fasterxml.jackson.core:jackson-core:2.9.8 -> 2.10.2
|         \--- org.webjars.npm:angular__http:2.4.10
+--- com.integral:oracle-persistence:9.9.9-SNAPSHOT
|    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT
|    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.29
|    |    \--- com.googlecode.disruptor:disruptor:2.8
|    +--- com.integral:util:1.2
|    |    +--- org.jctools:jctools-core:1.1
|    |    \--- com.integral:log:1.1 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:spaces:2.3
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.0.1 -> 5.9
|    |    |    +--- com.integral:serializer:1.3 -> 1.3.1
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.10.1
|    |    |    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    |    |    +--- de.undercouch:bson4jackson:2.4.0
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    |    |    |    +--- org.apache.maven:maven-artifact:3.0.3
|    |    |    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
|    |    |    |    \--- org.javassist:javassist:3.13.0-GA
|    |    |    +--- junit:junit:4.11 -> 4.12
|    |    |    |    \--- org.hamcrest:hamcrest-core:1.3 -> 2.1
|    |    |    |         \--- org.hamcrest:hamcrest:2.1
|    |    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    |    \--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- org.mongodb:mongo-java-driver:1.0.0
|    |    +--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- com.rabbitmq:amqp-client:3.6.0
|    |    +--- com.integral:serializer:1.2 -> 1.3.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- com.googlecode.disruptor:disruptor:2.8
|    |    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    |    \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.integral:serializer:1.3.1 (*)
|    +--- com.integral:messaging:1.0.5 -> 1.1-SNAPSHOT
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.rabbitmq:amqp-client:3.6.0
|    |    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- com.integral:serializer:1.0.1 -> 1.3.1 (*)
|    |    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    |    \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.integral:model:5.7 -> 5.9 (*)
|    +--- com.integral:clustering:1.0.1
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    |    +--- com.google.guava:guava:15.0 -> 29.0-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.11.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.4
|    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    +--- com.101tec:zkclient:0.1
|    |    +--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    +--- org.apache.helix:helix-core:0.7.3
|    |    \--- org.apache.zookeeper:zookeeper:3.4.6 -> 3.5.3-beta
|    |         +--- org.slf4j:slf4j-api:1.7.5 -> 1.7.29
|    |         +--- org.slf4j:slf4j-log4j12:1.7.5 -> 1.7.29
|    |         +--- commons-cli:commons-cli:1.2
|    |         +--- log4j:log4j:1.2.17
|    |         \--- io.netty:netty:3.10.5.Final
|    +--- com.integral:multicast:9.9.9-SNAPSHOT
|    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.12
|    |    |    +--- commons-logging:commons-logging:1.2
|    |    |    \--- commons-codec:commons-codec:1.11 -> 1.13
|    |    +--- log4j:log4j:1.2.14 -> 1.2.17
|    |    +--- org.apache.httpcomponents:httpcore:4.1 -> 4.4.12
|    |    +--- com.integral:util:1.1 -> 1.2 (*)
|    |    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- junit:junit:4.11 -> 4.12 (*)
|    |    +--- com.integral:rds:2.3 -> 3.0-SNAPSHOT
|    |    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    |    +--- com.integral:model:4.2 -> 5.9 (*)
|    |    |    +--- com.integral:spaces:2.0 -> 2.3 (*)
|    |    |    +--- com.integral:messaging:1.1-SNAPSHOT (*)
|    |    |    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    |    |    +--- io.netty:netty-common:4.1.1.Final -> 4.1.43.Final
|    |    |    +--- io.netty:netty-buffer:4.1.1.Final -> 4.1.43.Final
|    |    |    |    \--- io.netty:netty-common:4.1.43.Final
|    |    |    +--- io.netty:netty-handler:4.1.1.Final -> 4.1.43.Final
|    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.43.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    |    \--- io.netty:netty-resolver:4.1.43.Final
|    |    |    |    |         \--- io.netty:netty-common:4.1.43.Final
|    |    |    |    \--- io.netty:netty-codec:4.1.43.Final
|    |    |    |         +--- io.netty:netty-common:4.1.43.Final
|    |    |    |         +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |         \--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    +--- io.netty:netty-codec-http:4.1.1.Final -> 4.1.43.Final
|    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-codec:4.1.43.Final (*)
|    |    |    |    \--- io.netty:netty-handler:4.1.43.Final (*)
|    |    |    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    |    |    +--- commons-codec:commons-codec:1.5 -> 1.13
|    |    |    +--- org.eclipse.jetty.aggregate:jetty-all-server:8.1.15.v20140411
|    |    |    \--- io.micrometer:micrometer-registry-prometheus:1.5.2 -> 1.3.1
|    |    |         +--- io.micrometer:micrometer-core:1.3.1
|    |    |         |    +--- org.hdrhistogram:HdrHistogram:2.1.11
|    |    |         |    \--- org.latencyutils:LatencyUtils:2.0.3
|    |    |         \--- io.prometheus:simpleclient_common:0.7.0
|    |    |              \--- io.prometheus:simpleclient:0.7.0
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    \--- com.integral:model:2.5 -> 5.9 (*)
|    +--- com.integral:rds:2.3 -> 3.0-SNAPSHOT (*)
|    +--- com.integral:services:2.0
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- org.apache.curator:curator-framework:2.5.0 -> 4.0.1
|    |    |    \--- org.apache.curator:curator-client:4.0.1
|    |    |         +--- org.apache.zookeeper:zookeeper:3.5.3-beta (*)
|    |    |         +--- com.google.guava:guava:20.0 -> 29.0-jre (*)
|    |    |         \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.29
|    |    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    |    +--- org.apache.curator:curator-recipes:2.5.0 -> 4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.apache.curator:curator-client:2.5.0 -> 4.0.1 (*)
|    |    +--- org.apache.curator:curator-x-discovery:2.5.0 -> 4.0.1
|    |    |    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    |    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    +--- org.apache.zookeeper:zookeeper:3.4.6 -> 3.5.3-beta (*)
|    |    \--- com.integral:imtp:1.0.1
|    |         +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |         +--- org.jboss.netty:netty:3.2.4.Final
|    |         +--- com.sleepycat:je:5.0.34
|    |         +--- commons-lang:commons-lang:2.2 -> 2.6
|    |         +--- com.integral:serializer:1.0.1 -> 1.3.1 (*)
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.googlecode.disruptor:disruptor:2.8
|    +--- org.drools:drools-compiler:6.0.1.Final
|    +--- org.drools:drools-core:6.0.1.Final
|    +--- org.drools:kie-internal:6.0.1.Final
|    +--- org.drools:kie-api:6.0.1.Final
|    +--- com.ibm:com.ibm.mq:7.0
|    +--- com.ibm:com.ibm.mq.pcf:7.0
|    +--- com.ibm:com.ibm.mqjms:7.0
|    +--- com.ibm:com.ibm.mq.headers:7.0
|    +--- com.ibm:com.ibm.mq.jmqi:7.0
|    +--- com.amazonaws:aws-java-sdk:1.3.26
|    +--- com.google.http-client:google-http-client:1.20.0 -> 1.37.0
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 -> 4.5.10 (*)
|    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.12
|    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    +--- com.google.guava:guava:29.0-android -> 29.0-jre (*)
|    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    +--- io.opencensus:opencensus-api:0.24.0
|    |    |    \--- io.grpc:grpc-context:1.22.1 -> 1.33.0
|    |    \--- io.opencensus:opencensus-contrib-http-util:0.24.0
|    |         +--- io.opencensus:opencensus-api:0.24.0 (*)
|    |         \--- com.google.guava:guava:26.0-android -> 29.0-jre (*)
|    +--- org.jctools:jctools-core:1.1
|    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    +--- junit:junit:4.11 -> 4.12 (*)
|    +--- ch.qos.logback:logback-classic:1.0.6 -> 1.2.3 (*)
|    +--- ch.qos.logback:logback-core:1.0.6 -> 1.2.3
|    +--- ch.qos.logback:slf4j-api:1.6.1
|    +--- org.mongodb:mongo-java-driver:1.0.0
|    +--- mx4j:mx4j-tools:1.0.0
|    +--- cactus:cactus:1.5-rc1
|    +--- commons-jxpath:commons-jxpath:1.2
|    +--- commons-lang:commons-lang:2.2 -> 2.6
|    +--- commons-math:commons-math:1.1
|    +--- commons-codec:commons-codec:1.5 -> 1.13
|    +--- com.integral:TOPLink:1.0
|    +--- commons-httpclient:commons-httpclient:3.0-rc2
|    +--- org.apache.commons:commons-beanutils:1.6
|    +--- org.apache.commons:commons-collections:3.1
|    +--- commons-logging:commons-logging:1.0 -> 1.2
|    +--- com.integral:instantj:1.6
|    +--- log4j:log4j:1.2.14 -> 1.2.17
|    +--- commons-pool:commons-pool:1.5.5 -> 1.6
|    +--- castor:castor:0.9.3.9-xml
|    +--- com.ibm:ibm-bsf-regx:1.0
|    +--- com.integral:soap:1.0.0
|    +--- com.oracle:classes12:1.0.0
|    +--- exml:exml:7.0
|    +--- javax.mail:mail:1.0
|    +--- xmlunit:xmlunit:1.0
|    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    +--- com.thoughtworks.xstream:xstream:1.4.5
|    +--- com.sonicmq:sonic_client:7.6.2
|    +--- com.sonicmq:mgmt_client:7.6.2
|    +--- com.sonicmq:mgmt_config:7.6.2
|    +--- com.sonicmq:sonic_mgmt_client:7.6.2
|    +--- xerces:xercesImpl:1.0.0
|    +--- xerces:xml-apis:1.0.0
|    +--- xalan:xalan:1.0.0
|    +--- org.apache.tomcat:catalina:7.0.65
|    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10 (*)
|    +--- org.apache.httpcomponents:httpcore:4.1 -> 4.4.12
|    +--- org.apache.helix:helix-core:0.7.3
|    +--- com.101tec:zkclient:0.1
|    +--- com.rabbitmq:amqp-client:3.6.0
|    +--- quickfixj:quickfixj-all:1.4.0.1 -> 1.4.2
|    \--- org.codehaus.groovy:groovy-all:2.0.2
+--- com.rabbitmq:amqp-client:3.6.0
+--- org.mongodb:mongo-java-driver:1.0.0
+--- com.integral:monitor-core:3.0.3
|    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    \--- com.integral:util:1.1 -> 1.2 (*)
+--- org.json:json:20080701
+--- com.integral:rds:3.0-SNAPSHOT (*)
+--- com.integral:log:2.3-SNAPSHOT (*)
+--- com.integral:services:2.0 (*)
+--- com.integral:unity-server:2.4
|    +--- com.integral:spaces:1.0.1 -> 2.3 (*)
|    +--- com.corundumstudio.socketio:netty-socketio:1.7.11
|    |    +--- io.netty:netty-buffer:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-common:4.1.1.Final -> 4.1.43.Final
|    |    +--- io.netty:netty-transport:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-handler:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-codec-http:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-codec:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-transport-native-epoll:4.1.1.Final -> 4.1.43.Final
|    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    +--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    \--- io.netty:netty-transport-native-unix-common:4.1.43.Final
|    |    |         +--- io.netty:netty-common:4.1.43.Final
|    |    |         +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |         \--- io.netty:netty-transport:4.1.43.Final (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.29
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.7.4 -> 2.10.2
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.7.4 -> 2.10.2 (*)
|    +--- commons-codec:commons-codec:1.5 -> 1.13
|    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    +--- com.integral:util:1.0 -> 1.2 (*)
|    +--- com.integral:messaging:1.0.1 -> 1.1-SNAPSHOT (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
+--- com.integral:riskmanagement-common:2.5
|    +--- com.integral:darwin-common:2.5
|    |    +--- commons-collections:commons-collections:3.2.2
|    |    +--- org.springframework:spring-beans:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- com.integral:util:1.2 (*)
|    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    +--- org.springframework:spring-core:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- com.integral:model:1.2 -> 5.9 (*)
|    |    +--- com.integral:rds:2.1 -> 3.0-SNAPSHOT (*)
|    |    +--- com.integral:log:1.0 -> 2.3-SNAPSHOT (*)
|    |    +--- org.springframework:spring-context:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-test-autoconfigure:1.4.4.RELEASE -> 2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-test:2.2.2.RELEASE
|    |    |    |    \--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-webmvc:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot:1.4.4.RELEASE -> 2.2.2.RELEASE (*)
|    |    \--- com.integral:oracle-persistence:1.0 -> 9.9.9-SNAPSHOT (*)
|    +--- com.integral:TOPLink:1.0
|    +--- com.integral:services:2.0 (*)
|    +--- quickfixj:quickfixj-all:1.4.0 -> 1.4.2
|    +--- com.integral:yieldmanager-api:2.6.2
|    |    +--- com.integral:darwin-common:2.5 (*)
|    |    +--- com.integral:services:2.0 (*)
|    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    \--- com.integral:model:2.6 -> 5.9 (*)
|    \--- com.integral:multicast:1.0 -> 9.9.9-SNAPSHOT (*)
+--- com.integral:darwin-auth:3.15.0
+--- com.integral:model:5.9 (*)
+--- struts:struts:1.0.0
+--- commons-io:commons-io:1.1
+--- org.restlet.jee:org.restlet:1.0.0
+--- org.restlet.jee:org.restlet.ext.json:1.0.0
+--- com.fasterxml.jackson.core:jackson-databind:2.10.2 (*)
+--- com.fasterxml.jackson.core:jackson-core:2.10.2
+--- org.hibernate:hibernate-validator:5.3.1.Final
|    +--- javax.validation:validation-api:1.1.0.Final -> 2.0.1.Final
|    +--- org.jboss.logging:jboss-logging:3.3.0.Final -> 3.4.1.Final
|    \--- com.fasterxml:classmate:1.3.1 -> 1.5.1
+--- com.ning:async-http-client:1.6.2
+--- aopalliance:aopalliance:1.0
+--- aspectj:aspectjweaver:1.6.12
+--- commons-fileupload:commons-fileupload:1.1.1
+--- joda-time:joda-time:2.2
+--- org.owasp.esapi:esapi:2.0.1
+--- org.eclipse.jetty:jetty-server:9.4.28.v20200408
|    +--- javax.servlet:javax.servlet-api:3.1.0 -> 4.0.1
|    +--- org.eclipse.jetty:jetty-http:9.4.28.v20200408 -> 9.4.24.v20191120
|    |    +--- org.eclipse.jetty:jetty-util:9.4.24.v20191120
|    |    \--- org.eclipse.jetty:jetty-io:9.4.24.v20191120
|    |         \--- org.eclipse.jetty:jetty-util:9.4.24.v20191120
|    \--- org.eclipse.jetty:jetty-io:9.4.28.v20200408 -> 9.4.24.v20191120 (*)
+--- quickfixj:quickfixj-all:1.4.2
+--- org.apache.mina:mina-core:1.1.7
+--- org.apache.logging.log4j:log4j-api:2.16.0
+--- com.hazelcast:hazelcast:5.1.2
+--- org.mapstruct:mapstruct:1.4.1.Final
+--- org.mapstruct:mapstruct-jdk8:1.4.1.Final
+--- io.jsonwebtoken:jjwt:0.9.1
|    \--- com.fasterxml.jackson.core:jackson-databind:2.9.6 -> 2.10.2 (*)
+--- org.springframework.boot:spring-boot-starter-web -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.2.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.12.1 -> 2.16.0
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.16.0
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.29
|    |    |         \--- org.slf4j:slf4j-api:1.7.29
|    |    +--- jakarta.annotation:jakarta.annotation-api:1.3.5
|    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-starter-json:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.10.1
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.10.1
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.2.2.RELEASE
|    |    +--- jakarta.annotation:jakarta.annotation-api:1.3.5
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.29
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.29
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.29
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.29
|    +--- org.springframework.boot:spring-boot-starter-validation:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    +--- jakarta.validation:jakarta.validation-api:2.0.1
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.29
|    |    \--- org.hibernate.validator:hibernate-validator:6.0.18.Final
|    |         +--- org.jboss.logging:jboss-logging:3.3.2.Final -> 3.4.1.Final
|    |         \--- com.fasterxml:classmate:1.3.4 -> 1.5.1
|    +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-tomcat -> 2.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-websocket -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter-web:2.2.2.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.2.2.RELEASE
|    |    +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    \--- org.springframework:spring-websocket:5.2.2.RELEASE
|         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|         \--- org.springframework:spring-web:5.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    \--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    \--- io.micrometer:micrometer-core:1.3.1 (*)
+--- io.micrometer:micrometer-core -> 1.3.1 (*)
+--- io.micrometer:micrometer-registry-prometheus -> 1.3.1 (*)
+--- org.springframework.cloud:spring-cloud-starter-consul-discovery -> 2.2.6.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-consul:2.2.6.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:2.2.7.RELEASE
|    |    |    |    \--- org.springframework.security:spring-security-crypto:5.3.6.RELEASE -> 5.2.1.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:2.2.7.RELEASE
|    |    |    |    \--- org.springframework.security:spring-security-crypto:5.3.6.RELEASE -> 5.2.1.RELEASE
|    |    |    \--- org.springframework.security:spring-security-rsa:1.0.9.RELEASE
|    |    |         \--- org.bouncycastle:bcpkix-jdk15on:1.64
|    |    |              \--- org.bouncycastle:bcprov-jdk15on:1.64
|    |    +--- org.springframework.cloud:spring-cloud-consul-core:2.2.6.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot-starter-validation:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    +--- com.ecwid.consul:consul-api:1.4.5
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 -> 4.5.10 (*)
|    |    \--- org.apache.httpcomponents:httpcore:4.4.14 -> 4.4.12
|    +--- org.springframework.cloud:spring-cloud-consul-discovery:2.2.6.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-consul-core:2.2.6.RELEASE (*)
|    |    \--- commons-configuration:commons-configuration:1.8
|    |         \--- commons-lang:commons-lang:2.6
|    +--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.2.7.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-starter-aop:2.3.8.RELEASE -> 2.2.2.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|    |         \--- org.aspectj:aspectjweaver:1.9.5
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.7.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.2.7.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.2.7.RELEASE
|    |    |    +--- com.netflix.archaius:archaius-core:0.7.7
|    |    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    |    +--- com.netflix.ribbon:ribbon:2.3.0
|    |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|    |    +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|    |    \--- io.reactivex:rxjava:1.3.8
|    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-loadbalancer:2.2.7.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-validation:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    |    +--- io.projectreactor:reactor-core:3.3.13.RELEASE -> 3.3.1.RELEASE
|    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.3
|    |    |    \--- io.projectreactor.addons:reactor-extra:3.3.5.RELEASE -> 3.3.1.RELEASE
|    |    |         \--- io.projectreactor:reactor-core:3.3.1.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-cache:2.3.8.RELEASE -> 2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    |    \--- org.springframework:spring-context-support:5.2.2.RELEASE
|    |    |         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |    |         \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    \--- com.stoyanr:evictor:1.0.0
|    \--- joda-time:joda-time:2.10.5 -> 2.2
+--- org.springframework.boot:spring-boot-starter-hateoas -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter-web:2.2.2.RELEASE (*)
|    \--- org.springframework.hateoas:spring-hateoas:1.0.2.RELEASE
|         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|         +--- org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE
|         |    +--- org.springframework:spring-beans:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    +--- org.springframework:spring-context:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    +--- org.springframework:spring-aop:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|         +--- com.jayway.jsonpath:json-path:2.4.0
|         |    +--- net.minidev:json-smart:2.3
|         |    |    \--- net.minidev:accessors-smart:1.2
|         |    |         \--- org.ow2.asm:asm:5.0.4
|         |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.29
+--- org.springframework.boot:spring-boot-starter-validation -> 2.2.2.RELEASE (*)
+--- com.integral:portal-service-apps:3.13.2-jdk8
+--- com.integral:price-making-commons:9.9.9-jdk8-SNAPSHOT
+--- com.integral:darwin-taggable-entities:3.13.2-jdk8
+--- com.google.code.gson:gson:2.8.6
+--- org.apache.logging.log4j:log4j-to-slf4j:2.16.0 (*)
+--- org.springframework.boot:spring-boot-starter-test -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test:2.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:2.2.2.RELEASE (*)
|    +--- com.jayway.jsonpath:json-path:2.4.0 (*)
|    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    +--- org.junit.jupiter:junit-jupiter:5.5.2
|    |    +--- org.junit.jupiter:junit-jupiter-api:5.5.2
|    |    |    +--- org.apiguardian:apiguardian-api:1.1.0
|    |    |    +--- org.opentest4j:opentest4j:1.2.0
|    |    |    \--- org.junit.platform:junit-platform-commons:1.5.2
|    |    |         \--- org.apiguardian:apiguardian-api:1.1.0
|    |    \--- org.junit.jupiter:junit-jupiter-params:5.5.2
|    |         +--- org.apiguardian:apiguardian-api:1.1.0
|    |         \--- org.junit.jupiter:junit-jupiter-api:5.5.2 (*)
|    +--- org.mockito:mockito-junit-jupiter:3.1.0
|    |    \--- org.mockito:mockito-core:3.1.0
|    |         +--- net.bytebuddy:byte-buddy:1.9.10 -> 1.10.4
|    |         +--- net.bytebuddy:byte-buddy-agent:1.9.10 -> 1.10.4
|    |         \--- org.objenesis:objenesis:2.6
|    +--- org.assertj:assertj-core:3.13.2
|    +--- org.hamcrest:hamcrest:2.1
|    +--- org.mockito:mockito-core:3.1.0 (*)
|    +--- org.skyscreamer:jsonassert:1.5.0
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    +--- org.springframework:spring-test:5.2.2.RELEASE
|    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    \--- org.xmlunit:xmlunit-core:2.6.3
\--- com.squareup.okhttp3:okhttp:4.10.0
     +--- com.squareup.okio:okio:3.0.0
     |    \--- com.squareup.okio:okio-jvm:3.0.0
     |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.31 -> 1.3.61
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.61
     |         |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.3.61
     |         |    |    \--- org.jetbrains:annotations:13.0
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.61
     |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.61 (*)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.5.31 -> 1.3.61
     \--- org.jetbrains.kotlin:kotlin-stdlib:1.6.20 -> 1.3.61 (*)

testCompileOnly - Compile only dependencies for source set 'test'. (n)
No dependencies

testImplementation - Implementation only dependencies for source set 'test'. (n)
+--- org.springframework.boot:spring-boot-starter-test (n)
\--- com.squareup.okhttp3:okhttp:4.10.0 (n)

testRuntimeClasspath - Runtime classpath of source set 'test'.
+--- org.springdoc:springdoc-openapi-ui:1.5.2
|    +--- org.springdoc:springdoc-openapi-webmvc-core:1.5.2
|    |    +--- org.springdoc:springdoc-openapi-common:1.5.2
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.4.0 -> 2.2.2.RELEASE
|    |    |    |    \--- org.springframework.boot:spring-boot:2.2.2.RELEASE
|    |    |    |         +--- org.springframework:spring-core:5.2.2.RELEASE
|    |    |    |         |    \--- org.springframework:spring-jcl:5.2.2.RELEASE
|    |    |    |         \--- org.springframework:spring-context:5.2.2.RELEASE
|    |    |    |              +--- org.springframework:spring-aop:5.2.2.RELEASE
|    |    |    |              |    +--- org.springframework:spring-beans:5.2.2.RELEASE
|    |    |    |              |    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |    |              +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    |              \--- org.springframework:spring-expression:5.2.2.RELEASE
|    |    |    |                   \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    +--- org.springframework:spring-web:5.3.1 -> 5.2.2.RELEASE
|    |    |    |    +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    |    +--- io.swagger.core.v3:swagger-models:2.1.6
|    |    |    |    \--- com.fasterxml.jackson.core:jackson-annotations:2.11.1 -> 2.10.1
|    |    |    +--- io.swagger.core.v3:swagger-annotations:2.1.6
|    |    |    +--- io.swagger.core.v3:swagger-integration:2.1.6
|    |    |    |    +--- io.swagger.core.v3:swagger-core:2.1.6
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.7 -> 3.9
|    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.11.1 -> 2.10.1
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.1 -> 2.10.2
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.10.2 -> 2.10.1
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.10.2
|    |    |    |    |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.11.1 -> 2.10.1
|    |    |    |    |    |    +--- org.yaml:snakeyaml:1.24 -> 1.25
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.1 -> 2.10.1
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.10.1
|    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    |    |    |    +--- io.swagger.core.v3:swagger-annotations:2.1.6
|    |    |    |    |    +--- io.swagger.core.v3:swagger-models:2.1.6 (*)
|    |    |    |    |    \--- jakarta.validation:jakarta.validation-api:2.0.2 -> 2.0.1
|    |    |    |    \--- io.swagger.core.v3:swagger-models:2.1.6 (*)
|    |    |    +--- io.github.classgraph:classgraph:4.8.69
|    |    |    \--- org.apache.commons:commons-lang3:3.11 -> 3.9
|    |    \--- org.springframework:spring-webmvc:5.3.1 -> 5.2.2.RELEASE
|    |         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-expression:5.2.2.RELEASE (*)
|    |         \--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    +--- org.webjars:swagger-ui:3.38.0
|    \--- org.webjars:webjars-locator-core:0.45 -> 0.41
|         +--- org.slf4j:slf4j-api:1.7.7 -> 1.7.29
|         +--- com.fasterxml.jackson.core:jackson-core:2.9.8 -> 2.10.2
|         \--- org.webjars.npm:angular__http:2.4.10
+--- com.integral:oracle-persistence:9.9.9-SNAPSHOT
|    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT
|    |    +--- ch.qos.logback:logback-classic:1.2.3
|    |    |    +--- ch.qos.logback:logback-core:1.2.3
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    +--- org.slf4j:slf4j-api:1.7.30 -> 1.7.29
|    |    \--- com.googlecode.disruptor:disruptor:2.8
|    +--- com.integral:util:1.2
|    |    +--- org.jctools:jctools-core:1.1
|    |    \--- com.integral:log:1.1 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:spaces:2.3
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.0.1 -> 5.9
|    |    |    +--- com.integral:serializer:1.3 -> 1.3.1
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.10.1
|    |    |    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    |    |    +--- de.undercouch:bson4jackson:2.4.0
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    |    |    |    +--- org.apache.maven:maven-artifact:3.0.3
|    |    |    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
|    |    |    |    \--- org.javassist:javassist:3.13.0-GA
|    |    |    +--- junit:junit:4.11 -> 4.12
|    |    |    |    \--- org.hamcrest:hamcrest-core:1.3 -> 2.1
|    |    |    |         \--- org.hamcrest:hamcrest:2.1
|    |    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    |    \--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- org.mongodb:mongo-java-driver:1.0.0
|    |    +--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- com.rabbitmq:amqp-client:3.6.0
|    |    +--- com.integral:serializer:1.2 -> 1.3.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- com.googlecode.disruptor:disruptor:2.8
|    |    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    |    \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.integral:serializer:1.3.1 (*)
|    +--- com.integral:messaging:1.0.5 -> 1.1-SNAPSHOT
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.rabbitmq:amqp-client:3.6.0
|    |    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- com.integral:serializer:1.0.1 -> 1.3.1 (*)
|    |    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    |    \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.integral:model:5.7 -> 5.9 (*)
|    +--- com.integral:clustering:1.0.1
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    |    +--- com.google.guava:guava:15.0 -> 29.0-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.11.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.4
|    |    |    \--- com.google.j2objc:j2objc-annotations:1.3
|    |    +--- commons-cli:commons-cli:1.2
|    |    +--- log4j:log4j:1.2.14 -> 1.2.17
|    |    +--- com.101tec:zkclient:0.1
|    |    +--- com.integral:util:1.0 -> 1.2 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    +--- org.apache.helix:helix-core:0.7.3
|    |    \--- org.apache.zookeeper:zookeeper:3.4.6 -> 3.5.3-beta
|    |         +--- org.slf4j:slf4j-api:1.7.5 -> 1.7.29
|    |         +--- org.slf4j:slf4j-log4j12:1.7.5 -> 1.7.29
|    |         +--- commons-cli:commons-cli:1.2
|    |         +--- log4j:log4j:1.2.17
|    |         \--- io.netty:netty:3.10.5.Final
|    +--- com.integral:multicast:9.9.9-SNAPSHOT
|    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.12
|    |    |    +--- commons-logging:commons-logging:1.2
|    |    |    \--- commons-codec:commons-codec:1.11 -> 1.13
|    |    +--- log4j:log4j:1.2.14 -> 1.2.17
|    |    +--- org.apache.httpcomponents:httpcore:4.1 -> 4.4.12
|    |    +--- com.integral:util:1.1 -> 1.2 (*)
|    |    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- junit:junit:4.11 -> 4.12 (*)
|    |    +--- com.integral:rds:2.3 -> 3.0-SNAPSHOT
|    |    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    |    +--- com.integral:model:4.2 -> 5.9 (*)
|    |    |    +--- com.integral:spaces:2.0 -> 2.3 (*)
|    |    |    +--- com.integral:messaging:1.1-SNAPSHOT (*)
|    |    |    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    |    |    +--- io.netty:netty-common:4.1.1.Final -> 4.1.43.Final
|    |    |    +--- io.netty:netty-buffer:4.1.1.Final -> 4.1.43.Final
|    |    |    |    \--- io.netty:netty-common:4.1.43.Final
|    |    |    +--- io.netty:netty-handler:4.1.1.Final -> 4.1.43.Final
|    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.43.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    |    \--- io.netty:netty-resolver:4.1.43.Final
|    |    |    |    |         \--- io.netty:netty-common:4.1.43.Final
|    |    |    |    \--- io.netty:netty-codec:4.1.43.Final
|    |    |    |         +--- io.netty:netty-common:4.1.43.Final
|    |    |    |         +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |         \--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    +--- io.netty:netty-codec-http:4.1.1.Final -> 4.1.43.Final
|    |    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    |    +--- io.netty:netty-codec:4.1.43.Final (*)
|    |    |    |    \--- io.netty:netty-handler:4.1.43.Final (*)
|    |    |    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    |    |    +--- commons-codec:commons-codec:1.5 -> 1.13
|    |    |    +--- org.eclipse.jetty.aggregate:jetty-all-server:8.1.15.v20140411
|    |    |    \--- io.micrometer:micrometer-registry-prometheus:1.5.2 -> 1.3.1
|    |    |         +--- io.micrometer:micrometer-core:1.3.1
|    |    |         |    +--- org.hdrhistogram:HdrHistogram:2.1.11
|    |    |         |    \--- org.latencyutils:LatencyUtils:2.0.3
|    |    |         \--- io.prometheus:simpleclient_common:0.7.0
|    |    |              \--- io.prometheus:simpleclient:0.7.0
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    \--- com.integral:model:2.5 -> 5.9 (*)
|    +--- com.integral:rds:2.3 -> 3.0-SNAPSHOT (*)
|    +--- com.integral:services:2.0
|    |    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |    +--- org.apache.curator:curator-framework:2.5.0 -> 4.0.1
|    |    |    \--- org.apache.curator:curator-client:4.0.1
|    |    |         +--- org.apache.zookeeper:zookeeper:3.5.3-beta (*)
|    |    |         +--- com.google.guava:guava:20.0 -> 29.0-jre (*)
|    |    |         \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.29
|    |    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    |    +--- org.apache.curator:curator-recipes:2.5.0 -> 4.0.1
|    |    |    \--- org.apache.curator:curator-framework:4.0.1 (*)
|    |    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.apache.curator:curator-client:2.5.0 -> 4.0.1 (*)
|    |    +--- org.apache.curator:curator-x-discovery:2.5.0 -> 4.0.1
|    |    |    +--- org.apache.curator:curator-recipes:4.0.1 (*)
|    |    |    \--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    |    +--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    |    +--- org.apache.zookeeper:zookeeper:3.4.6 -> 3.5.3-beta (*)
|    |    \--- com.integral:imtp:1.0.1
|    |         +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    |         +--- org.jboss.netty:netty:3.2.4.Final
|    |         +--- com.sleepycat:je:5.0.34
|    |         +--- commons-lang:commons-lang:2.2 -> 2.6
|    |         +--- com.integral:serializer:1.0.1 -> 1.3.1 (*)
|    |         \--- org.codehaus.jackson:jackson-core-asl:1.9.13
|    +--- com.googlecode.disruptor:disruptor:2.8
|    +--- org.drools:drools-compiler:6.0.1.Final
|    +--- org.drools:drools-core:6.0.1.Final
|    +--- org.drools:kie-internal:6.0.1.Final
|    +--- org.drools:kie-api:6.0.1.Final
|    +--- com.ibm:com.ibm.mq:7.0
|    +--- com.ibm:com.ibm.mq.pcf:7.0
|    +--- com.ibm:com.ibm.mqjms:7.0
|    +--- com.ibm:com.ibm.mq.headers:7.0
|    +--- com.ibm:com.ibm.mq.jmqi:7.0
|    +--- com.amazonaws:aws-java-sdk:1.3.26
|    +--- com.google.http-client:google-http-client:1.20.0 -> 1.37.0
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 -> 4.5.10 (*)
|    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.12
|    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    +--- com.google.guava:guava:29.0-android -> 29.0-jre (*)
|    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    +--- io.opencensus:opencensus-api:0.24.0
|    |    |    \--- io.grpc:grpc-context:1.22.1 -> 1.33.0
|    |    \--- io.opencensus:opencensus-contrib-http-util:0.24.0
|    |         +--- io.opencensus:opencensus-api:0.24.0 (*)
|    |         \--- com.google.guava:guava:26.0-android -> 29.0-jre (*)
|    +--- org.jctools:jctools-core:1.1
|    +--- org.codehaus.jackson:jackson-mapper-asl:1.9.13
|    +--- junit:junit:4.11 -> 4.12 (*)
|    +--- ch.qos.logback:logback-classic:1.0.6 -> 1.2.3 (*)
|    +--- ch.qos.logback:logback-core:1.0.6 -> 1.2.3
|    +--- ch.qos.logback:slf4j-api:1.6.1
|    +--- org.mongodb:mongo-java-driver:1.0.0
|    +--- mx4j:mx4j-tools:1.0.0
|    +--- cactus:cactus:1.5-rc1
|    +--- commons-jxpath:commons-jxpath:1.2
|    +--- commons-lang:commons-lang:2.2 -> 2.6
|    +--- commons-math:commons-math:1.1
|    +--- commons-codec:commons-codec:1.5 -> 1.13
|    +--- com.integral:TOPLink:1.0
|    +--- commons-httpclient:commons-httpclient:3.0-rc2
|    +--- org.apache.commons:commons-beanutils:1.6
|    +--- org.apache.commons:commons-collections:3.1
|    +--- commons-logging:commons-logging:1.0 -> 1.2
|    +--- com.integral:instantj:1.6
|    +--- log4j:log4j:1.2.14 -> 1.2.17
|    +--- commons-pool:commons-pool:1.5.5 -> 1.6
|    +--- castor:castor:0.9.3.9-xml
|    +--- com.ibm:ibm-bsf-regx:1.0
|    +--- com.integral:soap:1.0.0
|    +--- com.oracle:classes12:1.0.0
|    +--- exml:exml:7.0
|    +--- javax.mail:mail:1.0
|    +--- xmlunit:xmlunit:1.0
|    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    +--- com.thoughtworks.xstream:xstream:1.4.5
|    +--- com.sonicmq:sonic_client:7.6.2
|    +--- com.sonicmq:mgmt_client:7.6.2
|    +--- com.sonicmq:mgmt_config:7.6.2
|    +--- com.sonicmq:sonic_mgmt_client:7.6.2
|    +--- xerces:xercesImpl:1.0.0
|    +--- xerces:xml-apis:1.0.0
|    +--- xalan:xalan:1.0.0
|    +--- org.apache.tomcat:catalina:7.0.65
|    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10 (*)
|    +--- org.apache.httpcomponents:httpcore:4.1 -> 4.4.12
|    +--- org.apache.helix:helix-core:0.7.3
|    +--- com.101tec:zkclient:0.1
|    +--- com.rabbitmq:amqp-client:3.6.0
|    +--- quickfixj:quickfixj-all:1.4.0.1 -> 1.4.2
|    \--- org.codehaus.groovy:groovy-all:2.0.2
+--- com.rabbitmq:amqp-client:3.6.0
+--- org.mongodb:mongo-java-driver:1.0.0
+--- com.integral:monitor-core:3.0.3
|    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:serializer:1.3 -> 1.3.1 (*)
|    \--- com.integral:util:1.1 -> 1.2 (*)
+--- org.json:json:20080701
+--- com.integral:rds:3.0-SNAPSHOT (*)
+--- com.integral:log:2.3-SNAPSHOT (*)
+--- com.integral:services:2.0 (*)
+--- com.integral:unity-server:2.4
|    +--- com.integral:spaces:1.0.1 -> 2.3 (*)
|    +--- com.corundumstudio.socketio:netty-socketio:1.7.11
|    |    +--- io.netty:netty-buffer:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-common:4.1.1.Final -> 4.1.43.Final
|    |    +--- io.netty:netty-transport:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-handler:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-codec-http:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-codec:4.1.1.Final -> 4.1.43.Final (*)
|    |    +--- io.netty:netty-transport-native-epoll:4.1.1.Final -> 4.1.43.Final
|    |    |    +--- io.netty:netty-common:4.1.43.Final
|    |    |    +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |    +--- io.netty:netty-transport:4.1.43.Final (*)
|    |    |    \--- io.netty:netty-transport-native-unix-common:4.1.43.Final
|    |    |         +--- io.netty:netty-common:4.1.43.Final
|    |    |         +--- io.netty:netty-buffer:4.1.43.Final (*)
|    |    |         \--- io.netty:netty-transport:4.1.43.Final (*)
|    |    +--- org.slf4j:slf4j-api:1.7.21 -> 1.7.29
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.7.4 -> 2.10.2
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.7.4 -> 2.10.2 (*)
|    +--- commons-codec:commons-codec:1.5 -> 1.13
|    +--- com.integral:log:1.0.2 -> 2.3-SNAPSHOT (*)
|    +--- com.integral:model:1.0.1 -> 5.9 (*)
|    +--- com.google.guava:guava:15.0 -> 29.0-jre (*)
|    +--- com.integral:util:1.0 -> 1.2 (*)
|    +--- com.integral:messaging:1.0.1 -> 1.1-SNAPSHOT (*)
|    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
+--- com.integral:riskmanagement-common:2.5
|    +--- com.integral:darwin-common:2.5 -> 3.15.0
|    |    +--- org.springframework:spring-core:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-beans:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-context:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-webmvc:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- com.integral:util:1.2 (*)
|    |    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:1.2 -> 5.9 (*)
|    |    +--- com.integral:rds:2.1 -> 3.0-SNAPSHOT (*)
|    |    +--- commons-collections:commons-collections:3.2.2
|    |    +--- com.integral:oracle-persistence:1.2.83.2 -> 9.9.9-SNAPSHOT (*)
|    |    \--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    +--- com.integral:TOPLink:1.0
|    +--- com.integral:services:2.0 (*)
|    +--- quickfixj:quickfixj-all:1.4.0 -> 1.4.2
|    +--- com.integral:yieldmanager-api:2.6.2
|    |    +--- com.integral:darwin-common:2.5 -> 3.15.0 (*)
|    |    +--- com.integral:services:2.0 (*)
|    |    +--- javax.validation:validation-api:1.0.0.GA -> 2.0.1.Final
|    |    \--- com.integral:model:2.6 -> 5.9 (*)
|    \--- com.integral:multicast:1.0 -> 9.9.9-SNAPSHOT (*)
+--- com.integral:darwin-auth:3.15.0
|    +--- com.integral:darwin-common:3.15.0 (*)
|    +--- com.integral:darwin-taggable-entities:3.15.0 -> 3.13.2-jdk8
|    |    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT (*)
|    |    +--- com.integral:model:2.7 -> 5.9 (*)
|    |    +--- com.integral:darwin-common:3.13.2-jdk8 -> 3.15.0 (*)
|    |    +--- com.integral:rds:2.4 -> 3.0-SNAPSHOT (*)
|    |    +--- org.springframework:spring-core:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-beans:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-context:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-webmvc:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    |    \--- com.integral:oracle-persistence:1.2.83.2 -> 9.9.9-SNAPSHOT (*)
|    +--- com.integral:model:4.9 -> 5.9 (*)
|    +--- com.integral:rds:2.1 -> 3.0-SNAPSHOT (*)
|    +--- com.integral:log:1.2 -> 2.3-SNAPSHOT (*)
|    +--- io.jsonwebtoken:jjwt:0.6.0 -> 0.9.1
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.6 -> 2.10.2 (*)
|    \--- com.integral:oracle-persistence:1.2.83.2 -> 9.9.9-SNAPSHOT (*)
+--- com.integral:model:5.9 (*)
+--- struts:struts:1.0.0
+--- commons-io:commons-io:1.1
+--- org.restlet.jee:org.restlet:1.0.0
+--- org.restlet.jee:org.restlet.ext.json:1.0.0
+--- com.fasterxml.jackson.core:jackson-databind:2.10.2 (*)
+--- com.fasterxml.jackson.core:jackson-core:2.10.2
+--- org.hibernate:hibernate-validator:5.3.1.Final
|    +--- javax.validation:validation-api:1.1.0.Final -> 2.0.1.Final
|    +--- org.jboss.logging:jboss-logging:3.3.0.Final -> 3.4.1.Final
|    \--- com.fasterxml:classmate:1.3.1 -> 1.5.1
+--- com.ning:async-http-client:1.6.2
+--- aopalliance:aopalliance:1.0
+--- aspectj:aspectjweaver:1.6.12
+--- commons-fileupload:commons-fileupload:1.1.1
+--- joda-time:joda-time:2.2
+--- org.owasp.esapi:esapi:2.0.1
+--- org.eclipse.jetty:jetty-server:9.4.28.v20200408
|    +--- javax.servlet:javax.servlet-api:3.1.0 -> 4.0.1
|    +--- org.eclipse.jetty:jetty-http:9.4.28.v20200408 -> 9.4.24.v20191120
|    |    +--- org.eclipse.jetty:jetty-util:9.4.24.v20191120
|    |    \--- org.eclipse.jetty:jetty-io:9.4.24.v20191120
|    |         \--- org.eclipse.jetty:jetty-util:9.4.24.v20191120
|    \--- org.eclipse.jetty:jetty-io:9.4.28.v20200408 -> 9.4.24.v20191120 (*)
+--- quickfixj:quickfixj-all:1.4.2
+--- org.apache.mina:mina-core:1.1.7
+--- org.apache.logging.log4j:log4j-api:2.16.0
+--- com.hazelcast:hazelcast:5.1.2
+--- org.mapstruct:mapstruct:1.4.1.Final
+--- org.mapstruct:mapstruct-jdk8:1.4.1.Final
+--- io.jsonwebtoken:jjwt:0.9.1 (*)
+--- org.springframework.boot:spring-boot-starter-web -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:2.2.2.RELEASE
|    |    |    +--- ch.qos.logback:logback-classic:1.2.3 (*)
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.12.1 -> 2.16.0
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    \--- org.apache.logging.log4j:log4j-api:2.16.0
|    |    |    \--- org.slf4j:jul-to-slf4j:1.7.29
|    |    |         \--- org.slf4j:slf4j-api:1.7.29
|    |    +--- jakarta.annotation:jakarta.annotation-api:1.3.5
|    |    +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    \--- org.yaml:snakeyaml:1.25
|    +--- org.springframework.boot:spring-boot-starter-json:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.10.1
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.10.1
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.10.1 -> 2.10.2
|    |         \--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:2.2.2.RELEASE
|    |    +--- jakarta.annotation:jakarta.annotation-api:1.3.5
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:9.0.29
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.29
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:9.0.29
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:9.0.29
|    +--- org.springframework.boot:spring-boot-starter-validation:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    +--- jakarta.validation:jakarta.validation-api:2.0.1
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:9.0.29
|    |    \--- org.hibernate.validator:hibernate-validator:6.0.18.Final
|    |         +--- org.jboss.logging:jboss-logging:3.3.2.Final -> 3.4.1.Final
|    |         \--- com.fasterxml:classmate:1.3.4 -> 1.5.1
|    +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|    \--- org.springframework:spring-webmvc:5.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-tomcat -> 2.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-websocket -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter-web:2.2.2.RELEASE (*)
|    +--- org.springframework:spring-messaging:5.2.2.RELEASE
|    |    +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    \--- org.springframework:spring-websocket:5.2.2.RELEASE
|         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|         \--- org.springframework:spring-web:5.2.2.RELEASE (*)
+--- org.springframework.boot:spring-boot-starter-actuator -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-actuator:2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    |    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.10.1 -> 2.10.2 (*)
|    |    +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.10.1 (*)
|    \--- io.micrometer:micrometer-core:1.3.1 (*)
+--- io.micrometer:micrometer-core -> 1.3.1 (*)
+--- io.micrometer:micrometer-registry-prometheus -> 1.3.1 (*)
+--- org.springframework.cloud:spring-cloud-starter-consul-discovery -> 2.2.6.RELEASE
|    +--- org.springframework.cloud:spring-cloud-starter-consul:2.2.6.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:2.2.7.RELEASE
|    |    |    |    \--- org.springframework.security:spring-security-crypto:5.3.6.RELEASE -> 5.2.1.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:2.2.7.RELEASE
|    |    |    |    \--- org.springframework.security:spring-security-crypto:5.3.6.RELEASE -> 5.2.1.RELEASE
|    |    |    \--- org.springframework.security:spring-security-rsa:1.0.9.RELEASE
|    |    |         \--- org.bouncycastle:bcpkix-jdk15on:1.64
|    |    |              \--- org.bouncycastle:bcprov-jdk15on:1.64
|    |    +--- org.springframework.cloud:spring-cloud-consul-core:2.2.6.RELEASE
|    |    |    \--- org.springframework.boot:spring-boot-starter-validation:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    +--- com.ecwid.consul:consul-api:1.4.5
|    |    |    +--- com.google.code.gson:gson:2.8.2 -> 2.8.6
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.9 -> 4.4.12
|    |    |    \--- org.apache.httpcomponents:httpclient:4.5.5 -> 4.5.10 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 -> 4.5.10 (*)
|    |    \--- org.apache.httpcomponents:httpcore:4.4.14 -> 4.4.12
|    +--- org.springframework.cloud:spring-cloud-consul-discovery:2.2.6.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-consul-core:2.2.6.RELEASE (*)
|    |    \--- commons-configuration:commons-configuration:1.8
|    |         +--- commons-lang:commons-lang:2.6
|    |         \--- commons-logging:commons-logging:1.1.1 -> 1.2
|    +--- org.springframework.cloud:spring-cloud-netflix-hystrix:2.2.7.RELEASE
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-starter-aop:2.3.8.RELEASE -> 2.2.2.RELEASE
|    |         +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|    |         \--- org.aspectj:aspectjweaver:1.9.5
|    +--- org.springframework.cloud:spring-cloud-starter-netflix-ribbon:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.7.RELEASE
|    |    |    \--- org.springframework.cloud:spring-cloud-netflix-archaius:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter-netflix-archaius:2.2.7.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-netflix-ribbon:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-netflix-archaius:2.2.7.RELEASE
|    |    |    +--- com.netflix.archaius:archaius-core:0.7.7
|    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|    |    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.29
|    |    |    |    +--- com.google.guava:guava:16.0 -> 29.0-jre (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.4.3 -> 2.10.1
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.4.3 -> 2.10.2
|    |    |    |    \--- com.fasterxml.jackson.core:jackson-databind:2.4.3 -> 2.10.2 (*)
|    |    |    \--- commons-configuration:commons-configuration:1.8 (*)
|    |    +--- com.netflix.ribbon:ribbon:2.3.0
|    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0
|    |    |    |    +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.29
|    |    |    |    +--- com.google.guava:guava:16.0 -> 29.0-jre (*)
|    |    |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|    |    |    |    +--- commons-lang:commons-lang:2.6
|    |    |    |    \--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    |    +--- com.netflix.ribbon:ribbon-transport:2.3.0
|    |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|    |    |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0
|    |    |    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|    |    |    |    |    +--- com.netflix.netflix-commons:netflix-statistics:0.1.1
|    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.6.4 -> 1.7.29
|    |    |    |    |    +--- io.reactivex:rxjava:1.0.9 -> 1.3.8
|    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.29
|    |    |    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21
|    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|    |    |    |    |    |    \--- com.google.guava:guava:19.0 -> 29.0-jre (*)
|    |    |    |    |    +--- com.google.guava:guava:16.0.1 -> 29.0-jre (*)
|    |    |    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    |    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0
|    |    |    |    |         +--- org.slf4j:slf4j-api:1.6.4 -> 1.7.29
|    |    |    |    |         \--- javax.inject:javax.inject:1
|    |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    |    +--- io.reactivex:rxnetty:0.4.9
|    |    |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.6 -> 1.7.29
|    |    |    |    +--- io.reactivex:rxnetty-contexts:0.4.9
|    |    |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    |    |    \--- io.reactivex:rxnetty:0.4.9 (*)
|    |    |    |    +--- io.reactivex:rxnetty-servo:0.4.9
|    |    |    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|    |    |    |    |    \--- com.netflix.servo:servo-core:0.7.5 -> 0.12.21 (*)
|    |    |    |    +--- javax.inject:javax.inject:1
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.29
|    |    |    |    +--- com.google.guava:guava:16.0.1 -> 29.0-jre (*)
|    |    |    |    \--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    |    +--- com.netflix.hystrix:hystrix-core:1.4.3 -> 1.5.18
|    |    |    |    +--- org.slf4j:slf4j-api:1.7.0 -> 1.7.29
|    |    |    |    +--- com.netflix.archaius:archaius-core:0.4.1 -> 0.7.7 (*)
|    |    |    |    +--- io.reactivex:rxjava:1.2.0 -> 1.3.8
|    |    |    |    \--- org.hdrhistogram:HdrHistogram:2.1.9 -> 2.1.11
|    |    |    +--- javax.inject:javax.inject:1
|    |    |    +--- io.reactivex:rxjava:1.0.10 -> 1.3.8
|    |    |    +--- io.reactivex:rxnetty:0.4.9 (*)
|    |    |    +--- commons-configuration:commons-configuration:1.8 (*)
|    |    |    +--- com.google.guava:guava:16.0.1 -> 29.0-jre (*)
|    |    |    \--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|    |    +--- com.netflix.ribbon:ribbon-httpclient:2.3.0
|    |    |    +--- com.netflix.ribbon:ribbon-core:2.3.0 (*)
|    |    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|    |    |    +--- commons-collections:commons-collections:3.2.2
|    |    |    +--- org.apache.httpcomponents:httpclient:4.2.1 -> 4.5.10 (*)
|    |    |    +--- com.sun.jersey:jersey-client:1.19.1
|    |    |    |    \--- com.sun.jersey:jersey-core:1.19.1
|    |    |    |         \--- javax.ws.rs:jsr311-api:1.1.1
|    |    |    +--- com.sun.jersey.contribs:jersey-apache-client4:1.19.1
|    |    |    |    +--- org.apache.httpcomponents:httpclient:4.1.1 -> 4.5.10 (*)
|    |    |    |    \--- com.sun.jersey:jersey-client:1.19.1 (*)
|    |    |    +--- org.slf4j:slf4j-api:1.7.12 -> 1.7.29
|    |    |    +--- com.netflix.servo:servo-core:0.10.1 -> 0.12.21 (*)
|    |    |    +--- com.google.guava:guava:16.0.1 -> 29.0-jre (*)
|    |    |    +--- com.netflix.archaius:archaius-core:0.7.6 -> 0.7.7 (*)
|    |    |    \--- com.netflix.netflix-commons:netflix-commons-util:0.1.1 -> 0.3.0 (*)
|    |    +--- com.netflix.ribbon:ribbon-loadbalancer:2.3.0 (*)
|    |    \--- io.reactivex:rxjava:1.3.8
|    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer:2.2.7.RELEASE
|    |    +--- org.springframework.cloud:spring-cloud-starter:2.2.7.RELEASE (*)
|    |    +--- org.springframework.cloud:spring-cloud-loadbalancer:2.2.7.RELEASE
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:2.2.7.RELEASE (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-validation:2.3.8.RELEASE -> 2.2.2.RELEASE (*)
|    |    |    +--- io.projectreactor:reactor-core:3.3.13.RELEASE -> 3.3.1.RELEASE
|    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.3
|    |    |    \--- io.projectreactor.addons:reactor-extra:3.3.5.RELEASE -> 3.3.1.RELEASE
|    |    |         \--- io.projectreactor:reactor-core:3.3.1.RELEASE (*)
|    |    +--- org.springframework.boot:spring-boot-starter-cache:2.3.8.RELEASE -> 2.2.2.RELEASE
|    |    |    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    |    |    \--- org.springframework:spring-context-support:5.2.2.RELEASE
|    |    |         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|    |    |         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|    |    |         \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    |    \--- com.stoyanr:evictor:1.0.0
|    \--- joda-time:joda-time:2.10.5 -> 2.2
+--- org.springframework.boot:spring-boot-starter-hateoas -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter-web:2.2.2.RELEASE (*)
|    \--- org.springframework.hateoas:spring-hateoas:1.0.2.RELEASE
|         +--- org.springframework:spring-aop:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-beans:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-context:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|         +--- org.springframework:spring-web:5.2.2.RELEASE (*)
|         +--- org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE
|         |    +--- org.springframework:spring-beans:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    +--- org.springframework:spring-context:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    +--- org.springframework:spring-aop:5.2.0.RELEASE -> 5.2.2.RELEASE (*)
|         |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|         +--- com.jayway.jsonpath:json-path:2.4.0
|         |    +--- net.minidev:json-smart:2.3
|         |    |    \--- net.minidev:accessors-smart:1.2
|         |    |         \--- org.ow2.asm:asm:5.0.4
|         |    \--- org.slf4j:slf4j-api:1.7.25 -> 1.7.29
|         \--- org.slf4j:slf4j-api:1.7.26 -> 1.7.29
+--- org.springframework.boot:spring-boot-starter-validation -> 2.2.2.RELEASE (*)
+--- com.integral:portal-service-apps:3.13.2-jdk8
|    +--- com.integral:darwin-common:3.13.2-jdk8 -> 3.15.0 (*)
|    +--- com.integral:darwin-taggable-entities:3.13.2-jdk8 (*)
|    +--- com.integral:model:5.9 (*)
|    +--- com.integral:rds:2.1 -> 3.0-SNAPSHOT (*)
|    +--- com.integral:oracle-persistence:1.2.83.2 -> 9.9.9-SNAPSHOT (*)
|    +--- org.springframework:spring-core:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- org.springframework:spring-context:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- org.springframework:spring-web:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- org.springframework:spring-webmvc:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- org.springframework:spring-beans:3.1.1.RELEASE -> 5.2.2.RELEASE (*)
|    +--- com.google.code.gson:gson:2.2.4 -> 2.8.6
|    +--- org.apache.commons:commons-csv:1.4
|    \--- org.apache.httpcomponents:httpclient:4.4.1 -> 4.5.10 (*)
+--- com.integral:price-making-commons:9.9.9-jdk8-SNAPSHOT
|    \--- com.integral:oracle-persistence:9.9.9-SNAPSHOT (*)
+--- com.integral:darwin-taggable-entities:3.13.2-jdk8 (*)
+--- com.google.code.gson:gson:2.8.6
+--- org.apache.logging.log4j:log4j-to-slf4j:2.16.0 (*)
+--- net.sf.kxml:kxml2:2.3.0
+--- org.springframework.boot:spring-boot-starter-test -> 2.2.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:2.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test:2.2.2.RELEASE
|    |    \--- org.springframework.boot:spring-boot:2.2.2.RELEASE (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:2.2.2.RELEASE
|    |    +--- org.springframework.boot:spring-boot-test:2.2.2.RELEASE (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:2.2.2.RELEASE (*)
|    +--- com.jayway.jsonpath:json-path:2.4.0 (*)
|    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    +--- org.junit.jupiter:junit-jupiter:5.5.2
|    |    +--- org.junit.jupiter:junit-jupiter-api:5.5.2
|    |    |    +--- org.apiguardian:apiguardian-api:1.1.0
|    |    |    +--- org.opentest4j:opentest4j:1.2.0
|    |    |    \--- org.junit.platform:junit-platform-commons:1.5.2
|    |    |         \--- org.apiguardian:apiguardian-api:1.1.0
|    |    +--- org.junit.jupiter:junit-jupiter-params:5.5.2
|    |    |    +--- org.apiguardian:apiguardian-api:1.1.0
|    |    |    \--- org.junit.jupiter:junit-jupiter-api:5.5.2 (*)
|    |    \--- org.junit.jupiter:junit-jupiter-engine:5.5.2
|    |         +--- org.apiguardian:apiguardian-api:1.1.0
|    |         +--- org.junit.platform:junit-platform-engine:1.5.2
|    |         |    +--- org.apiguardian:apiguardian-api:1.1.0
|    |         |    +--- org.opentest4j:opentest4j:1.2.0
|    |         |    \--- org.junit.platform:junit-platform-commons:1.5.2 (*)
|    |         \--- org.junit.jupiter:junit-jupiter-api:5.5.2 (*)
|    +--- org.mockito:mockito-junit-jupiter:3.1.0
|    |    +--- org.mockito:mockito-core:3.1.0
|    |    |    +--- net.bytebuddy:byte-buddy:1.9.10 -> 1.10.4
|    |    |    +--- net.bytebuddy:byte-buddy-agent:1.9.10 -> 1.10.4
|    |    |    \--- org.objenesis:objenesis:2.6
|    |    \--- org.junit.jupiter:junit-jupiter-api:5.4.2 -> 5.5.2 (*)
|    +--- org.assertj:assertj-core:3.13.2
|    +--- org.hamcrest:hamcrest:2.1
|    +--- org.mockito:mockito-core:3.1.0 (*)
|    +--- org.skyscreamer:jsonassert:1.5.0
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    +--- org.springframework:spring-test:5.2.2.RELEASE
|    |    \--- org.springframework:spring-core:5.2.2.RELEASE (*)
|    \--- org.xmlunit:xmlunit-core:2.6.3
\--- com.squareup.okhttp3:okhttp:4.10.0
     +--- com.squareup.okio:okio:3.0.0
     |    \--- com.squareup.okio:okio-jvm:3.0.0
     |         +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.31 -> 1.3.61
     |         |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.61
     |         |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.3.61
     |         |    |    \--- org.jetbrains:annotations:13.0
     |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.61
     |         |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.61 (*)
     |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.5.31 -> 1.3.61
     \--- org.jetbrains.kotlin:kotlin-stdlib:1.6.20 -> 1.3.61 (*)

testRuntimeOnly - Runtime only dependencies for source set 'test'. (n)
No dependencies

(*) - dependencies omitted (listed previously)

(n) - Not resolved (configuration is not meant to be resolved)

A web-based, searchable dependency report is available by adding the --scan option.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 7.0.
Use '--warning-mode all' to show the individual deprecation warnings.
See https://docs.gradle.org/6.3/userguide/command_line_interface.html#sec:command_line_warnings

BUILD SUCCESSFUL in 1s
1 actionable task: 1 executed
